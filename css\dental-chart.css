/* مخطط الأسنان التفاعلي */
.dental-chart-container {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
    margin-bottom: 20px;
}

.dental-chart-header {
    text-align: center;
    margin-bottom: 20px;
}

.dental-chart-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 10px;
}

.dental-chart-info {
    color: var(--text-light);
    font-size: 0.9rem;
}

.dental-chart {
    display: flex;
    flex-direction: column;
    gap: 30px;
    max-width: 800px;
    margin: 0 auto;
}

.jaw-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.jaw-label {
    text-align: center;
    font-weight: 600;
    color: var(--text-dark);
    font-size: 1.1rem;
}

.teeth-row {
    display: flex;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
}

.tooth {
    width: 45px;
    height: 60px;
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 8px 8px 15px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    padding: 5px 2px;
    position: relative;
    user-select: none;
}

.tooth:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    border-color: var(--primary-color);
}

.tooth.selected {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(44, 90, 160, 0.3);
}

.tooth.selected:hover {
    background: #1e3d6f;
    border-color: #1e3d6f;
}

.tooth-number {
    font-size: 0.8rem;
    font-weight: 600;
    line-height: 1;
}

.tooth-type {
    font-size: 0.7rem;
    text-align: center;
    line-height: 1;
    opacity: 0.8;
}

/* أنواع الأسنان المختلفة */
.tooth.incisor {
    border-radius: 8px 8px 12px 12px;
}

.tooth.canine {
    border-radius: 8px 8px 20px 20px;
}

.tooth.premolar {
    border-radius: 8px 8px 15px 15px;
}

.tooth.molar {
    border-radius: 8px 8px 18px 18px;
    width: 50px;
}

/* حالات الأسنان */
.tooth.missing {
    background: #e74c3c;
    border-color: #c0392b;
    color: var(--white);
}

.tooth.filled {
    background: #f39c12;
    border-color: #d68910;
    color: var(--white);
}

.tooth.crown {
    background: #9b59b6;
    border-color: #8e44ad;
    color: var(--white);
}

.tooth.implant {
    background: #34495e;
    border-color: #2c3e50;
    color: var(--white);
}

/* أدوات التحكم في المخطط */
.dental-chart-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.chart-control-btn {
    padding: 8px 16px;
    border: 2px solid var(--border-color);
    background: var(--white);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.chart-control-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.chart-control-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

/* معلومات الأسنان المحددة */
.selected-teeth-info {
    background: var(--light-bg);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-top: 20px;
    border: 2px solid var(--border-color);
}

.selected-teeth-header {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.selected-teeth-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.selected-tooth-tag {
    background: var(--primary-color);
    color: var(--white);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.selected-tooth-tag .remove-tooth {
    cursor: pointer;
    opacity: 0.8;
    transition: var(--transition);
}

.selected-tooth-tag .remove-tooth:hover {
    opacity: 1;
    transform: scale(1.2);
}

.teeth-count {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-top: 10px;
}

/* أنماط خاصة للطباعة */
@media print {
    .dental-chart-controls {
        display: none;
    }
    
    .tooth {
        border-width: 1px;
        box-shadow: none;
    }
    
    .tooth:hover {
        transform: none;
        box-shadow: none;
    }
}

/* مخطط الأسنان المبسط */
.simple-dental-chart {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin: 20px 0;
}

.simple-dental-chart .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.simple-dental-chart .chart-tools {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.simple-dental-chart .tool-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.simple-dental-chart .tool-btn:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.simple-dental-chart .selected-count {
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 6px;
    font-weight: 500;
    color: #495057;
    border: 1px solid #dee2e6;
    white-space: nowrap;
}

.simple-dental-chart .dental-grid {
    display: flex;
    flex-direction: column;
    gap: 30px;
    margin: 20px 0;
}

.simple-dental-chart .jaw {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #e9ecef;
}

.simple-dental-chart .jaw-label {
    text-align: center;
    font-weight: 600;
    font-size: 1.1rem;
    color: #495057;
    margin-bottom: 15px;
    padding: 10px;
    background: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.simple-dental-chart .upper-jaw {
    border-color: #17a2b8;
}

.simple-dental-chart .lower-jaw {
    border-color: #28a745;
}

.simple-dental-chart .upper-jaw .jaw-label {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.simple-dental-chart .lower-jaw .jaw-label {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.simple-dental-chart .teeth-row {
    display: flex;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
}

.simple-dental-chart .tooth {
    width: 45px;
    height: 45px;
    background: white;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    font-weight: 600;
    color: #495057;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.simple-dental-chart .tooth:hover {
    border-color: #007bff;
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.simple-dental-chart .tooth.selected {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-color: #0056b3;
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

.simple-dental-chart .tooth.selected:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
}

.simple-dental-chart .tooth-number {
    font-size: 0.9rem;
    font-weight: 600;
}

.simple-dental-chart .selected-teeth-display {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.simple-dental-chart .teeth-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    min-height: 40px;
    align-items: center;
}

.simple-dental-chart .tooth-tag {
    background: #007bff;
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    animation: fadeIn 0.3s ease;
}

.simple-dental-chart .tooth-tag button {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 2px;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
}

.simple-dental-chart .tooth-tag button:hover {
    background: rgba(255,255,255,0.2);
}

.simple-dental-chart .no-selection {
    color: #6c757d;
    font-style: italic;
    padding: 10px;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* تحسينات للشاشات الصغيرة للمخطط المبسط */
@media (max-width: 768px) {
    .simple-dental-chart {
        padding: 15px;
        margin: 15px 0;
    }

    .simple-dental-chart .chart-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .simple-dental-chart .chart-tools {
        justify-content: center;
        flex-wrap: wrap;
    }

    .simple-dental-chart .tooth {
        width: 40px;
        height: 40px;
    }

    .simple-dental-chart .teeth-row {
        gap: 6px;
    }

    .simple-dental-chart .jaw {
        padding: 15px;
    }

    .simple-dental-chart .selected-count {
        text-align: center;
    }
}

/* التصميم المتجاوب للمخطط */
@media (max-width: 768px) {
    .dental-chart-container {
        padding: 15px;
    }
    
    .tooth {
        width: 35px;
        height: 50px;
        gap: 5px;
    }
    
    .tooth.molar {
        width: 40px;
    }
    
    .tooth-number {
        font-size: 0.7rem;
    }
    
    .tooth-type {
        font-size: 0.6rem;
    }
    
    .teeth-row {
        gap: 5px;
    }
    
    .dental-chart-controls {
        gap: 10px;
    }
    
    .chart-control-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .tooth {
        width: 30px;
        height: 45px;
    }
    
    .tooth.molar {
        width: 35px;
    }
    
    .teeth-row {
        gap: 3px;
    }
    
    .dental-chart-controls {
        flex-direction: column;
        align-items: center;
    }
    
    .chart-control-btn {
        width: 100%;
        max-width: 200px;
        justify-content: center;
    }
}

/* تأثيرات الحركة */
@keyframes toothSelect {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.tooth.selecting {
    animation: toothSelect 0.3s ease;
}

/* أنماط خاصة لأنواع التركيبات */
.tooth.porcelain {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-color: #6c757d;
    color: var(--text-dark);
}

.tooth.zircon {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-color: #2196f3;
    color: #1976d2;
}

.tooth.metal {
    background: linear-gradient(135deg, #cfd8dc, #b0bec5);
    border-color: #607d8b;
    color: #37474f;
}

.tooth.removable {
    background: linear-gradient(135deg, #fff3e0, #ffe0b2);
    border-color: #ff9800;
    color: #f57c00;
}

.tooth.orthodontic {
    background: linear-gradient(135deg, #f3e5f5, #e1bee7);
    border-color: #9c27b0;
    color: #7b1fa2;
}

/* مؤشر نوع التركيبة */
.prosthetic-indicator {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    font-size: 0.6rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    border: 2px solid var(--white);
}

.prosthetic-indicator.porcelain {
    background: #6c757d;
    color: var(--white);
}

.prosthetic-indicator.zircon {
    background: #2196f3;
    color: var(--white);
}

.prosthetic-indicator.metal {
    background: #607d8b;
    color: var(--white);
}

.prosthetic-indicator.removable {
    background: #ff9800;
    color: var(--white);
}

.prosthetic-indicator.orthodontic {
    background: #9c27b0;
    color: var(--white);
}

/* أسطورة المخطط */
.dental-chart-legend {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 20px;
    padding: 15px;
    background: var(--light-bg);
    border-radius: var(--border-radius);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 2px solid var(--border-color);
}

.legend-color.selected {
    background: var(--primary-color);
}

.legend-color.porcelain {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
}

.legend-color.zircon {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
}

.legend-color.metal {
    background: linear-gradient(135deg, #cfd8dc, #b0bec5);
}

.legend-color.removable {
    background: linear-gradient(135deg, #fff3e0, #ffe0b2);
}

.legend-color.orthodontic {
    background: linear-gradient(135deg, #f3e5f5, #e1bee7);
}
