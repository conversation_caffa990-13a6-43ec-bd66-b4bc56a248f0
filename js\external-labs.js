// إدارة المعامل الخارجية
class ExternalLabs {
    static currentLabs = [];
    static filteredLabs = [];
    static currentPage = 1;
    static pageSize = 10;
    static searchQuery = '';

    // عرض صفحة المعامل الخارجية
    static render() {
        const pageContent = document.getElementById('pageContent');
        
        pageContent.innerHTML = `
            <div class="external-labs-container">
                <!-- عنوان الإدارة المطور -->
                <div class="department-header inventory">
                    <div class="department-header-content">
                        <div class="department-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="department-info">
                            <h1 class="department-name" data-text="المعامل الخارجية">المعامل الخارجية</h1>
                            <p class="department-description">إدارة شاملة للمعامل الخارجية والأعمال المرسلة إليها</p>

                            <!-- 📊 الإحصائيات تحت العنوان مباشرة -->
                            <div class="header-stats">
                                <div class="header-stat-item">
                                    <div class="header-stat-icon"><i class="fas fa-building"></i></div>
                                    <div class="header-stat-content">
                                        <span class="header-stat-number" id="header-total-labs">0</span>
                                        <span class="header-stat-label">إجمالي المعامل</span>
                                    </div>
                                </div>
                                <div class="header-stat-item">
                                    <div class="header-stat-icon"><i class="fas fa-briefcase"></i></div>
                                    <div class="header-stat-content">
                                        <span class="header-stat-number" id="header-total-works">0</span>
                                        <span class="header-stat-label">إجمالي الأعمال</span>
                                    </div>
                                </div>
                                <div class="header-stat-item">
                                    <div class="header-stat-icon"><i class="fas fa-money-bill-wave"></i></div>
                                    <div class="header-stat-content">
                                        <span class="header-stat-number" id="header-total-cost">0</span>
                                        <span class="header-stat-label">إجمالي التكلفة</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="page-actions-container">
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="ExternalLabs.showAddModal()">
                            <i class="fas fa-plus"></i>
                            إضافة معمل جديد
                        </button>
                        <button class="btn btn-warning" onclick="ExternalLabs.showAddWorkModal()">
                            <i class="fas fa-briefcase"></i>
                            إضافة عمل جديد
                        </button>
                        <button class="btn btn-success" onclick="ExternalLabs.exportData()">
                            <i class="fas fa-download"></i>
                            تصدير البيانات
                        </button>
                    </div>
                </div>

                <!-- أدوات البحث -->
                <div class="filters-container">
                    <div class="search-box">
                        <input type="search" id="labs-search" placeholder="البحث في المعامل..." 
                               value="${this.searchQuery}">
                        <i class="fas fa-search"></i>
                    </div>
                    
                    <button class="btn btn-secondary" onclick="ExternalLabs.resetFilters()">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </button>
                </div>

                <!-- جدول المعامل -->
                <div class="table-container">
                    <div class="table-header">
                        <h3 class="table-title">قائمة المعامل الخارجية</h3>
                        <div class="table-info">
                            <span id="labs-count">0 معمل</span>
                        </div>
                    </div>
                    
                    <div class="table-wrapper">
                        <table class="data-table" id="labs-table">
                            <thead>
                                <tr>
                                    <th>اسم المعمل</th>
                                    <th>جهة الاتصال</th>
                                    <th>رقم الهاتف</th>
                                    <th>العنوان</th>
                                    <th>عدد الأعمال</th>
                                    <th>إجمالي التكلفة</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="labs-tbody">
                                <!-- سيتم تحميل البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="table-pagination" id="labs-pagination">
                        <!-- سيتم تحميل التصفح هنا -->
                    </div>
                </div>

                <!-- جدول الأعمال -->
                <div class="table-container">
                    <div class="table-header">
                        <h3 class="table-title">الأعمال المرسلة للمعامل الخارجية</h3>
                        <div class="table-info">
                            <span id="works-count">0 عمل</span>
                        </div>
                    </div>
                    
                    <div class="table-wrapper">
                        <table class="data-table" id="works-table">
                            <thead>
                                <tr>
                                    <th>رقم العمل</th>
                                    <th>المعمل</th>
                                    <th>نوع العمل</th>
                                    <th>التكلفة</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإرسال</th>
                                    <th>تاريخ الاستلام</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="works-tbody">
                                <!-- سيتم تحميل البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        this.loadData();
        this.setupEventListeners();
    }

    // تحميل البيانات
    static loadData() {
        this.currentLabs = Database.getItem('externalLabs') || [];
        this.applyFilters();
        this.updateTable();
        this.updateWorksTable();
        this.updateStats();
    }

    // إعداد مستمعي الأحداث
    static setupEventListeners() {
        // البحث
        const searchInput = document.getElementById('labs-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchQuery = e.target.value;
                this.currentPage = 1;
                this.applyFilters();
                this.updateTable();
            });
        }
    }

    // تطبيق الفلاتر
    static applyFilters() {
        let filtered = [...this.currentLabs];

        // فلتر البحث
        if (this.searchQuery) {
            const query = this.searchQuery.toLowerCase();
            filtered = filtered.filter(lab => 
                lab.name.toLowerCase().includes(query) ||
                lab.contactPerson?.toLowerCase().includes(query) ||
                lab.phone?.toLowerCase().includes(query) ||
                lab.address?.toLowerCase().includes(query)
            );
        }

        this.filteredLabs = filtered;
    }

    // تحديث جدول المعامل
    static updateTable() {
        const tbody = document.getElementById('labs-tbody');
        if (!tbody) return;

        // حساب البيانات للصفحة الحالية
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageData = this.filteredLabs.slice(startIndex, endIndex);

        // حساب إحصائيات كل معمل
        const works = Database.getItem('externalWorks') || [];
        const labStats = {};
        works.forEach(work => {
            if (!labStats[work.labId]) {
                labStats[work.labId] = { count: 0, totalCost: 0 };
            }
            labStats[work.labId].count++;
            labStats[work.labId].totalCost += work.cost || 0;
        });

        // إنشاء صفوف الجدول
        tbody.innerHTML = pageData.map(lab => {
            const stats = labStats[lab.id] || { count: 0, totalCost: 0 };
            return `
                <tr onclick="ExternalLabs.viewDetails('${lab.id}')" class="table-row-clickable">
                    <td>
                        <strong>${lab.name}</strong>
                        ${lab.specialization ? `<div class="lab-specialization">${lab.specialization}</div>` : ''}
                    </td>
                    <td>${lab.contactPerson || '-'}</td>
                    <td>
                        ${lab.phone ? `
                            <a href="tel:${lab.phone}" class="phone-link">
                                <i class="fas fa-phone"></i>
                                ${lab.phone}
                            </a>
                        ` : '-'}
                    </td>
                    <td>${lab.address || '-'}</td>
                    <td>
                        <span class="works-count-badge">
                            ${stats.count}
                        </span>
                    </td>
                    <td>
                        <strong>${Utils.formatCurrency(stats.totalCost)}</strong>
                    </td>
                    <td>${Utils.formatDate(lab.createdAt, 'DD/MM/YYYY')}</td>
                    <td class="actions-cell" onclick="event.stopPropagation()">
                        <div class="action-buttons">
                            <button class="btn-icon btn-primary" onclick="ExternalLabs.editLab('${lab.id}')" 
                                    title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn-icon btn-warning" onclick="ExternalLabs.addWorkToLab('${lab.id}')" 
                                    title="إضافة عمل">
                                <i class="fas fa-briefcase"></i>
                            </button>
                            <button class="btn-icon btn-info" onclick="ExternalLabs.viewLabWorks('${lab.id}')" 
                                    title="عرض الأعمال">
                                <i class="fas fa-list"></i>
                            </button>
                            <button class="btn-icon btn-danger" onclick="ExternalLabs.deleteLab('${lab.id}')" 
                                    title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        // إذا لم توجد بيانات
        if (pageData.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="no-data">
                        <div class="no-data-message">
                            <i class="fas fa-building"></i>
                            <p>لا توجد معامل مطابقة للبحث</p>
                        </div>
                    </td>
                </tr>
            `;
        }

        this.updatePagination();
    }

    // تحديث جدول الأعمال
    static updateWorksTable() {
        const tbody = document.getElementById('works-tbody');
        if (!tbody) return;

        const works = Database.getItem('externalWorks') || [];
        const labs = this.currentLabs;

        // إنشاء صفوف الجدول
        tbody.innerHTML = works.slice(0, 10).map(work => {
            const lab = labs.find(l => l.id === work.labId);
            return `
                <tr onclick="ExternalLabs.viewWorkDetails('${work.id}')" class="table-row-clickable">
                    <td><strong>${work.workNumber}</strong></td>
                    <td>${lab ? lab.name : 'معمل محذوف'}</td>
                    <td>${work.workType}</td>
                    <td><strong>${Utils.formatCurrency(work.cost || 0)}</strong></td>
                    <td>
                        <span class="status-badge status-${work.status}">
                            ${this.getWorkStatusDisplayName(work.status)}
                        </span>
                    </td>
                    <td>${Utils.formatDate(work.sentDate, 'DD/MM/YYYY')}</td>
                    <td>${work.receivedDate ? Utils.formatDate(work.receivedDate, 'DD/MM/YYYY') : '-'}</td>
                    <td class="actions-cell" onclick="event.stopPropagation()">
                        <div class="action-buttons">
                            <button class="btn-icon btn-primary" onclick="ExternalLabs.editWork('${work.id}')" 
                                    title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn-icon btn-success" onclick="ExternalLabs.updateWorkStatus('${work.id}')" 
                                    title="تحديث الحالة">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button class="btn-icon btn-danger" onclick="ExternalLabs.deleteWork('${work.id}')" 
                                    title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        // إذا لم توجد بيانات
        if (works.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="no-data">
                        <div class="no-data-message">
                            <i class="fas fa-briefcase"></i>
                            <p>لا توجد أعمال مرسلة</p>
                        </div>
                    </td>
                </tr>
            `;
        }

        // تحديث عداد الأعمال
        const worksCountElement = document.getElementById('works-count');
        if (worksCountElement) {
            worksCountElement.textContent = `${works.length} عمل`;
        }
    }

    // الحصول على اسم عرض حالة العمل
    static getWorkStatusDisplayName(status) {
        const statusNames = {
            sent: 'مرسل',
            inProgress: 'قيد التنفيذ',
            completed: 'مكتمل',
            received: 'تم الاستلام'
        };
        return statusNames[status] || status;
    }

    // تحديث التصفح
    static updatePagination() {
        const totalPages = Math.ceil(this.filteredLabs.length / this.pageSize);
        const paginationContainer = document.getElementById('labs-pagination');
        
        if (!paginationContainer) return;

        let paginationHTML = `
            <div class="pagination-info">
                عرض ${((this.currentPage - 1) * this.pageSize) + 1} إلى 
                ${Math.min(this.currentPage * this.pageSize, this.filteredLabs.length)} 
                من ${this.filteredLabs.length} معمل
            </div>
            <div class="pagination-controls">
        `;

        // زر السابق
        if (this.currentPage > 1) {
            paginationHTML += `
                <button class="btn btn-secondary btn-sm" onclick="ExternalLabs.changePage(${this.currentPage - 1})">
                    <i class="fas fa-chevron-right"></i>
                    السابق
                </button>
            `;
        }

        // أرقام الصفحات
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === this.currentPage ? 'btn-primary' : 'btn-secondary';
            paginationHTML += `
                <button class="btn ${activeClass} btn-sm" onclick="ExternalLabs.changePage(${i})">
                    ${i}
                </button>
            `;
        }

        // زر التالي
        if (this.currentPage < totalPages) {
            paginationHTML += `
                <button class="btn btn-secondary btn-sm" onclick="ExternalLabs.changePage(${this.currentPage + 1})">
                    التالي
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;
        }

        paginationHTML += '</div>';
        paginationContainer.innerHTML = paginationHTML;
    }

    // تغيير الصفحة
    static changePage(page) {
        this.currentPage = page;
        this.updateTable();
    }

    // تحديث الإحصائيات
    static updateStats() {
        const countElement = document.getElementById('labs-count');
        if (countElement) {
            countElement.textContent = `${this.filteredLabs.length} معمل`;
        }

        // تحديث إحصائيات العنوان
        this.updateDepartmentHeaderStats();
    }

    // تحديث إحصائيات عنوان الإدارة
    static updateDepartmentHeaderStats() {
        const allLabs = this.currentLabs;
        const works = Database.getItem('externalWorks') || [];

        // حساب الإحصائيات
        const stats = {
            totalLabs: allLabs.length,
            totalWorks: works.length,
            totalCost: works.reduce((sum, work) => sum + (work.cost || 0), 0)
        };

        // تحديث العناصر
        const headerTotalLabs = document.getElementById('header-total-labs');
        const headerTotalWorks = document.getElementById('header-total-works');
        const headerTotalCost = document.getElementById('header-total-cost');

        if (headerTotalLabs) headerTotalLabs.textContent = stats.totalLabs;
        if (headerTotalWorks) headerTotalWorks.textContent = stats.totalWorks;
        if (headerTotalCost) headerTotalCost.textContent = Utils.formatCurrency(stats.totalCost);
    }

    // إعادة تعيين الفلاتر
    static resetFilters() {
        this.searchQuery = '';
        this.currentPage = 1;

        const searchInput = document.getElementById('labs-search');
        if (searchInput) searchInput.value = '';

        this.applyFilters();
        this.updateTable();
    }

    // عرض نافذة إضافة معمل جديد
    static showAddModal() {
        Utils.createModal({
            title: 'إضافة معمل خارجي جديد',
            content: `
                <form id="add-lab-form" class="lab-form">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="lab-name">اسم المعمل *</label>
                            <input type="text" id="lab-name" name="name" required>
                        </div>

                        <div class="form-group">
                            <label for="lab-specialization">التخصص</label>
                            <input type="text" id="lab-specialization" name="specialization"
                                   placeholder="مثل: تركيبات ثابتة، أطقم متحركة">
                        </div>

                        <div class="form-group">
                            <label for="lab-contact-person">جهة الاتصال</label>
                            <input type="text" id="lab-contact-person" name="contactPerson">
                        </div>

                        <div class="form-group">
                            <label for="lab-phone">رقم الهاتف</label>
                            <input type="tel" id="lab-phone" name="phone">
                        </div>

                        <div class="form-group">
                            <label for="lab-phone2">رقم هاتف إضافي</label>
                            <input type="tel" id="lab-phone2" name="phone2">
                        </div>

                        <div class="form-group">
                            <label for="lab-email">البريد الإلكتروني</label>
                            <input type="email" id="lab-email" name="email">
                        </div>

                        <div class="form-group full-width">
                            <label for="lab-address">العنوان</label>
                            <textarea id="lab-address" name="address" rows="2"></textarea>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="lab-notes">ملاحظات</label>
                        <textarea id="lab-notes" name="notes" rows="3"></textarea>
                    </div>

                    <div class="modal-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ المعمل
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            `,
            size: 'large'
        });

        this.setupAddLabForm();
    }

    // عرض نافذة إضافة عمل جديد
    static showAddWorkModal() {
        const labs = this.currentLabs;
        if (labs.length === 0) {
            Utils.showNotification('يجب إضافة معمل خارجي أولاً', 'warning');
            return;
        }

        Utils.createModal({
            title: 'إضافة عمل جديد للمعامل الخارجية',
            content: `
                <form id="add-work-form" class="work-form">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="work-number">رقم العمل *</label>
                            <input type="text" id="work-number" name="workNumber" value="${this.generateWorkNumber()}" required>
                        </div>

                        <div class="form-group">
                            <label for="work-lab">المعمل *</label>
                            <select id="work-lab" name="labId" required>
                                <option value="">اختر المعمل</option>
                                ${labs.map(lab => `<option value="${lab.id}">${lab.name}</option>`).join('')}
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="work-type">نوع العمل *</label>
                            <select id="work-type" name="workType" required>
                                <option value="">اختر نوع العمل</option>
                                <option value="تركيبات ثابتة">تركيبات ثابتة</option>
                                <option value="أطقم متحركة">أطقم متحركة</option>
                                <option value="تقويم">تقويم</option>
                                <option value="زراعة">زراعة</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="work-cost">التكلفة</label>
                            <input type="number" id="work-cost" name="cost" step="0.01" min="0">
                        </div>

                        <div class="form-group">
                            <label for="work-sent-date">تاريخ الإرسال *</label>
                            <input type="date" id="work-sent-date" name="sentDate" value="${new Date().toISOString().split('T')[0]}" required>
                        </div>

                        <div class="form-group">
                            <label for="work-expected-date">تاريخ الاستلام المتوقع</label>
                            <input type="date" id="work-expected-date" name="expectedDate">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="work-description">وصف العمل</label>
                        <textarea id="work-description" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="work-notes">ملاحظات</label>
                        <textarea id="work-notes" name="notes" rows="2"></textarea>
                    </div>

                    <div class="modal-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ العمل
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            `,
            size: 'large'
        });

        this.setupAddWorkForm();
    }

    // توليد رقم عمل جديد
    static generateWorkNumber() {
        const works = Database.getItem('externalWorks') || [];
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');

        const prefix = `EXT-${year}${month}${day}`;
        const todayWorks = works.filter(w => w.workNumber && w.workNumber.startsWith(prefix));
        const nextNumber = String(todayWorks.length + 1).padStart(3, '0');

        return `${prefix}-${nextNumber}`;
    }

    // إعداد نموذج إضافة العمل
    static setupAddWorkForm() {
        const form = document.getElementById('add-work-form');
        if (!form) return;

        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveWork();
        });
    }

    // حفظ العمل
    static async saveWork() {
        const form = document.getElementById('add-work-form');
        if (!form) return;

        const formData = new FormData(form);

        // التحقق من صحة البيانات
        const workNumber = formData.get('workNumber').trim();
        const labId = formData.get('labId');
        const workType = formData.get('workType');
        const sentDate = formData.get('sentDate');

        if (!workNumber || !labId || !workType || !sentDate) {
            Utils.showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
            return;
        }

        // التحقق من عدم تكرار رقم العمل
        const works = Database.getItem('externalWorks') || [];
        const existingWork = works.find(w => w.workNumber === workNumber);
        if (existingWork) {
            Utils.showNotification('رقم العمل مستخدم بالفعل', 'warning');
            return;
        }

        // جمع بيانات العمل
        const workData = {
            id: Date.now().toString(),
            workNumber: workNumber,
            labId: labId,
            workType: workType,
            cost: parseFloat(formData.get('cost')) || 0,
            sentDate: sentDate,
            expectedDate: formData.get('expectedDate') || null,
            description: formData.get('description')?.trim() || '',
            notes: formData.get('notes')?.trim() || '',
            status: 'sent',
            createdAt: new Date().toISOString()
        };

        // حفظ في قاعدة البيانات
        works.push(workData);
        Database.setItem('externalWorks', works);

        Utils.showNotification('تم حفظ العمل بنجاح', 'success');
        Utils.closeModal();
        this.loadData();
    }

    // إعداد نموذج إضافة المعمل
    static setupAddLabForm() {
        const form = document.getElementById('add-lab-form');
        if (!form) return;

        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveLab();
        });
    }

    // حفظ المعمل
    static async saveLab() {
        const form = document.getElementById('add-lab-form');
        if (!form) return;

        const formData = new FormData(form);

        // التحقق من صحة البيانات
        const name = formData.get('name').trim();

        if (!name) {
            Utils.showNotification('يرجى إدخال اسم المعمل', 'warning');
            return;
        }

        // التحقق من عدم تكرار اسم المعمل
        const existingLab = this.currentLabs.find(l => l.name.toLowerCase() === name.toLowerCase());
        if (existingLab) {
            Utils.showNotification('اسم المعمل موجود بالفعل', 'warning');
            return;
        }

        // جمع بيانات المعمل
        const labData = {
            id: Utils.generateId(),
            name: name,
            specialization: formData.get('specialization')?.trim() || '',
            contactPerson: formData.get('contactPerson')?.trim() || '',
            phone: formData.get('phone')?.trim() || '',
            phone2: formData.get('phone2')?.trim() || '',
            email: formData.get('email')?.trim() || '',
            address: formData.get('address')?.trim() || '',
            notes: formData.get('notes')?.trim() || '',
            createdAt: new Date().toISOString()
        };

        // التحقق من صحة البريد الإلكتروني
        if (labData.email && !Utils.isValidEmail(labData.email)) {
            Utils.showNotification('البريد الإلكتروني غير صحيح', 'warning');
            return;
        }

        // حفظ في قاعدة البيانات
        this.currentLabs.push(labData);
        const saved = Database.setItem('externalLabs', this.currentLabs);

        if (saved) {
            Database.addActivity({
                type: 'external_lab_added',
                text: `تم إضافة معمل خارجي جديد: ${labData.name}`,
                icon: 'building',
                userId: Auth.getCurrentUser()?.id
            });

            Utils.showNotification('تم حفظ المعمل بنجاح', 'success');
            Utils.closeModal();
            this.loadData();
        } else {
            Utils.showNotification('فشل في حفظ المعمل', 'error');
        }
    }

    // عرض نافذة إضافة عمل جديد
    static showAddWorkModal(labId = null) {
        const labs = this.currentLabs;

        Utils.createModal({
            title: 'إضافة عمل جديد للمعامل الخارجية',
            content: `
                <form id="add-work-form" class="work-form">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="work-lab">المعمل *</label>
                            <select id="work-lab" name="labId" required>
                                <option value="">اختر المعمل</option>
                                ${labs.map(lab =>
                                    `<option value="${lab.id}" ${lab.id === labId ? 'selected' : ''}>${lab.name}</option>`
                                ).join('')}
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="work-type">نوع العمل *</label>
                            <input type="text" id="work-type" name="workType" required
                                   placeholder="مثل: تركيبة بورسلين، طقم متحرك">
                        </div>

                        <div class="form-group">
                            <label for="work-cost">التكلفة</label>
                            <input type="number" id="work-cost" name="cost" step="0.01" min="0">
                        </div>

                        <div class="form-group">
                            <label for="work-sent-date">تاريخ الإرسال *</label>
                            <input type="date" id="work-sent-date" name="sentDate" required>
                        </div>

                        <div class="form-group">
                            <label for="work-expected-date">تاريخ الاستلام المتوقع</label>
                            <input type="date" id="work-expected-date" name="expectedDate">
                        </div>

                        <div class="form-group">
                            <label for="work-status">الحالة</label>
                            <select id="work-status" name="status">
                                <option value="sent">مرسل</option>
                                <option value="inProgress">قيد التنفيذ</option>
                                <option value="completed">مكتمل</option>
                                <option value="received">تم الاستلام</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="work-description">وصف العمل</label>
                        <textarea id="work-description" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="work-notes">ملاحظات</label>
                        <textarea id="work-notes" name="notes" rows="2"></textarea>
                    </div>

                    <div class="modal-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ العمل
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            `,
            size: 'large'
        });

        // تعيين تاريخ الإرسال الافتراضي (اليوم)
        const sentDateInput = document.getElementById('work-sent-date');
        if (sentDateInput) {
            sentDateInput.value = Utils.formatDate(new Date(), 'YYYY-MM-DD');
        }

        this.setupAddWorkForm();
    }

    // إعداد نموذج إضافة العمل
    static setupAddWorkForm() {
        const form = document.getElementById('add-work-form');
        if (!form) return;

        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveWork();
        });
    }

    // حفظ العمل
    static async saveWork() {
        const form = document.getElementById('add-work-form');
        if (!form) return;

        const formData = new FormData(form);

        // التحقق من صحة البيانات
        const labId = formData.get('labId');
        const workType = formData.get('workType').trim();
        const sentDate = formData.get('sentDate');

        if (!labId) {
            Utils.showNotification('يرجى اختيار المعمل', 'warning');
            return;
        }

        if (!workType) {
            Utils.showNotification('يرجى إدخال نوع العمل', 'warning');
            return;
        }

        if (!sentDate) {
            Utils.showNotification('يرجى تحديد تاريخ الإرسال', 'warning');
            return;
        }

        // توليد رقم العمل
        const works = Database.getItem('externalWorks') || [];
        const workNumber = `EXT-${Utils.formatDate(new Date(), 'YYYYMM')}-${String(works.length + 1).padStart(3, '0')}`;

        // جمع بيانات العمل
        const workData = {
            id: Utils.generateId(),
            workNumber: workNumber,
            labId: labId,
            workType: workType,
            cost: parseFloat(formData.get('cost')) || 0,
            sentDate: sentDate,
            expectedDate: formData.get('expectedDate') || null,
            status: formData.get('status') || 'sent',
            description: formData.get('description')?.trim() || '',
            notes: formData.get('notes')?.trim() || '',
            createdAt: new Date().toISOString(),
            createdBy: Auth.getCurrentUser()?.id
        };

        // حفظ في قاعدة البيانات
        works.push(workData);
        const saved = Database.setItem('externalWorks', works);

        if (saved) {
            const lab = this.currentLabs.find(l => l.id === labId);
            Database.addActivity({
                type: 'external_work_added',
                text: `تم إضافة عمل جديد للمعمل الخارجي: ${lab?.name} - ${workData.workNumber}`,
                icon: 'briefcase',
                userId: Auth.getCurrentUser()?.id
            });

            Utils.showNotification('تم حفظ العمل بنجاح', 'success');
            Utils.closeModal();
            this.loadData();
        } else {
            Utils.showNotification('فشل في حفظ العمل', 'error');
        }
    }

    // إضافة عمل لمعمل محدد
    static addWorkToLab(labId) {
        this.showAddWorkModal(labId);
    }

    // عرض تفاصيل المعمل
    static viewDetails(labId) {
        const lab = this.currentLabs.find(l => l.id === labId);
        if (!lab) {
            Utils.showNotification('المعمل غير موجود', 'error');
            return;
        }

        // حساب إحصائيات المعمل
        const works = Database.getItem('externalWorks') || [];
        const labWorks = works.filter(w => w.labId === labId);
        const totalCost = labWorks.reduce((sum, w) => sum + (w.cost || 0), 0);
        const completedWorks = labWorks.filter(w => w.status === 'completed' || w.status === 'received').length;

        Utils.createModal({
            title: `تفاصيل المعمل - ${lab.name}`,
            content: `
                <div class="lab-details">
                    <div class="details-grid">
                        <div class="detail-item">
                            <label>اسم المعمل:</label>
                            <span>${lab.name}</span>
                        </div>
                        ${lab.specialization ? `
                            <div class="detail-item">
                                <label>التخصص:</label>
                                <span>${lab.specialization}</span>
                            </div>
                        ` : ''}
                        ${lab.contactPerson ? `
                            <div class="detail-item">
                                <label>جهة الاتصال:</label>
                                <span>${lab.contactPerson}</span>
                            </div>
                        ` : ''}
                        ${lab.phone ? `
                            <div class="detail-item">
                                <label>رقم الهاتف:</label>
                                <span>
                                    <a href="tel:${lab.phone}" class="phone-link">
                                        <i class="fas fa-phone"></i>
                                        ${lab.phone}
                                    </a>
                                </span>
                            </div>
                        ` : ''}
                        ${lab.phone2 ? `
                            <div class="detail-item">
                                <label>رقم هاتف إضافي:</label>
                                <span>
                                    <a href="tel:${lab.phone2}" class="phone-link">
                                        <i class="fas fa-phone"></i>
                                        ${lab.phone2}
                                    </a>
                                </span>
                            </div>
                        ` : ''}
                        ${lab.email ? `
                            <div class="detail-item">
                                <label>البريد الإلكتروني:</label>
                                <span>
                                    <a href="mailto:${lab.email}" class="email-link">
                                        <i class="fas fa-envelope"></i>
                                        ${lab.email}
                                    </a>
                                </span>
                            </div>
                        ` : ''}
                        ${lab.address ? `
                            <div class="detail-item full-width">
                                <label>العنوان:</label>
                                <span>${lab.address}</span>
                            </div>
                        ` : ''}
                        <div class="detail-item">
                            <label>تاريخ الإضافة:</label>
                            <span>${Utils.formatDate(lab.createdAt, 'DD/MM/YYYY')}</span>
                        </div>
                    </div>

                    <!-- إحصائيات المعمل -->
                    <div class="lab-stats">
                        <h4>الإحصائيات</h4>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value">${labWorks.length}</div>
                                <div class="stat-label">إجمالي الأعمال</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${completedWorks}</div>
                                <div class="stat-label">أعمال مكتملة</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${Utils.formatCurrency(totalCost)}</div>
                                <div class="stat-label">إجمالي التكلفة</div>
                            </div>
                        </div>
                    </div>

                    ${lab.notes ? `
                        <div class="detail-section">
                            <label>الملاحظات:</label>
                            <div class="notes-content">${lab.notes}</div>
                        </div>
                    ` : ''}

                    <div class="modal-actions">
                        <button class="btn btn-primary" onclick="ExternalLabs.editLab('${lab.id}')">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </button>
                        <button class="btn btn-warning" onclick="ExternalLabs.addWorkToLab('${lab.id}')">
                            <i class="fas fa-briefcase"></i>
                            إضافة عمل
                        </button>
                        <button class="btn btn-info" onclick="ExternalLabs.viewLabWorks('${lab.id}')">
                            <i class="fas fa-list"></i>
                            عرض الأعمال
                        </button>
                        <button class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            `,
            size: 'large'
        });
    }

    // تصدير البيانات
    static exportData() {
        try {
            const labs = this.currentLabs;
            const works = Database.getItem('externalWorks') || [];

            const exportData = {
                labs: labs,
                works: works,
                exportDate: new Date().toISOString(),
                totalLabs: labs.length,
                totalWorks: works.length
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `external-labs-data-${Utils.formatDate(new Date(), 'YYYY-MM-DD')}.json`;
            link.click();

            Utils.showNotification('تم تصدير البيانات بنجاح', 'success');
        } catch (error) {
            console.error('خطأ في تصدير البيانات:', error);
            Utils.showNotification('فشل في تصدير البيانات', 'error');
        }
    }

    // إضافة عمل لمعمل محدد
    static addWorkToLab(labId) {
        const lab = this.currentLabs.find(l => l.id === labId);
        if (!lab) {
            Utils.showNotification('المعمل غير موجود', 'error');
            return;
        }

        Utils.createModal({
            title: `إضافة عمل جديد - ${lab.name}`,
            content: `
                <form id="add-work-to-lab-form" class="work-form">
                    <input type="hidden" name="labId" value="${labId}">

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="work-number-lab">رقم العمل *</label>
                            <input type="text" id="work-number-lab" name="workNumber" value="${this.generateWorkNumber()}" required>
                        </div>

                        <div class="form-group">
                            <label for="work-type-lab">نوع العمل *</label>
                            <select id="work-type-lab" name="workType" required>
                                <option value="">اختر نوع العمل</option>
                                <option value="تركيبات ثابتة">تركيبات ثابتة</option>
                                <option value="أطقم متحركة">أطقم متحركة</option>
                                <option value="تقويم">تقويم</option>
                                <option value="زراعة">زراعة</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="work-cost-lab">التكلفة</label>
                            <input type="number" id="work-cost-lab" name="cost" step="0.01" min="0">
                        </div>

                        <div class="form-group">
                            <label for="work-sent-date-lab">تاريخ الإرسال *</label>
                            <input type="date" id="work-sent-date-lab" name="sentDate" value="${new Date().toISOString().split('T')[0]}" required>
                        </div>

                        <div class="form-group">
                            <label for="work-expected-date-lab">تاريخ الاستلام المتوقع</label>
                            <input type="date" id="work-expected-date-lab" name="expectedDate">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="work-description-lab">وصف العمل</label>
                        <textarea id="work-description-lab" name="description" rows="3"></textarea>
                    </div>

                    <div class="modal-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ العمل
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            `,
            size: 'large'
        });

        // إعداد النموذج
        const form = document.getElementById('add-work-to-lab-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveWork();
            });
        }
    }

    // عرض أعمال المعمل
    static viewLabWorks(labId) {
        const lab = this.currentLabs.find(l => l.id === labId);
        if (!lab) {
            Utils.showNotification('المعمل غير موجود', 'error');
            return;
        }

        const works = Database.getItem('externalWorks') || [];
        const labWorks = works.filter(w => w.labId === labId);

        Utils.createModal({
            title: `أعمال المعمل - ${lab.name}`,
            content: `
                <div class="lab-works">
                    ${labWorks.length > 0 ? `
                        <div class="table-wrapper">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>رقم العمل</th>
                                        <th>نوع العمل</th>
                                        <th>التكلفة</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإرسال</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${labWorks.map(work => `
                                        <tr>
                                            <td><strong>${work.workNumber}</strong></td>
                                            <td>${work.workType}</td>
                                            <td>${Utils.formatCurrency(work.cost || 0)}</td>
                                            <td>
                                                <span class="status-badge status-${work.status}">
                                                    ${this.getWorkStatusDisplayName(work.status)}
                                                </span>
                                            </td>
                                            <td>${Utils.formatDate(work.sentDate, 'DD/MM/YYYY')}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    ` : `
                        <div class="no-data-message">
                            <i class="fas fa-briefcase"></i>
                            <p>لا توجد أعمال لهذا المعمل</p>
                        </div>
                    `}

                    <div class="modal-actions">
                        <button class="btn btn-primary" onclick="ExternalLabs.addWorkToLab('${labId}'); Utils.closeModal();">
                            <i class="fas fa-plus"></i>
                            إضافة عمل جديد
                        </button>
                        <button class="btn btn-secondary" onclick="Utils.closeModal()">إغلاق</button>
                    </div>
                </div>
            `,
            size: 'large'
        });
    }
}
