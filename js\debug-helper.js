// مساعد التشخيص والإصلاح
console.log('🔄 بدء تحميل ملف debug-helper.js...');

class DebugHelper {
    // فحص شامل للنظام
    static performSystemDiagnostic() {
        console.log('🔍 بدء التشخيص الشامل للنظام...');
        
        const results = {
            timestamp: new Date().toISOString(),
            classes: this.checkClasses(),
            dom: this.checkDOM(),
            navigation: this.checkNavigation(),
            issues: [],
            recommendations: []
        };

        // تحليل النتائج
        this.analyzeResults(results);
        
        // عرض التقرير
        this.displayDiagnosticReport(results);
        
        return results;
    }

    // فحص الكلاسات
    static checkClasses() {
        const requiredClasses = [
            'Utils', 'Database', 'Auth', 'Navigation', 'Dashboard',
            'Prosthetics', 'Doctors', 'Employees', 'ExternalLabs',
            'Financial', 'Reports', 'Settings', 'Inventory', 'Backup'
        ];

        const results = {};
        
        requiredClasses.forEach(className => {
            const classObj = window[className];
            results[className] = {
                loaded: typeof classObj !== 'undefined',
                type: typeof classObj,
                methods: classObj ? Object.getOwnPropertyNames(classObj).filter(name => 
                    typeof classObj[name] === 'function' && name !== 'constructor'
                ) : []
            };
        });

        return results;
    }

    // فحص DOM
    static checkDOM() {
        const requiredElements = [
            'pageContent', 'sidebar', 'topBar', 'bottomBar'
        ];

        const results = {};
        
        requiredElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            results[elementId] = {
                exists: !!element,
                visible: element ? element.offsetParent !== null : false,
                hasContent: element ? element.innerHTML.length > 0 : false
            };
        });

        return results;
    }

    // فحص التنقل
    static checkNavigation() {
        const results = {
            navigationClass: typeof Navigation !== 'undefined',
            currentSection: Navigation ? Navigation.currentSection : null,
            sidebarVisible: false,
            menuItems: []
        };

        // فحص الشريط الجانبي
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            results.sidebarVisible = sidebar.offsetParent !== null;
            
            // فحص عناصر القائمة
            const menuItems = sidebar.querySelectorAll('[data-section]');
            results.menuItems = Array.from(menuItems).map(item => ({
                section: item.dataset.section,
                text: item.textContent.trim(),
                visible: item.offsetParent !== null
            }));
        }

        return results;
    }

    // تحليل النتائج
    static analyzeResults(results) {
        // فحص الكلاسات المفقودة
        Object.entries(results.classes).forEach(([className, info]) => {
            if (!info.loaded) {
                results.issues.push(`الكلاس ${className} غير محمل`);
                results.recommendations.push(`تحقق من تحميل ملف js/${className.toLowerCase()}.js`);
            } else if (info.methods.length === 0) {
                results.issues.push(`الكلاس ${className} لا يحتوي على وظائف`);
                results.recommendations.push(`تحقق من بناء الكلاس ${className}`);
            }
        });

        // فحص العناصر المفقودة
        Object.entries(results.dom).forEach(([elementId, info]) => {
            if (!info.exists) {
                results.issues.push(`العنصر ${elementId} غير موجود في DOM`);
                results.recommendations.push(`تحقق من وجود العنصر ${elementId} في HTML`);
            }
        });

        // فحص التنقل
        if (!results.navigation.navigationClass) {
            results.issues.push('كلاس Navigation غير محمل');
            results.recommendations.push('تحقق من تحميل ملف js/navigation.js');
        }
    }

    // عرض تقرير التشخيص
    static displayDiagnosticReport(results) {
        console.group('📊 تقرير التشخيص الشامل');
        
        // عرض حالة الكلاسات
        console.group('🔧 حالة الكلاسات');
        Object.entries(results.classes).forEach(([className, info]) => {
            const status = info.loaded ? '✅' : '❌';
            console.log(`${status} ${className}: ${info.loaded ? 'محمل' : 'غير محمل'} (${info.methods.length} وظيفة)`);
        });
        console.groupEnd();

        // عرض حالة DOM
        console.group('🌐 حالة DOM');
        Object.entries(results.dom).forEach(([elementId, info]) => {
            const status = info.exists ? '✅' : '❌';
            console.log(`${status} ${elementId}: ${info.exists ? 'موجود' : 'غير موجود'}`);
        });
        console.groupEnd();

        // عرض المشاكل
        if (results.issues.length > 0) {
            console.group('⚠️ المشاكل المكتشفة');
            results.issues.forEach(issue => console.warn('❌', issue));
            console.groupEnd();

            console.group('💡 التوصيات');
            results.recommendations.forEach(rec => console.info('💡', rec));
            console.groupEnd();
        } else {
            console.log('✅ لا توجد مشاكل مكتشفة');
        }

        console.groupEnd();

        // إنشاء تقرير مرئي
        this.createVisualReport(results);
    }

    // إنشاء تقرير مرئي
    static createVisualReport(results) {
        if (typeof Utils !== 'undefined' && Utils.createModal) {
            const classesStatus = Object.entries(results.classes).map(([name, info]) => `
                <div class="diagnostic-item ${info.loaded ? 'success' : 'error'}">
                    <span class="item-icon">${info.loaded ? '✅' : '❌'}</span>
                    <span class="item-name">${name}</span>
                    <span class="item-status">${info.loaded ? 'محمل' : 'غير محمل'}</span>
                    <span class="item-details">${info.methods.length} وظيفة</span>
                </div>
            `).join('');

            const domStatus = Object.entries(results.dom).map(([name, info]) => `
                <div class="diagnostic-item ${info.exists ? 'success' : 'error'}">
                    <span class="item-icon">${info.exists ? '✅' : '❌'}</span>
                    <span class="item-name">${name}</span>
                    <span class="item-status">${info.exists ? 'موجود' : 'غير موجود'}</span>
                </div>
            `).join('');

            const issuesList = results.issues.length > 0 ? 
                results.issues.map(issue => `<li class="issue-item">❌ ${issue}</li>`).join('') :
                '<li class="success-item">✅ لا توجد مشاكل</li>';

            Utils.createModal({
                title: 'تقرير التشخيص الشامل',
                content: `
                    <div class="diagnostic-report">
                        <div class="report-section">
                            <h3>🔧 حالة الكلاسات</h3>
                            <div class="diagnostic-grid">
                                ${classesStatus}
                            </div>
                        </div>

                        <div class="report-section">
                            <h3>🌐 حالة DOM</h3>
                            <div class="diagnostic-grid">
                                ${domStatus}
                            </div>
                        </div>

                        <div class="report-section">
                            <h3>⚠️ المشاكل والتوصيات</h3>
                            <ul class="issues-list">
                                ${issuesList}
                            </ul>
                        </div>

                        <div class="report-actions">
                            <button class="btn btn-primary" onclick="DebugHelper.fixCommonIssues()">
                                <i class="fas fa-wrench"></i>
                                إصلاح المشاكل الشائعة
                            </button>
                            <button class="btn btn-info" onclick="DebugHelper.testSpecificFeature()">
                                <i class="fas fa-test-tube"></i>
                                اختبار ميزة محددة
                            </button>
                            <button class="btn btn-secondary" onclick="Utils.closeModal()">
                                <i class="fas fa-times"></i>
                                إغلاق
                            </button>
                        </div>
                    </div>

                    <style>
                        .diagnostic-report .report-section {
                            margin-bottom: 20px;
                            padding: 15px;
                            border: 1px solid #ddd;
                            border-radius: 8px;
                        }
                        .diagnostic-grid {
                            display: grid;
                            gap: 10px;
                        }
                        .diagnostic-item {
                            display: grid;
                            grid-template-columns: 30px 1fr 100px 80px;
                            align-items: center;
                            padding: 8px;
                            border-radius: 4px;
                        }
                        .diagnostic-item.success {
                            background: #d4edda;
                            border: 1px solid #c3e6cb;
                        }
                        .diagnostic-item.error {
                            background: #f8d7da;
                            border: 1px solid #f5c6cb;
                        }
                        .issues-list {
                            list-style: none;
                            padding: 0;
                        }
                        .issues-list li {
                            padding: 5px 0;
                        }
                        .success-item {
                            color: #155724;
                        }
                        .issue-item {
                            color: #721c24;
                        }
                    </style>
                `,
                size: 'large'
            });
        }
    }

    // إصلاح المشاكل الشائعة
    static fixCommonIssues() {
        console.log('🔧 محاولة إصلاح المشاكل الشائعة...');

        // إعادة تحميل الكلاسات المفقودة
        if (typeof Prosthetics === 'undefined') {
            console.log('🔄 محاولة إعادة تحميل Prosthetics...');
            this.reloadScript('js/prosthetics.js');
        }

        if (typeof Reports === 'undefined') {
            console.log('🔄 محاولة إعادة تحميل Reports...');
            this.reloadScript('js/reports.js');
        }

        // إعادة تهيئة التنقل
        if (typeof Navigation !== 'undefined') {
            try {
                Navigation.init();
                console.log('✅ تم إعادة تهيئة Navigation');
            } catch (error) {
                console.error('❌ فشل في إعادة تهيئة Navigation:', error);
            }
        }

        setTimeout(() => {
            Utils.showNotification('تم محاولة إصلاح المشاكل الشائعة', 'info');
            this.performSystemDiagnostic();
        }, 2000);
    }

    // إعادة تحميل سكريبت
    static reloadScript(src) {
        // إزالة السكريبت القديم
        const oldScript = document.querySelector(`script[src="${src}"]`);
        if (oldScript) {
            oldScript.remove();
        }

        // إضافة السكريبت الجديد
        const script = document.createElement('script');
        script.src = src;
        script.onload = () => console.log(`✅ تم إعادة تحميل ${src}`);
        script.onerror = () => console.error(`❌ فشل في إعادة تحميل ${src}`);
        document.head.appendChild(script);
    }

    // اختبار ميزة محددة
    static testSpecificFeature() {
        const feature = prompt('أدخل اسم الميزة للاختبار (prosthetics/reports/navigation):');
        
        switch (feature?.toLowerCase()) {
            case 'prosthetics':
                this.testProsthetics();
                break;
            case 'reports':
                this.testReports();
                break;
            case 'navigation':
                this.testNavigation();
                break;
            default:
                Utils.showNotification('ميزة غير مدعومة', 'warning');
        }
    }

    // اختبار Prosthetics
    static testProsthetics() {
        try {
            if (typeof Prosthetics !== 'undefined') {
                console.log('🧪 اختبار Prosthetics...');
                
                // محاولة استدعاء render
                const pageContent = document.getElementById('pageContent');
                if (pageContent) {
                    Prosthetics.render();
                    Utils.showNotification('✅ Prosthetics يعمل بشكل صحيح', 'success');
                } else {
                    Utils.showNotification('❌ pageContent غير موجود', 'error');
                }
            } else {
                Utils.showNotification('❌ Prosthetics غير محمل', 'error');
            }
        } catch (error) {
            console.error('❌ خطأ في اختبار Prosthetics:', error);
            Utils.showNotification(`خطأ في Prosthetics: ${error.message}`, 'error');
        }
    }

    // اختبار Reports
    static testReports() {
        try {
            if (typeof Reports !== 'undefined') {
                console.log('🧪 اختبار Reports...');
                
                // محاولة استدعاء render
                const pageContent = document.getElementById('pageContent');
                if (pageContent) {
                    Reports.render();
                    Utils.showNotification('✅ Reports يعمل بشكل صحيح', 'success');
                } else {
                    Utils.showNotification('❌ pageContent غير موجود', 'error');
                }
            } else {
                Utils.showNotification('❌ Reports غير محمل', 'error');
            }
        } catch (error) {
            console.error('❌ خطأ في اختبار Reports:', error);
            Utils.showNotification(`خطأ في Reports: ${error.message}`, 'error');
        }
    }

    // اختبار Navigation
    static testNavigation() {
        try {
            if (typeof Navigation !== 'undefined') {
                console.log('🧪 اختبار Navigation...');
                
                // اختبار التنقل للتقارير
                Navigation.navigateTo('reports');
                Utils.showNotification('✅ Navigation يعمل بشكل صحيح', 'success');
            } else {
                Utils.showNotification('❌ Navigation غير محمل', 'error');
            }
        } catch (error) {
            console.error('❌ خطأ في اختبار Navigation:', error);
            Utils.showNotification(`خطأ في Navigation: ${error.message}`, 'error');
        }
    }
}

// إضافة اختصار لوحة المفاتيح للتشخيص (Ctrl+Shift+D)
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        e.preventDefault();
        DebugHelper.performSystemDiagnostic();
    }
});

// تسجيل اكتمال تحميل الملف
console.log('✅ تم تحميل ملف debug-helper.js بالكامل');

// التأكد من أن الكلاس متاح عالمياً
window.DebugHelper = DebugHelper;
