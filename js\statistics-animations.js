// تأثيرات وحركات الإحصائيات الموحدة

class StatisticsAnimations {
    
    // تهيئة التأثيرات للإحصائيات
    static initializeStatistics() {
        // تجنب التهيئة المتكررة للعناصر نفسها
        const statBoxes = document.querySelectorAll('.stat-box:not(.stats-initialized)');
        if (statBoxes.length === 0) return;

        try {
            this.addHoverEffects();
            this.addCountUpAnimation();
            this.addPulseEffect();
            this.addLoadingStates();

            // وضع علامة على العناصر المهيأة
            statBoxes.forEach(box => box.classList.add('stats-initialized'));
        } catch (error) {
            console.warn('خطأ في تهيئة الإحصائيات:', error);
        }
    }
    
    // إضافة تأثيرات التمرير
    static addHoverEffects() {
        const statBoxes = document.querySelectorAll('.stat-box:not(.hover-initialized)');

        statBoxes.forEach(box => {
            // تجنب إضافة المستمعات المتكررة
            box.classList.add('hover-initialized');

            box.addEventListener('mouseenter', () => {
                this.playHoverSound();
                this.addRippleEffect(box);
            });

            box.addEventListener('mouseleave', () => {
                this.removeRippleEffect(box);
            });

            box.addEventListener('click', () => {
                this.playClickSound();
                this.addClickEffect(box);
            });
        });
    }
    
    // تأثير الموجة عند التمرير
    static addRippleEffect(element) {
        const ripple = document.createElement('div');
        ripple.className = 'stat-ripple';
        ripple.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: ripple-expand 0.6s ease-out;
            pointer-events: none;
            z-index: 0;
        `;
        
        element.appendChild(ripple);
        
        // إضافة CSS للحركة
        if (!document.getElementById('ripple-styles')) {
            const style = document.createElement('style');
            style.id = 'ripple-styles';
            style.textContent = `
                @keyframes ripple-expand {
                    to {
                        width: 200px;
                        height: 200px;
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    // إزالة تأثير الموجة
    static removeRippleEffect(element) {
        const ripples = element.querySelectorAll('.stat-ripple');
        ripples.forEach(ripple => {
            setTimeout(() => {
                if (ripple.parentNode) {
                    ripple.parentNode.removeChild(ripple);
                }
            }, 600);
        });
    }
    
    // تأثير النقر
    static addClickEffect(element) {
        element.style.transform = 'translateY(-8px) scale(0.98)';
        setTimeout(() => {
            element.style.transform = '';
        }, 150);
    }
    
    // حركة العد التصاعدي للأرقام
    static addCountUpAnimation() {
        const statValues = document.querySelectorAll('.stat-value:not(.count-animated)');

        statValues.forEach(value => {
            value.classList.add('count-animated');
            const finalValue = parseInt(value.textContent.replace(/[^\d]/g, '')) || 0;
            if (finalValue > 0) {
                this.animateCountUp(value, 0, finalValue, 1500);
            }
        });
    }
    
    // تنفيذ حركة العد
    static animateCountUp(element, start, end, duration) {
        const startTime = performance.now();
        const isRevenue = element.closest('.stat-box').classList.contains('revenue');
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // استخدام easing function للحركة الطبيعية
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = Math.floor(start + (end - start) * easeOutQuart);
            
            if (isRevenue) {
                element.textContent = this.formatCurrency(currentValue);
            } else {
                element.textContent = currentValue.toLocaleString('ar-EG');
            }
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }
    
    // تنسيق العملة
    static formatCurrency(amount) {
        return new Intl.NumberFormat('ar-EG', {
            style: 'currency',
            currency: 'EGP',
            minimumFractionDigits: 0
        }).format(amount);
    }
    
    // تأثير النبض للإحصائيات المهمة
    static addPulseEffect() {
        const importantStats = document.querySelectorAll('.stat-box.highlight, .stat-box.pulse');
        
        importantStats.forEach(stat => {
            stat.style.animation = 'pulse-glow 3s ease-in-out infinite';
        });
    }
    
    // حالات التحميل
    static addLoadingStates() {
        const statBoxes = document.querySelectorAll('.stat-box');
        
        statBoxes.forEach(box => {
            const value = box.querySelector('.stat-value');
            if (value && (value.textContent === '0' || value.textContent === '')) {
                this.showLoadingState(box);
            }
        });
    }
    
    // عرض حالة التحميل
    static showLoadingState(element) {
        element.classList.add('loading');
        const value = element.querySelector('.stat-value');
        if (value) {
            value.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        }
    }
    
    // إخفاء حالة التحميل
    static hideLoadingState(element) {
        element.classList.remove('loading');
    }
    
    // تحديث قيمة إحصائية مع حركة
    static updateStatValue(elementId, newValue, isRevenue = false) {
        try {
            const element = document.getElementById(elementId);
            if (!element) return;

            const statBox = element.closest('.stat-box');
            if (statBox) {
                this.hideLoadingState(statBox);
            }

            const currentValue = parseInt(element.textContent.replace(/[^\d]/g, '')) || 0;

            // تجنب التحديث إذا كانت القيمة نفسها
            if (currentValue === newValue) return;

            this.animateCountUp(element, currentValue, newValue, 1000);

            // إضافة تأثير التحديث بحذر
            if (statBox) {
                statBox.style.transform = 'scale(1.05)';
                statBox.style.boxShadow = '0 25px 50px rgba(0,0,0,0.2)';

                setTimeout(() => {
                    statBox.style.transform = '';
                    statBox.style.boxShadow = '';
                }, 300);
            }
        } catch (error) {
            console.warn('خطأ في تحديث قيمة الإحصائية:', error);
        }
    }
    
    // أصوات التفاعل (اختيارية)
    static playHoverSound() {
        // يمكن إضافة صوت خفيف عند التمرير
        if (window.AudioContext) {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                gainNode.gain.setValueAtTime(0.01, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.1);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.1);
            } catch (e) {
                // تجاهل الأخطاء الصوتية
            }
        }
    }
    
    static playClickSound() {
        if (window.AudioContext) {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.setValueAtTime(1000, audioContext.currentTime);
                gainNode.gain.setValueAtTime(0.02, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.2);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.2);
            } catch (e) {
                // تجاهل الأخطاء الصوتية
            }
        }
    }
    
    // تأثيرات خاصة للحالات المختلفة
    static highlightStat(elementId, type = 'success') {
        const element = document.getElementById(elementId);
        if (!element) return;
        
        const statBox = element.closest('.stat-box');
        statBox.classList.add(type);
        
        setTimeout(() => {
            statBox.classList.remove(type);
        }, 3000);
    }
    
    // تحديث جميع الإحصائيات
    static updateAllStats(data) {
        if (!data || typeof data !== 'object') return;

        try {
            if (data.totalWorks !== undefined) {
                this.updateStatValue('totalWorks', data.totalWorks);
            }
            if (data.pendingWorks !== undefined) {
                this.updateStatValue('pendingWorks', data.pendingWorks);
            }
            if (data.completedWorks !== undefined) {
                this.updateStatValue('completedWorks', data.completedWorks);
            }
            if (data.totalRevenue !== undefined) {
                this.updateStatValue('totalRevenue', data.totalRevenue, true);
            }
        } catch (error) {
            console.warn('خطأ في تحديث الإحصائيات:', error);
        }
    }
    
    // تهيئة عند تحميل الصفحة
    static init() {
        // تجنب التهيئة المتكررة
        if (this.initialized) return;
        this.initialized = true;

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.safeInitialize();
            });
        } else {
            this.safeInitialize();
        }
    }

    // تهيئة آمنة
    static safeInitialize() {
        try {
            this.initializeStatistics();
        } catch (error) {
            console.warn('خطأ في تهيئة تأثيرات الإحصائيات:', error);
        }
    }
}

// تهيئة التأثيرات فقط إذا لم تكن مهيأة
if (typeof window !== 'undefined' && !window.StatisticsAnimationsInitialized) {
    window.StatisticsAnimationsInitialized = true;
    StatisticsAnimations.init();
}
