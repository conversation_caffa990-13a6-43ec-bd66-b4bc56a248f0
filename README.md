# نظام إدارة معمل الأسنان المتطور

## 🦷 نظرة عامة

نظام إدارة شامل ومتطور لمعامل الأسنان مصمم خصيصاً للبيئة العربية. يوفر النظام واجهة مستخدم عصرية وسهلة الاستخدام مع دعم كامل للغة العربية واتجاه RTL.

## ✨ المميزات الرئيسية

### 🎯 الإجراءات السريعة (3 أزرار رئيسية)
- **إضافة تركيبة جديدة** 🦷 - إنشاء طلب تركيبة سنية جديد بسرعة
- **إضافة طبيب جديد** 👨‍⚕️ - تسجيل طبيب أسنان جديد في النظام
- **تصدير البيانات** 📥 - تصدير جميع بيانات النظام كنسخة احتياطية شاملة

### 📊 لوحة التحكم
- إحصائيات شاملة ومرئية
- مخططات تفاعلية للبيانات
- نشاطات النظام الأخيرة
- مؤشرات الأداء الرئيسية

### 🦷 إدارة التركيبات السنية
- مخطط أسنان تفاعلي
- أنواع متعددة من التركيبات (بورسلين، زيركون، معدنية، متحركة، تقويم)
- تتبع حالة التركيبات
- حساب التكاليف التلقائي
- طباعة التقارير

### 👨‍⚕️ إدارة الأطباء
- قاعدة بيانات شاملة للأطباء
- معلومات الاتصال والتخصصات
- تتبع التركيبات لكل طبيب
- إحصائيات الأداء

### 👥 إدارة الموظفين
- سجلات الموظفين الكاملة
- إدارة المناوبات والحضور
- تتبع الأداء والمهام

### 🏢 المعامل الخارجية
- إدارة شراكات المعامل الخارجية
- تتبع الأعمال المرسلة
- حساب التكاليف والمدفوعات

### 💰 النظام المالي
- إدارة الحسابات المالية
- تتبع المصروفات والإيرادات
- نظام الدفعات والفواتير
- تقارير مالية شاملة

### 📈 التقارير والإحصائيات
- تقارير مفصلة لجميع الأقسام
- تصدير البيانات بصيغ متعددة
- مخططات بيانية تفاعلية

### 🔒 الأمان والنسخ الاحتياطي
- نظام مصادقة آمن
- نسخ احتياطية تلقائية
- تشفير البيانات المحلية

## 🚀 كيفية التشغيل

### المتطلبات
- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- Python 3.x (للخادم المحلي)

### التشغيل السريع
1. قم بتحميل الملفات
2. انقر نقراً مزدوجاً على `start-server.bat`
3. افتح المتصفح وانتقل إلى `http://localhost:8000`

### التشغيل اليدوي
```bash
# في مجلد النظام
python -m http.server 8000
```

## 🎨 التصميم العصري

### المميزات البصرية
- **تصميم Material Design** مع لمسة عربية
- **ألوان متدرجة** وتأثيرات بصرية حديثة
- **رسوم متحركة سلسة** للتفاعلات
- **تصميم متجاوب** يعمل على جميع الأجهزة
- **دعم الوضع المظلم** (قريباً)

### تجربة المستخدم
- **واجهة بديهية** سهلة الاستخدام
- **تنقل سريع** بين الأقسام
- **بحث ذكي** في جميع البيانات
- **إشعارات تفاعلية** للعمليات
- **اختصارات لوحة المفاتيح**

## 📱 التوافق

### المتصفحات المدعومة
- ✅ Google Chrome 80+
- ✅ Mozilla Firefox 75+
- ✅ Safari 13+
- ✅ Microsoft Edge 80+

### الأجهزة المدعومة
- 💻 أجهزة الكمبيوتر المكتبية
- 💻 أجهزة الكمبيوتر المحمولة
- 📱 الهواتف الذكية
- 📱 الأجهزة اللوحية

## 🔧 التخصيص

### الإعدادات
- تخصيص معلومات المعمل
- إدارة أسعار التركيبات
- إعدادات الإشعارات
- تخصيص العملة والضرائب

### النسخ الاحتياطي
- تصدير البيانات بصيغة JSON
- استيراد البيانات من نسخة احتياطية
- جدولة النسخ الاحتياطية التلقائية

## 🛠️ التقنيات المستخدمة

### Frontend
- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتنسيق
- **JavaScript ES6+** - المنطق والتفاعل
- **Font Awesome** - الأيقونات
- **Google Fonts (Cairo)** - الخطوط العربية

### Backend
- **LocalStorage** - تخزين البيانات المحلي
- **Python HTTP Server** - الخادم المحلي

### المكتبات
- **Chart.js** - المخططات البيانية (قريباً)
- **jsPDF** - تصدير PDF (قريباً)

## 📞 الدعم والمساعدة

### بيانات تسجيل الدخول الافتراضية
- **اسم المستخدم:** dentallab
- **كلمة المرور:** dental2024

### الميزات القادمة
- 🔄 مزامنة البيانات السحابية
- 📧 إشعارات البريد الإلكتروني
- 📱 تطبيق الهاتف المحمول
- 🤖 الذكاء الاصطناعي للتنبؤات
- 📊 تحليلات متقدمة

## 📄 الترخيص

هذا النظام مطور خصيصاً لمعامل الأسنان ويمكن استخدامه وتعديله حسب الحاجة.

---

**نظام إدارة معمل الأسنان المتطور** - حلول تقنية متقدمة لإدارة معامل الأسنان 🦷✨
