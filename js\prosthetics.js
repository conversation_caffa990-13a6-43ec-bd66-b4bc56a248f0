/**
 * كلاس إدارة التركيبات
 * يوفر واجهة شاملة لإدارة التركيبات السنية
 */
class Prosthetics {

    // الحصول على بيانات التركيبات الافتراضية
    static getProstheticTypes() {
        return {
        porcelain: {
            name: 'بورسلين',
            items: [
                { name: 'بورسلين جي سرام', price: 350 },
                { name: 'بورسلين فيتا', price: 300 },
                { name: 'بورسلين فيس', price: 400 }
            ]
        },
        zircon: {
            name: 'زيركون',
            items: [
                { name: 'زيركون فل أناتومي', price: 600 },
                { name: 'زيركون كوبي + بورسلين', price: 450 },
                { name: 'زيركون أونلاي', price: 400 }
            ]
        },
        metal: {
            name: 'معدن',
            items: [
                { name: 'معدن عادي', price: 200 },
                { name: 'فيتاليوم', price: 250 }
            ]
        },
        orthodontic: {
            name: 'تقويم',
            items: [
                { name: 'Space maintainer', price: 160 },
                { name: 'Lingual arch', price: 200 },
                { name: 'Retainer فكين', price: 150 },
                { name: 'Night guard', price: 100 }
            ]
        }
        };
    }

    /**
     * عرض الواجهة الرئيسية
     */
    static render() {
        console.log('🎨 عرض واجهة إدارة التركيبات...');

        const pageContent = document.getElementById('pageContent');
        if (!pageContent) {
            console.error('❌ عنصر pageContent غير موجود');
            // محاولة أخرى بعد تأخير
            setTimeout(() => {
                const pageContent2 = document.getElementById('pageContent');
                if (pageContent2) {
                    this.renderContent(pageContent2);
                } else {
                    console.error('❌ فشل في العثور على عنصر pageContent حتى بعد التأخير');
                }
            }, 100);
            return;
        }

        this.renderContent(pageContent);
    }

    /**
     * عرض المحتوى في العنصر المحدد
     */
    static renderContent(pageContent) {
        pageContent.innerHTML = this.getMainHTML();

        // تهيئة الواجهة
        setTimeout(() => {
            this.initializeInterface();
            console.log('✅ تم تهيئة واجهة التركيبات بنجاح');
        }, 100);
    }

    /**
     * HTML الواجهة الرئيسية
     */
    static getMainHTML() {
        return `
            <div class="prosthetics-container">
                <!-- رأس الصفحة -->
                <div class="department-header">
                    <div class="header-main-content">
                        <div class="header-text-section">
                            <div class="department-logo-title">
                                <div class="department-logo">
                                    <i class="fas fa-tooth"></i>
                                </div>
                                <div class="title-text">
                                    <h1 class="department-title">إدارة التركيبات</h1>
                                    <p class="department-description">إدارة وحفظ التركيبات لجميع بيانات النظام</p>
                                </div>
                            </div>
                        </div>

                        <div class="download-section">
                            <button class="download-btn" onclick="Prosthetics.exportData()">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>

                    <!-- الإحصائيات تحت العنوان -->
                    <div class="header-stats-section">
                        <div class="stat-box total">
                            <div class="stat-icon">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="stat-value" id="totalWorks">0</div>
                            <div class="stat-name">إجمالي الأعمال</div>
                        </div>
                        <div class="stat-box pending">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-value" id="pendingWorks">0</div>
                            <div class="stat-name">قيد التنفيذ</div>
                        </div>
                        <div class="stat-box completed">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-value" id="completedWorks">0</div>
                            <div class="stat-name">مكتملة</div>
                        </div>
                        <div class="stat-box revenue">
                            <div class="stat-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="stat-value" id="totalRevenue">0</div>
                            <div class="stat-name">إجمالي الإيرادات</div>
                        </div>
                    </div>
                </div>

                <!-- شريط الأدوات -->
                <div class="toolbar">
                    <div class="toolbar-section">
                        <h2>أدوات الإدارة</h2>
                        <div class="toolbar-buttons">
                            <button class="tool-btn primary" onclick="Prosthetics.showNewWorkModal()">
                                <i class="fas fa-plus"></i>
                                <span>تسجيل عمل جديد</span>
                            </button>
                            <button class="tool-btn secondary" onclick="Prosthetics.showProstheticsListModal()">
                                <i class="fas fa-list"></i>
                                <span>قائمة التركيبات</span>
                            </button>
                            <button class="tool-btn info" onclick="Prosthetics.showReportsModal()">
                                <i class="fas fa-chart-bar"></i>
                                <span>التقارير</span>
                            </button>
                            <button class="tool-btn warning" onclick="Prosthetics.showScheduleModal()">
                                <i class="fas fa-calendar-alt"></i>
                                <span>الجدولة الزمنية</span>
                            </button>
                            <button class="tool-btn success" onclick="Prosthetics.exportData()">
                                <i class="fas fa-download"></i>
                                <span>تصدير البيانات</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- قائمة التركيبات -->
                <div class="prosthetics-list-container">
                    <div class="list-header">
                        <h2>قائمة التركيبات</h2>
                        <div class="list-filters">
                            <select id="statusFilter" onchange="Prosthetics.filterWorks()">
                                <option value="">جميع الحالات</option>
                                <option value="pending">قيد الانتظار</option>
                                <option value="progress">قيد التنفيذ</option>
                                <option value="completed">مكتملة</option>
                            </select>
                            <input type="text" id="searchInput" placeholder="البحث..." onkeyup="Prosthetics.searchWorks()">
                        </div>
                    </div>
                    <div class="prosthetics-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>رقم الحالة</th>
                                    <th>اسم المريض</th>
                                    <th>نوع التركيبة</th>
                                    <th>الطبيب</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                    <th>السعر</th>
                                    <th>الفاتورة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="prostheticsTableBody">
                                <!-- سيتم ملء البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * تهيئة الواجهة
     */
    static initializeInterface() {
        try {
            this.loadProstheticsData();
            this.updateStats();

            // تهيئة التأثيرات البسيطة والآمنة
            setTimeout(() => {
                if (typeof SimpleStatistics !== 'undefined') {
                    SimpleStatistics.init();
                }
            }, 100);
        } catch (error) {
            console.error('خطأ في تهيئة واجهة التركيبات:', error);
        }
    }

    /**
     * تحميل بيانات التركيبات
     */
    static loadProstheticsData() {
        let prosthetics = Database.getProsthetics() || [];

        // إضافة بيانات تجريبية إذا كانت القائمة فارغة
        if (prosthetics.length === 0) {
            this.addSampleData();
            prosthetics = Database.getProsthetics() || [];
        }

        this.renderProstheticsTable(prosthetics);
    }

    /**
     * إضافة بيانات تجريبية
     */
    static addSampleData() {
        const sampleProsthetics = [
            {
                id: Utils.generateId(),
                caseNumber: 'C001',
                patientName: 'أحمد محمد علي',
                type: 'بورسلين جي سرام',
                doctorName: 'د. محمد أحمد',
                status: 'completed',
                totalPrice: 350,
                createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                createdBy: 'admin'
            },
            {
                id: Utils.generateId(),
                caseNumber: 'C002',
                patientName: 'فاطمة حسن',
                type: 'زيركون فل أناتومي',
                doctorName: 'د. سارة محمود',
                status: 'progress',
                totalPrice: 600,
                createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
                createdBy: 'admin'
            },
            {
                id: Utils.generateId(),
                caseNumber: 'C003',
                patientName: 'محمد عبدالله',
                type: 'Space maintainer',
                doctorName: 'د. أحمد سالم',
                status: 'pending',
                totalPrice: 160,
                createdAt: new Date().toISOString(),
                createdBy: 'admin'
            }
        ];

        sampleProsthetics.forEach(prosthetic => {
            Database.addProsthetic(prosthetic);
        });
    }

    /**
     * عرض جدول التركيبات
     */
    static renderProstheticsTable(prosthetics) {
        const tbody = document.getElementById('prostheticsTableBody');
        if (!tbody) return;

        if (prosthetics.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="no-data">
                        <i class="fas fa-inbox"></i>
                        <p>لا توجد تركيبات مسجلة</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = prosthetics.map(prosthetic => `
            <tr>
                <td>${prosthetic.caseNumber}</td>
                <td>${prosthetic.patientName}</td>
                <td>${prosthetic.type}</td>
                <td>${prosthetic.doctorName}</td>
                <td>${new Date(prosthetic.createdAt).toLocaleDateString('ar-EG')}</td>
                <td>
                    <span class="status-badge status-${prosthetic.status}">
                        ${this.getStatusText(prosthetic.status)}
                    </span>
                </td>
                <td>${prosthetic.totalPrice} ج.م</td>
                <td>
                    <button class="btn-icon btn-invoice" onclick="Prosthetics.generateInvoice('${prosthetic.id}')" title="إنشاء فاتورة">
                        <i class="fas fa-file-invoice"></i>
                    </button>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon btn-edit" onclick="Prosthetics.editWork('${prosthetic.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon btn-delete" onclick="Prosthetics.deleteWork('${prosthetic.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * تحديث الإحصائيات
     */
    static updateStats() {
        const prosthetics = Database.getProsthetics() || [];
        
        const totalWorks = prosthetics.length;
        const pendingWorks = prosthetics.filter(p => p.status === 'pending' || p.status === 'progress').length;
        const completedWorks = prosthetics.filter(p => p.status === 'completed').length;
        const totalRevenue = prosthetics
            .filter(p => p.status === 'completed')
            .reduce((sum, p) => sum + (p.totalPrice || 0), 0);

        // تحديث العناصر بطريقة آمنة
        try {
            const totalWorksEl = document.getElementById('totalWorks');
            const pendingWorksEl = document.getElementById('pendingWorks');
            const completedWorksEl = document.getElementById('completedWorks');
            const totalRevenueEl = document.getElementById('totalRevenue');

            if (totalWorksEl) totalWorksEl.textContent = totalWorks;
            if (pendingWorksEl) pendingWorksEl.textContent = pendingWorks;
            if (completedWorksEl) completedWorksEl.textContent = completedWorks;
            if (totalRevenueEl) totalRevenueEl.textContent = totalRevenue.toLocaleString();

            // تطبيق التأثيرات البسيطة والآمنة
            if (typeof SimpleStatistics !== 'undefined' && SimpleStatistics.updateStats) {
                SimpleStatistics.updateStats({
                    totalWorks: totalWorks,
                    pendingWorks: pendingWorks,
                    completedWorks: completedWorks,
                    totalRevenue: totalRevenue
                });
            }
        } catch (error) {
            console.warn('خطأ في تحديث الإحصائيات:', error);
        }
    }

    /**
     * الحصول على نص الحالة
     */
    static getStatusText(status) {
        const statusTexts = {
            pending: 'قيد الانتظار',
            progress: 'قيد التنفيذ',
            completed: 'مكتملة'
        };
        return statusTexts[status] || status;
    }

    /**
     * فلترة الأعمال
     */
    static filterWorks() {
        const statusFilter = document.getElementById('statusFilter').value;
        const prosthetics = Database.getProsthetics() || [];
        
        let filteredProsthetics = prosthetics;
        if (statusFilter) {
            filteredProsthetics = prosthetics.filter(p => p.status === statusFilter);
        }
        
        this.renderProstheticsTable(filteredProsthetics);
    }

    /**
     * البحث في الأعمال
     */
    static searchWorks() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const prosthetics = Database.getProsthetics() || [];
        
        const filteredProsthetics = prosthetics.filter(p => 
            p.patientName.toLowerCase().includes(searchTerm) ||
            p.caseNumber.toLowerCase().includes(searchTerm) ||
            p.doctorName.toLowerCase().includes(searchTerm) ||
            p.type.toLowerCase().includes(searchTerm)
        );
        
        this.renderProstheticsTable(filteredProsthetics);
    }

    // === النوافذ المنبثقة ===

    /**
     * عرض نافذة تسجيل عمل جديد
     */
    static showNewWorkModal() {
        console.log('🔄 فتح نافذة تسجيل عمل جديد...');
        NewWorkManager.showModal();
    }

    /**
     * عرض نافذة قائمة الأعمال
     */
    static showWorksListModal() {
        console.log('🔄 فتح نافذة قائمة الأعمال...');

        const prosthetics = Database.getProsthetics() || [];
        const doctors = Database.getDoctors() || [];

        Utils.createModal({
            title: 'قائمة الأعمال',
            content: this.getWorksListHTML(prosthetics, doctors),
            size: 'extra-large',
            showCloseButton: true
        });

        // تهيئة الفلاتر بعد عرض النافذة
        setTimeout(() => {
            this.initializeWorksFilters();
        }, 100);
    }

    /**
     * HTML قائمة الأعمال الجديدة
     */
    static getWorksListHTML(prosthetics, doctors) {
        return `
            <div class="works-list-container">
                <!-- شريط الفلاتر -->
                <div class="filters-bar">
                    <div class="filter-group">
                        <label><i class="fas fa-user-md"></i> فلترة حسب الطبيب:</label>
                        <select id="doctorFilter" onchange="Prosthetics.filterWorksByDoctor()">
                            <option value="">جميع الأطباء</option>
                            ${doctors.map(doctor =>
                                `<option value="${doctor.name}">${doctor.name}</option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="filter-group">
                        <label><i class="fas fa-calendar"></i> فلترة حسب التاريخ:</label>
                        <input type="date" id="dateFilter" onchange="Prosthetics.filterWorksByDate()">
                    </div>
                    <div class="filter-group">
                        <label><i class="fas fa-tasks"></i> فلترة حسب الحالة:</label>
                        <select id="statusFilter" onchange="Prosthetics.filterWorksByStatus()">
                            <option value="">جميع الحالات</option>
                            <option value="pending">قيد الانتظار</option>
                            <option value="in-progress">قيد التنفيذ</option>
                            <option value="completed">مكتمل</option>
                            <option value="delivered">تم التسليم</option>
                        </select>
                    </div>
                    <div class="filter-actions">
                        <button class="btn-filter-reset" onclick="Prosthetics.resetFilters()">
                            <i class="fas fa-undo"></i> إعادة تعيين
                        </button>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="works-stats">
                    <div class="stat-card">
                        <i class="fas fa-clipboard-list"></i>
                        <span class="stat-number" id="totalWorks">${prosthetics.length}</span>
                        <span class="stat-label">إجمالي الأعمال</span>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-clock"></i>
                        <span class="stat-number" id="pendingWorks">${prosthetics.filter(p => p.status === 'pending').length}</span>
                        <span class="stat-label">قيد الانتظار</span>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-check-circle"></i>
                        <span class="stat-number" id="completedWorks">${prosthetics.filter(p => p.status === 'completed').length}</span>
                        <span class="stat-label">مكتمل</span>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-money-bill-wave"></i>
                        <span class="stat-number" id="totalRevenue">${prosthetics.reduce((sum, p) => sum + (p.finalPrice || p.totalPrice || 0), 0).toFixed(0)}</span>
                        <span class="stat-label">إجمالي الإيرادات</span>
                    </div>
                </div>

                ${prosthetics.length === 0 ? `
                    <div class="empty-list">
                        <i class="fas fa-inbox fa-3x"></i>
                        <h3>لا توجد أعمال مسجلة</h3>
                        <p>لم يتم تسجيل أي أعمال حتى الآن</p>
                        <button class="btn btn-primary" onclick="Utils.closeModal(); NewWorkManager.showModal();">
                            <i class="fas fa-plus"></i> إضافة عمل جديد
                        </button>
                    </div>
                ` : `
                    <!-- جدول الأعمال -->
                    <div class="works-table-container">
                        <table class="works-table" id="worksTable">
                            <thead>
                                <tr>
                                    <th>رقم الحالة</th>
                                    <th>اسم المريض</th>
                                    <th>اسم الطبيب</th>
                                    <th>نوع التركيبة</th>
                                    <th>عدد الأسنان</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="worksTableBody">
                                ${this.generateWorksTableRows(prosthetics)}
                            </tbody>
                        </table>
                    </div>
                `}
            </div>
        `;

    /**
     * إنشاء صفوف جدول الأعمال
     */
    static generateWorksTableRows(prosthetics) {
        return prosthetics.map(prosthetic => `
            <tr class="work-row" data-doctor="${prosthetic.doctorName}" data-status="${prosthetic.status}" data-date="${prosthetic.createdAt}">
                <td class="case-number">${prosthetic.caseNumber}</td>
                <td class="patient-name">${prosthetic.patientName}</td>
                <td class="doctor-name">${prosthetic.doctorName}</td>
                <td class="prosthetic-type">${prosthetic.prostheticType || prosthetic.type}</td>
                <td class="teeth-count">${prosthetic.selectedTeeth ? prosthetic.selectedTeeth.length : '-'}</td>
                <td class="total-price">${(prosthetic.finalPrice || prosthetic.totalPrice || 0).toFixed(0)} ريال</td>
                <td class="status">
                    <span class="status-badge status-${prosthetic.status}">
                        ${this.getStatusText(prosthetic.status)}
                    </span>
                </td>
                <td class="created-date">${new Date(prosthetic.createdAt).toLocaleDateString('ar-SA')}</td>
                <td class="actions">
                    <div class="action-buttons">
                        <button class="btn-icon btn-view" onclick="Prosthetics.viewProstheticDetails('${prosthetic.id}')" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-icon btn-edit" onclick="Prosthetics.editWork('${prosthetic.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon btn-invoice" onclick="Prosthetics.generateInvoice('${prosthetic.id}')" title="طباعة فاتورة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn-icon btn-delete" onclick="Prosthetics.deleteWork('${prosthetic.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * تهيئة فلاتر قائمة الأعمال
     */
    static initializeWorksFilters() {
        console.log('🔧 تهيئة فلاتر قائمة الأعمال...');
        // يمكن إضافة تهيئة إضافية هنا إذا لزم الأمر
    }

    /**
     * فلترة الأعمال حسب الطبيب
     */
    static filterWorksByDoctor() {
        const doctorFilter = document.getElementById('doctorFilter').value;
        const rows = document.querySelectorAll('.work-row');

        rows.forEach(row => {
            const doctorName = row.getAttribute('data-doctor');
            if (!doctorFilter || doctorName === doctorFilter) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });

        this.updateFilteredStats();
    }

    /**
     * فلترة الأعمال حسب التاريخ
     */
    static filterWorksByDate() {
        const dateFilter = document.getElementById('dateFilter').value;
        if (!dateFilter) return;

        const filterDate = new Date(dateFilter);
        const rows = document.querySelectorAll('.work-row');

        rows.forEach(row => {
            const workDate = new Date(row.getAttribute('data-date'));
            const isSameDate = workDate.toDateString() === filterDate.toDateString();

            if (isSameDate) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });

        this.updateFilteredStats();
    }

    /**
     * فلترة الأعمال حسب الحالة
     */
    static filterWorksByStatus() {
        const statusFilter = document.getElementById('statusFilter').value;
        const rows = document.querySelectorAll('.work-row');

        rows.forEach(row => {
            const status = row.getAttribute('data-status');
            if (!statusFilter || status === statusFilter) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });

        this.updateFilteredStats();
    }

    /**
     * إعادة تعيين جميع الفلاتر
     */
    static resetFilters() {
        document.getElementById('doctorFilter').value = '';
        document.getElementById('dateFilter').value = '';
        document.getElementById('statusFilter').value = '';

        const rows = document.querySelectorAll('.work-row');
        rows.forEach(row => {
            row.style.display = '';
        });

        this.updateFilteredStats();
    }

    /**
     * تحديث الإحصائيات المفلترة
     */
    static updateFilteredStats() {
        const visibleRows = document.querySelectorAll('.work-row[style=""], .work-row:not([style])');
        const totalVisible = visibleRows.length;

        // تحديث العدد المعروض
        document.getElementById('totalWorks').textContent = totalVisible;

        // حساب الإحصائيات للصفوف المرئية
        let pendingCount = 0;
        let completedCount = 0;
        let totalRevenue = 0;

        visibleRows.forEach(row => {
            const status = row.getAttribute('data-status');
            if (status === 'pending') pendingCount++;
            if (status === 'completed') completedCount++;

            const priceText = row.querySelector('.total-price').textContent;
            const price = parseFloat(priceText.replace(/[^\d.]/g, '')) || 0;
            totalRevenue += price;
        });

        document.getElementById('pendingWorks').textContent = pendingCount;
        document.getElementById('completedWorks').textContent = completedCount;
        document.getElementById('totalRevenue').textContent = totalRevenue.toFixed(0);
    }

    /**
     * تعديل عمل
     */
    static editWork(workId) {
        console.log('✏️ تعديل العمل:', workId);
        const prosthetic = Database.getProsthetics().find(p => p.id === workId);
        if (!prosthetic) {
            alert('❌ لم يتم العثور على العمل');
            return;
        }

        // إغلاق النافذة الحالية وفتح نافذة التعديل
        Utils.closeModal();
        setTimeout(() => {
            NewWorkManager.showEditModal(prosthetic);
        }, 300);
    }

    /**
     * حذف عمل
     */
    static deleteWork(workId) {
        console.log('🗑️ حذف العمل:', workId);
        const prosthetic = Database.getProsthetics().find(p => p.id === workId);
        if (!prosthetic) {
            alert('❌ لم يتم العثور على العمل');
            return;
        }

        const confirmed = confirm(`⚠️ هل أنت متأكد من حذف العمل؟\n\nرقم الحالة: ${prosthetic.caseNumber}\nالمريض: ${prosthetic.patientName}\nالطبيب: ${prosthetic.doctorName}`);

        if (confirmed) {
            Database.deleteProsthetic(workId);
            Utils.showNotification('تم حذف العمل بنجاح', 'success');

            // إعادة تحميل النافذة
            this.showWorksListModal();
        }
    }

    /**
     * إنشاء فاتورة للعمل
     */
    static generateInvoice(workId) {
        console.log('🧾 إنشاء فاتورة للعمل:', workId);
        const prosthetic = Database.getProsthetics().find(p => p.id === workId);
        if (!prosthetic) {
            alert('❌ لم يتم العثور على العمل');
            return;
        }

        // فتح نافذة الطباعة
        const invoiceWindow = window.open('', '_blank', 'width=800,height=600');
        const invoiceHTML = this.generateInvoiceHTML(prosthetic);

        invoiceWindow.document.open();
        invoiceWindow.document.write(invoiceHTML);
        invoiceWindow.document.close();

        // طباعة الفاتورة تلقائياً
        setTimeout(() => {
            invoiceWindow.print();
        }, 500);
    }

    /**
     * تحديث زر قائمة التركيبات في الواجهة الرئيسية
     */
    static updateProstheticsListButton() {
        // البحث عن الزر في الواجهة الرئيسية وتحديثه
        const listButton = document.querySelector('button[onclick*="showProstheticsListModal"]');
        if (listButton) {
            listButton.setAttribute('onclick', 'Prosthetics.showWorksListModal()');
            listButton.innerHTML = '<i class="fas fa-list"></i> قائمة الأعمال';
        }
    }
}

// تصدير الكلاس للاستخدام العام
window.Prosthetics = Prosthetics;

console.log('✅ تم تحميل كلاس إدارة التركيبات بنجاح');
                background: linear-gradient(135deg, #f8fafc 0%, #e5e7eb 100%);
                border-radius: 10px;
                border: 1px solid #d1d5db;
            }

            .list-stats {
                display: flex;
                gap: 20px;
            }

            .stat-item {
                text-align: center;
                padding: 10px 15px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .stat-number {
                display: block;
                font-size: 1.5rem;
                font-weight: bold;
                color: #3b82f6;
            }

            .stat-label {
                font-size: 0.85rem;
                color: #6b7280;
            }

            .list-actions {
                display: flex;
                gap: 10px;
            }

            .table-container {
                max-height: 60vh;
                overflow-y: auto;
                border-radius: 10px;
                border: 1px solid #d1d5db;
            }

            .prosthetics-table {
                width: 100%;
                border-collapse: collapse;
                background: white;
            }

            .prosthetics-table th {
                background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
                color: white;
                padding: 12px 8px;
                text-align: center;
                font-weight: 600;
                position: sticky;
                top: 0;
                z-index: 10;
            }

            .prosthetics-table td {
                padding: 10px 8px;
                text-align: center;
                border-bottom: 1px solid #e5e7eb;
                font-size: 0.9rem;
            }

            .prosthetics-table tr:hover {
                background: #f8fafc;
            }

            .status-badge {
                padding: 4px 12px;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: 500;
            }

            .status-pending {
                background: #fef3c7;
                color: #92400e;
            }

            .status-progress {
                background: #dbeafe;
                color: #1e40af;
            }

            .status-completed {
                background: #d1fae5;
                color: #065f46;
            }

            .action-buttons {
                display: flex;
                gap: 5px;
                justify-content: center;
            }

            .btn-icon {
                width: 32px;
                height: 32px;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease;
            }

            .btn-view {
                background: #3b82f6;
                color: white;
            }

            .btn-edit {
                background: #f59e0b;
                color: white;
            }

            .btn-invoice {
                background: #10b981;
                color: white;
            }

            .btn-delete {
                background: #ef4444;
                color: white;
            }

            .btn-icon:hover {
                transform: scale(1.1);
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            }

            .empty-list {
                text-align: center;
                padding: 60px 20px;
                color: #6b7280;
            }

            .empty-list i {
                color: #d1d5db;
                margin-bottom: 20px;
            }

            .empty-list h3 {
                margin: 20px 0 10px 0;
                color: #374151;
            }

            @media (max-width: 768px) {
                .list-header {
                    flex-direction: column;
                    gap: 15px;
                }

                .list-stats {
                    flex-wrap: wrap;
                    justify-content: center;
                }

                .prosthetics-table {
                    font-size: 0.8rem;
                }

                .prosthetics-table th,
                .prosthetics-table td {
                    padding: 8px 4px;
                }
            }
            </style>
        `;
    }

    /**
     * عرض نافذة التقارير
     */
    static showReportsModal() {
        console.log('🔄 فتح نافذة التقارير...');
        Utils.showNotification('سيتم تطوير نافذة التقارير قريباً', 'info');
    }

    /**
     * عرض نافذة الجدولة الزمنية
     */
    static showScheduleModal() {
        console.log('🔄 فتح نافذة الجدولة الزمنية...');
        Utils.showNotification('سيتم تطوير نافذة الجدولة الزمنية قريباً', 'info');
    }

    /**
     * تصدير البيانات
     */
    static exportData() {
        console.log('🔄 تصدير البيانات...');
        const prosthetics = Database.getProsthetics() || [];

        if (prosthetics.length === 0) {
            Utils.showNotification('لا توجد بيانات للتصدير', 'warning');
            return;
        }

        // تحويل البيانات إلى CSV
        const csvContent = this.convertToCSV(prosthetics);
        this.downloadCSV(csvContent, 'prosthetics_data.csv');
        Utils.showNotification('تم تصدير البيانات بنجاح', 'success');
    }

    /**
     * تحويل البيانات إلى CSV
     */
    static convertToCSV(data) {
        const headers = ['رقم الحالة', 'اسم المريض', 'نوع التركيبة', 'الطبيب', 'التاريخ', 'الحالة', 'السعر'];
        const csvRows = [headers.join(',')];

        data.forEach(item => {
            const row = [
                item.caseNumber,
                item.patientName,
                item.type,
                item.doctorName,
                new Date(item.createdAt).toLocaleDateString('ar-EG'),
                this.getStatusText(item.status),
                item.totalPrice
            ];
            csvRows.push(row.join(','));
        });

        return csvRows.join('\n');
    }

    /**
     * تحميل ملف CSV
     */
    static downloadCSV(csvContent, filename) {
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    /**
     * إنشاء فاتورة للتركيبة
     */
    static generateInvoice(id) {
        console.log('🔄 إنشاء فاتورة للتركيبة:', id);
        const prosthetic = Database.getProstheticById(id);

        if (!prosthetic) {
            Utils.showNotification('لم يتم العثور على التركيبة', 'error');
            return;
        }

        // إنشاء نافذة الفاتورة
        const invoiceWindow = window.open('', '_blank', 'width=800,height=600');
        const invoiceHTML = this.generateInvoiceHTML(prosthetic);

        invoiceWindow.document.write(invoiceHTML);
        invoiceWindow.document.close();

        // طباعة الفاتورة تلقائياً
        setTimeout(() => {
            invoiceWindow.print();
        }, 500);
    }

    /**
     * إنشاء HTML للفاتورة
     */
    static generateInvoiceHTML(prosthetic) {
        const currentDate = new Date().toLocaleDateString('ar-EG');

        return `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>فاتورة - ${prosthetic.caseNumber}</title>
                <style>
                    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; }
                    .invoice-header { text-align: center; border-bottom: 2px solid #3b82f6; padding-bottom: 20px; margin-bottom: 30px; }
                    .invoice-title { color: #3b82f6; font-size: 2rem; margin-bottom: 10px; }
                    .invoice-details { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px; }
                    .detail-section { background: #f8fafc; padding: 15px; border-radius: 8px; }
                    .detail-title { font-weight: bold; color: #1f2937; margin-bottom: 10px; }
                    .detail-item { margin-bottom: 8px; }
                    .invoice-total { text-align: center; background: #3b82f6; color: white; padding: 20px; border-radius: 8px; font-size: 1.5rem; }
                    @media print { body { margin: 0; } }
                </style>
            </head>
            <body>
                <div class="invoice-header">
                    <h1 class="invoice-title">فاتورة تركيبة سنية</h1>
                    <p>رقم الحالة: ${prosthetic.caseNumber}</p>
                    <p>تاريخ الإصدار: ${currentDate}</p>
                </div>

                <div class="invoice-details">
                    <div class="detail-section">
                        <div class="detail-title">بيانات المريض</div>
                        <div class="detail-item"><strong>الاسم:</strong> ${prosthetic.patientName}</div>
                        <div class="detail-item"><strong>رقم الحالة:</strong> ${prosthetic.caseNumber}</div>
                    </div>

                    <div class="detail-section">
                        <div class="detail-title">بيانات التركيبة</div>
                        <div class="detail-item"><strong>نوع التركيبة:</strong> ${prosthetic.type}</div>
                        <div class="detail-item"><strong>الطبيب المعالج:</strong> ${prosthetic.doctorName}</div>
                        <div class="detail-item"><strong>تاريخ التسجيل:</strong> ${new Date(prosthetic.createdAt).toLocaleDateString('ar-EG')}</div>
                        <div class="detail-item"><strong>الحالة:</strong> ${this.getStatusText(prosthetic.status)}</div>
                    </div>
                </div>

                <div class="invoice-total">
                    <strong>إجمالي المبلغ: ${prosthetic.totalPrice} جنيه مصري</strong>
                </div>

                <div style="margin-top: 40px; text-align: center; color: #6b7280;">
                    <p>شكراً لثقتكم بنا</p>
                </div>
            </body>
            </html>
        `;
    }

    /**
     * عرض تفاصيل التركيبة
     */
    static viewProstheticDetails(id) {
        console.log('🔄 عرض تفاصيل التركيبة:', id);
        const prosthetic = Database.getProstheticById(id);

        if (!prosthetic) {
            Utils.showNotification('لم يتم العثور على التركيبة', 'error');
            return;
        }

        Utils.createModal({
            title: `تفاصيل التركيبة - ${prosthetic.caseNumber}`,
            content: this.getProstheticDetailsHTML(prosthetic),
            size: 'large',
            showCloseButton: true
        });
    }

    /**
     * HTML تفاصيل التركيبة
     */
    static getProstheticDetailsHTML(prosthetic) {
        const selectedTeethText = prosthetic.selectedTeeth ? 
            prosthetic.selectedTeeth.map(tooth => {
                const jawText = tooth.jaw === 'upper' ? 'علوي' : 'سفلي';
                const sideText = tooth.side === 'right' ? 'يمين' : 'يسار';
                return `${jawText} ${sideText} ${tooth.num}`;
            }).join(', ') : 'غير محدد';

        return `
            <div class="prosthetic-details">
                <div class="details-header">
                    <div class="case-info">
                        <h3>${prosthetic.caseNumber}</h3>
                        <span class="status-badge status-${prosthetic.status}">
                            ${this.getStatusText(prosthetic.status)}
                        </span>
                    </div>
                    <div class="creation-date">
                        تاريخ الإنشاء: ${new Date(prosthetic.createdAt).toLocaleDateString('ar-SA')}
                    </div>
                </div>

                <div class="details-grid">
                    <div class="detail-section">
                        <h4><i class="fas fa-user"></i> معلومات المريض</h4>
                        <div class="detail-item">
                            <span class="label">اسم المريض:</span>
                            <span class="value">${prosthetic.patientName}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">الطبيب المعالج:</span>
                            <span class="value">${prosthetic.doctorName}</span>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h4><i class="fas fa-tooth"></i> تفاصيل التركيبة</h4>
                        <div class="detail-item">
                            <span class="label">نوع التركيبة:</span>
                            <span class="value">${prosthetic.prostheticType || prosthetic.type}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">اللون:</span>
                            <span class="value">${prosthetic.color || 'غير محدد'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">سعر الوحدة:</span>
                            <span class="value">${prosthetic.unitPrice || 0} ريال</span>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h4><i class="fas fa-teeth"></i> الأسنان المختارة</h4>
                        <div class="selected-teeth-display">
                            ${selectedTeethText}
                        </div>
                        <div class="detail-item">
                            <span class="label">عدد الأسنان:</span>
                            <span class="value">${prosthetic.selectedTeeth ? prosthetic.selectedTeeth.length : 0}</span>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h4><i class="fas fa-calculator"></i> التكلفة</h4>
                        <div class="detail-item">
                            <span class="label">السعر الإجمالي:</span>
                            <span class="value">${prosthetic.totalPrice || 0} ريال</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">الخصم:</span>
                            <span class="value">${prosthetic.discount || 0} ريال</span>
                        </div>
                        <div class="detail-item final-price">
                            <span class="label">السعر النهائي:</span>
                            <span class="value">${prosthetic.finalPrice || prosthetic.totalPrice || 0} ريال</span>
                        </div>
                    </div>
                </div>

                <div class="details-actions">
                    <button class="btn btn-primary" onclick="Prosthetics.editWork('${prosthetic.id}')">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-success" onclick="Prosthetics.generateInvoice('${prosthetic.id}')">
                        <i class="fas fa-print"></i> طباعة فاتورة
                    </button>
                    <button class="btn btn-danger" onclick="Prosthetics.deleteWork('${prosthetic.id}')">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>

            <style>
            .prosthetic-details {
                max-width: 800px;
                width: 100%;
            }

            .details-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 25px;
                padding: 20px;
                background: linear-gradient(135deg, #f8fafc 0%, #e5e7eb 100%);
                border-radius: 10px;
                border: 1px solid #d1d5db;
            }

            .case-info {
                display: flex;
                align-items: center;
                gap: 15px;
            }

            .case-info h3 {
                margin: 0;
                color: #1f2937;
                font-size: 1.5rem;
            }

            .creation-date {
                color: #6b7280;
                font-size: 0.9rem;
            }

            .details-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-bottom: 25px;
            }

            .detail-section {
                background: white;
                border-radius: 10px;
                padding: 20px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                border: 1px solid #e5e7eb;
            }

            .detail-section h4 {
                margin: 0 0 15px 0;
                color: #374151;
                font-size: 1.1rem;
                display: flex;
                align-items: center;
                gap: 8px;
                padding-bottom: 10px;
                border-bottom: 2px solid #e5e7eb;
            }

            .detail-section h4 i {
                color: #3b82f6;
            }

            .detail-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid #f3f4f6;
            }

            .detail-item:last-child {
                border-bottom: none;
            }

            .detail-item .label {
                font-weight: 500;
                color: #6b7280;
            }

            .detail-item .value {
                font-weight: 600;
                color: #1f2937;
            }

            .final-price {
                background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
                border-radius: 8px;
                padding: 12px !important;
                margin-top: 10px;
            }

            .final-price .value {
                color: #0369a1;
                font-size: 1.1rem;
            }

            .selected-teeth-display {
                background: #f8fafc;
                border-radius: 8px;
                padding: 12px;
                margin-bottom: 10px;
                border: 1px solid #e5e7eb;
                font-size: 0.9rem;
                color: #374151;
                line-height: 1.5;
            }

            .details-actions {
                display: flex;
                gap: 10px;
                justify-content: center;
                padding-top: 20px;
                border-top: 1px solid #e5e7eb;
            }

            .details-actions .btn {
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .btn-primary {
                background: #3b82f6;
                color: white;
            }

            .btn-success {
                background: #10b981;
                color: white;
            }

            .btn-danger {
                background: #ef4444;
                color: white;
            }

            .details-actions .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            }

            @media (max-width: 768px) {
                .details-header {
                    flex-direction: column;
                    gap: 10px;
                    text-align: center;
                }

                .details-grid {
                    grid-template-columns: 1fr;
                }

                .details-actions {
                    flex-direction: column;
                }

                .details-actions .btn {
                    width: 100%;
                    justify-content: center;
                }
            }
            </style>
        `;
    }

    /**
     * الحصول على نص الحالة
     */
    static getStatusText(status) {
        const statusMap = {
            'pending': 'في الانتظار',
            'progress': 'قيد التنفيذ',
            'completed': 'مكتملة',
            'cancelled': 'ملغية'
        };
        return statusMap[status] || 'غير محدد';
    }

    /**
     * تعديل عمل
     */
    static editWork(id) {
        console.log('🔄 تعديل العمل:', id);
        // سيتم تطوير هذه الدالة لاحقاً
        Utils.showNotification('سيتم تطوير وظيفة التعديل قريباً', 'info');
    }

    /**
     * تصدير البيانات
     */
    static exportData() {
        console.log('🔄 تصدير البيانات...');
        const prosthetics = Database.getProsthetics() || [];
        
        if (prosthetics.length === 0) {
            Utils.showNotification('لا توجد بيانات للتصدير', 'warning');
            return;
        }

        // تحويل البيانات إلى CSV
        const headers = ['رقم الحالة', 'اسم المريض', 'الطبيب', 'نوع التركيبة', 'اللون', 'عدد الأسنان', 'السعر النهائي', 'الحالة', 'التاريخ'];
        const csvContent = [
            headers.join(','),
            ...prosthetics.map(p => [
                p.caseNumber,
                p.patientName,
                p.doctorName,
                p.prostheticType || p.type,
                p.color || '',
                p.selectedTeeth ? p.selectedTeeth.length : 0,
                p.finalPrice || p.totalPrice || 0,
                this.getStatusText(p.status),
                new Date(p.createdAt).toLocaleDateString('ar-SA')
            ].join(','))
        ].join('\n');

        // تنزيل الملف
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `prosthetics_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        Utils.showNotification('تم تصدير البيانات بنجاح', 'success');
    }

    /**
     * حذف عمل
     */
    static deleteWork(id) {
        if (confirm('هل أنت متأكد من حذف هذا العمل؟')) {
            Database.deleteProsthetic(id);
            this.loadProstheticsData();
            this.updateStats();
            Utils.showNotification('تم حذف العمل بنجاح', 'success');
        }
    }
}

// تصدير الكلاس للاستخدام العام
window.Prosthetics = Prosthetics;

console.log('✅ تم تحميل كلاس إدارة التركيبات بنجاح');
