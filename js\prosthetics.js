/**
 * كلاس إدارة التركيبات
 * يوفر واجهة شاملة لإدارة التركيبات السنية
 */
class Prosthetics {

    // الحصول على بيانات التركيبات الافتراضية
    static getProstheticTypes() {
        return {
        porcelain: {
            name: 'بورسلين',
            items: [
                { name: 'بورسلين جي سرام', price: 350 },
                { name: 'بورسلين فيتا', price: 300 },
                { name: 'بورسلين فيس', price: 400 }
            ]
        },
        zircon: {
            name: 'زيركون',
            items: [
                { name: 'زيركون فل أناتومي', price: 600 },
                { name: 'زيركون كوبي + بورسلين', price: 450 },
                { name: 'زيركون أونلاي', price: 400 }
            ]
        },
        metal: {
            name: 'معدن',
            items: [
                { name: 'معدن عادي', price: 200 },
                { name: 'فيتاليوم', price: 250 }
            ]
        },
        orthodontic: {
            name: 'تقويم',
            items: [
                { name: 'Space maintainer', price: 160 },
                { name: 'Lingual arch', price: 200 },
                { name: 'Retainer فكين', price: 150 },
                { name: 'Night guard', price: 100 }
            ]
        }
        };
    }

    /**
     * عرض الواجهة الرئيسية
     */
    static render() {
        console.log('🎨 عرض واجهة إدارة التركيبات...');

        const pageContent = document.getElementById('pageContent');
        if (!pageContent) {
            console.error('❌ عنصر pageContent غير موجود');
            // محاولة أخرى بعد تأخير
            setTimeout(() => {
                const pageContent2 = document.getElementById('pageContent');
                if (pageContent2) {
                    this.renderContent(pageContent2);
                } else {
                    console.error('❌ فشل في العثور على عنصر pageContent حتى بعد التأخير');
                }
            }, 100);
            return;
        }

        this.renderContent(pageContent);
    }

    /**
     * عرض المحتوى في العنصر المحدد
     */
    static renderContent(pageContent) {
        pageContent.innerHTML = this.getMainHTML();

        // تهيئة الواجهة
        setTimeout(() => {
            this.initializeInterface();
            console.log('✅ تم تهيئة واجهة التركيبات بنجاح');
        }, 100);
    }

    /**
     * HTML الواجهة الرئيسية
     */
    static getMainHTML() {
        return `
            <div class="prosthetics-container">
                <!-- رأس الصفحة -->
                <div class="department-header">
                    <div class="header-main-content">
                        <div class="header-text-section">
                            <div class="department-logo-title">
                                <div class="department-logo">
                                    <i class="fas fa-tooth"></i>
                                </div>
                                <div class="title-text">
                                    <h1 class="department-title">إدارة التركيبات</h1>
                                    <p class="department-description">إدارة وحفظ التركيبات لجميع بيانات النظام</p>
                                </div>
                            </div>
                        </div>

                        <div class="download-section">
                            <button class="download-btn" onclick="Prosthetics.exportData()">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>

                    <!-- الإحصائيات تحت العنوان -->
                    <div class="header-stats-section">
                        <div class="stat-box total">
                            <div class="stat-icon">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="stat-value" id="totalWorks">0</div>
                            <div class="stat-name">إجمالي الأعمال</div>
                        </div>
                        <div class="stat-box pending">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-value" id="pendingWorks">0</div>
                            <div class="stat-name">قيد التنفيذ</div>
                        </div>
                        <div class="stat-box completed">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-value" id="completedWorks">0</div>
                            <div class="stat-name">مكتملة</div>
                        </div>
                        <div class="stat-box revenue">
                            <div class="stat-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="stat-value" id="totalRevenue">0</div>
                            <div class="stat-name">إجمالي الإيرادات</div>
                        </div>
                    </div>
                </div>

                <!-- شريط الأدوات -->
                <div class="toolbar">
                    <div class="toolbar-section">
                        <h2>أدوات الإدارة</h2>
                        <div class="toolbar-buttons">
                            <button class="tool-btn primary" onclick="Prosthetics.showNewWorkModal()">
                                <i class="fas fa-plus"></i>
                                <span>تسجيل عمل جديد</span>
                            </button>
                            <button class="tool-btn secondary" onclick="Prosthetics.showWorksListModal()">
                                <i class="fas fa-list"></i>
                                <span>قائمة الأعمال</span>
                            </button>
                            <button class="tool-btn info" onclick="Prosthetics.showReportsModal()">
                                <i class="fas fa-chart-bar"></i>
                                <span>التقارير</span>
                            </button>
                            <button class="tool-btn warning" onclick="Prosthetics.showScheduleModal()">
                                <i class="fas fa-calendar-alt"></i>
                                <span>الجدولة الزمنية</span>
                            </button>
                            <button class="tool-btn success" onclick="Prosthetics.exportData()">
                                <i class="fas fa-download"></i>
                                <span>تصدير البيانات</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- الأعمال الحديثة -->
                <div class="recent-works">
                    <div class="section-header">
                        <h2><i class="fas fa-history"></i> الأعمال الحديثة</h2>
                        <button class="btn-view-all" onclick="Prosthetics.showWorksListModal()">
                            عرض الكل <i class="fas fa-arrow-left"></i>
                        </button>
                    </div>
                    <div class="recent-works-list" id="recentWorksList">
                        <!-- سيتم تحميل الأعمال الحديثة هنا -->
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * تهيئة الواجهة
     */
    static initializeInterface() {
        try {
            this.loadStats();
            this.loadRecentWorks();

            // تهيئة التأثيرات البسيطة والآمنة
            setTimeout(() => {
                if (typeof SimpleStatistics !== 'undefined') {
                    SimpleStatistics.init();
                }
            }, 100);
        } catch (error) {
            console.error('خطأ في تهيئة واجهة التركيبات:', error);
        }
    }

    /**
     * تحميل الإحصائيات
     */
    static loadStats() {
        let prosthetics = Database.getProsthetics() || [];

        // إضافة بيانات تجريبية إذا كانت القائمة فارغة
        if (prosthetics.length === 0) {
            this.addSampleData();
            prosthetics = Database.getProsthetics() || [];
        }

        const totalWorks = prosthetics.length;
        const pendingWorks = prosthetics.filter(p => p.status === 'pending' || p.status === 'in-progress').length;
        const completedWorks = prosthetics.filter(p => p.status === 'completed').length;
        const totalRevenue = prosthetics.reduce((sum, p) => sum + (p.finalPrice || p.totalPrice || 0), 0);

        const totalWorksEl = document.getElementById('totalWorks');
        const pendingWorksEl = document.getElementById('pendingWorks');
        const completedWorksEl = document.getElementById('completedWorks');
        const totalRevenueEl = document.getElementById('totalRevenue');

        if (totalWorksEl) totalWorksEl.textContent = totalWorks;
        if (pendingWorksEl) pendingWorksEl.textContent = pendingWorks;
        if (completedWorksEl) completedWorksEl.textContent = completedWorks;
        if (totalRevenueEl) totalRevenueEl.textContent = totalRevenue.toFixed(0);
    }

    /**
     * إضافة بيانات تجريبية
     */
    static addSampleData() {
        const sampleProsthetics = [
            {
                id: Utils.generateId(),
                caseNumber: 'C001',
                patientName: 'أحمد محمد علي',
                prostheticType: 'بورسلين جي سرام',
                type: 'بورسلين جي سرام',
                doctorName: 'د. محمد أحمد',
                status: 'completed',
                totalPrice: 350,
                finalPrice: 350,
                selectedTeeth: [{jaw: 'upper', side: 'right', num: 1}],
                createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                createdBy: 'admin'
            },
            {
                id: Utils.generateId(),
                caseNumber: 'C002',
                patientName: 'فاطمة حسن',
                prostheticType: 'زيركون فل أناتومي',
                type: 'زيركون فل أناتومي',
                doctorName: 'د. سارة محمود',
                status: 'in-progress',
                totalPrice: 600,
                finalPrice: 600,
                selectedTeeth: [{jaw: 'upper', side: 'left', num: 2}, {jaw: 'upper', side: 'left', num: 3}],
                createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
                createdBy: 'admin'
            },
            {
                id: Utils.generateId(),
                caseNumber: 'C003',
                patientName: 'محمد عبدالله',
                prostheticType: 'Space maintainer',
                type: 'Space maintainer',
                doctorName: 'د. أحمد سالم',
                status: 'pending',
                totalPrice: 160,
                finalPrice: 160,
                selectedTeeth: [{jaw: 'lower', side: 'right', num: 4}],
                createdAt: new Date().toISOString(),
                createdBy: 'admin'
            }
        ];

        sampleProsthetics.forEach(prosthetic => {
            Database.addProsthetic(prosthetic);
        });
    }

    /**
     * تحميل الأعمال الحديثة
     */
    static loadRecentWorks() {
        const prosthetics = Database.getProsthetics() || [];
        const recentWorks = prosthetics
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        const recentWorksList = document.getElementById('recentWorksList');
        if (!recentWorksList) return;

        if (recentWorks.length === 0) {
            recentWorksList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <p>لا توجد أعمال حديثة</p>
                    <button class="btn-primary" onclick="NewWorkManager.showModal()">
                        إضافة عمل جديد
                    </button>
                </div>
            `;
            return;
        }

        recentWorksList.innerHTML = recentWorks.map(work => `
            <div class="recent-work-item">
                <div class="work-info">
                    <div class="work-header">
                        <span class="case-number">${work.caseNumber}</span>
                        <span class="status-badge status-${work.status}">
                            ${this.getStatusText(work.status)}
                        </span>
                    </div>
                    <div class="work-details">
                        <span class="patient-name">${work.patientName}</span>
                        <span class="doctor-name">د. ${work.doctorName}</span>
                        <span class="prosthetic-type">${work.prostheticType || work.type}</span>
                    </div>
                    <div class="work-meta">
                        <span class="price">${(work.finalPrice || work.totalPrice || 0).toFixed(0)} ريال</span>
                        <span class="date">${new Date(work.createdAt).toLocaleDateString('ar-SA')}</span>
                    </div>
                </div>
                <div class="work-actions">
                    <button class="btn-icon btn-edit" onclick="Prosthetics.editWork('${work.id}')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon btn-invoice" onclick="Prosthetics.generateInvoice('${work.id}')" title="فاتورة">
                        <i class="fas fa-file-invoice"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }







    /**
     * تحديث الإحصائيات
     */
    static updateStats() {
        const prosthetics = Database.getProsthetics() || [];
        
        const totalWorks = prosthetics.length;
        const pendingWorks = prosthetics.filter(p => p.status === 'pending' || p.status === 'progress').length;
        const completedWorks = prosthetics.filter(p => p.status === 'completed').length;
        const totalRevenue = prosthetics
            .filter(p => p.status === 'completed')
            .reduce((sum, p) => sum + (p.totalPrice || 0), 0);

        // تحديث العناصر بطريقة آمنة
        try {
            const totalWorksEl = document.getElementById('totalWorks');
            const pendingWorksEl = document.getElementById('pendingWorks');
            const completedWorksEl = document.getElementById('completedWorks');
            const totalRevenueEl = document.getElementById('totalRevenue');

            if (totalWorksEl) totalWorksEl.textContent = totalWorks;
            if (pendingWorksEl) pendingWorksEl.textContent = pendingWorks;
            if (completedWorksEl) completedWorksEl.textContent = completedWorks;
            if (totalRevenueEl) totalRevenueEl.textContent = totalRevenue.toLocaleString();

            // تطبيق التأثيرات البسيطة والآمنة
            if (typeof SimpleStatistics !== 'undefined' && SimpleStatistics.updateStats) {
                SimpleStatistics.updateStats({
                    totalWorks: totalWorks,
                    pendingWorks: pendingWorks,
                    completedWorks: completedWorks,
                    totalRevenue: totalRevenue
                });
            }
        } catch (error) {
            console.warn('خطأ في تحديث الإحصائيات:', error);
        }
    }

    /**
     * الحصول على نص الحالة
     */
    static getStatusText(status) {
        const statusTexts = {
            pending: 'قيد الانتظار',
            progress: 'قيد التنفيذ',
            completed: 'مكتملة'
        };
        return statusTexts[status] || status;
    }



    // === النوافذ المنبثقة ===

    /**
     * عرض نافذة تسجيل عمل جديد
     */
    static showNewWorkModal() {
        console.log('🔄 فتح نافذة تسجيل عمل جديد...');
        NewWorkManager.showModal();
    }

    /**
     * عرض نافذة قائمة الأعمال
     */
    static showWorksListModal() {
        console.log('🔄 فتح نافذة قائمة الأعمال...');

        // التحقق من وجود Utils
        if (typeof Utils === 'undefined' || typeof Utils.createModal !== 'function') {
            console.error('❌ Utils.createModal غير متاح');
            alert('خطأ: لا يمكن فتح النافذة. Utils غير متاح.');
            return;
        }

        const prosthetics = Database.getProsthetics() || [];
        const doctors = Database.getDoctors() || [];

        console.log('📊 عدد الأعمال:', prosthetics.length);
        console.log('👨‍⚕️ عدد الأطباء:', doctors.length);

        try {
            const content = this.getWorksListHTML(prosthetics, doctors);
            console.log('📝 تم إنشاء محتوى النافذة بنجاح');

            Utils.createModal({
                title: 'قائمة الأعمال',
                content: content,
                size: 'extra-large',
                showCloseButton: true
            });

            console.log('✅ تم استدعاء Utils.createModal');

            // تهيئة الفلاتر بعد عرض النافذة
            setTimeout(() => {
                this.initializeWorksFilters();
            }, 100);
        } catch (error) {
            console.error('❌ خطأ في فتح النافذة:', error);
            alert('خطأ في فتح النافذة: ' + error.message);
        }
    }

    /**
     * HTML قائمة الأعمال
     */
    static getWorksListHTML(prosthetics, doctors) {
        return `
            <div class="works-list-container">
                <!-- شريط الفلاتر -->
                <div class="filters-bar">
                    <div class="filter-group">
                        <label><i class="fas fa-user-md"></i> فلترة حسب الطبيب:</label>
                        <select id="doctorFilter" onchange="Prosthetics.filterWorksByDoctor()">
                            <option value="">جميع الأطباء</option>
                            ${doctors.map(doctor =>
                                `<option value="${doctor.name}">${doctor.name}</option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="filter-group">
                        <label><i class="fas fa-calendar"></i> فلترة حسب التاريخ:</label>
                        <input type="date" id="dateFilter" onchange="Prosthetics.filterWorksByDate()">
                    </div>
                    <div class="filter-group">
                        <label><i class="fas fa-tasks"></i> فلترة حسب الحالة:</label>
                        <select id="statusFilter" onchange="Prosthetics.filterWorksByStatus()">
                            <option value="">جميع الحالات</option>
                            <option value="pending">قيد الانتظار</option>
                            <option value="in-progress">قيد التنفيذ</option>
                            <option value="completed">مكتمل</option>
                            <option value="delivered">تم التسليم</option>
                        </select>
                    </div>
                    <div class="filter-actions">
                        <button class="btn-filter-reset" onclick="Prosthetics.resetFilters()">
                            <i class="fas fa-undo"></i> إعادة تعيين
                        </button>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="works-stats">
                    <div class="stat-card">
                        <i class="fas fa-clipboard-list"></i>
                        <span class="stat-number" id="totalWorks">${prosthetics.length}</span>
                        <span class="stat-label">إجمالي الأعمال</span>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-clock"></i>
                        <span class="stat-number" id="pendingWorks">${prosthetics.filter(p => p.status === 'pending').length}</span>
                        <span class="stat-label">قيد الانتظار</span>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-check-circle"></i>
                        <span class="stat-number" id="completedWorks">${prosthetics.filter(p => p.status === 'completed').length}</span>
                        <span class="stat-label">مكتمل</span>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-money-bill-wave"></i>
                        <span class="stat-number" id="totalRevenue">${prosthetics.reduce((sum, p) => sum + (p.finalPrice || p.totalPrice || 0), 0).toFixed(0)}</span>
                        <span class="stat-label">إجمالي الإيرادات</span>
                    </div>
                </div>

                ${prosthetics.length === 0 ? `
                    <div class="empty-list">
                        <i class="fas fa-inbox fa-3x"></i>
                        <h3>لا توجد أعمال مسجلة</h3>
                        <p>لم يتم تسجيل أي أعمال حتى الآن</p>
                        <button class="btn btn-primary" onclick="Utils.closeModal(); NewWorkManager.showModal();">
                            <i class="fas fa-plus"></i> إضافة عمل جديد
                        </button>
                    </div>
                ` : `
                    <!-- جدول الأعمال -->
                    <div class="works-table-container">
                        <table class="works-table" id="worksTable">
                            <thead>
                                <tr>
                                    <th>رقم الحالة</th>
                                    <th>اسم المريض</th>
                                    <th>اسم الطبيب</th>
                                    <th>نوع التركيبة</th>
                                    <th>عدد الأسنان</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="worksTableBody">
                                ${this.generateWorksTableRows(prosthetics)}
                            </tbody>
                        </table>
                    </div>
                `}
            </div>
        `;
    }



    /**
     * إنشاء صفوف جدول الأعمال
     */
    static generateWorksTableRows(prosthetics) {
        return prosthetics.map(prosthetic => `
            <tr class="work-row" data-doctor="${prosthetic.doctorName}" data-status="${prosthetic.status}" data-date="${prosthetic.createdAt}">
                <td class="case-number">${prosthetic.caseNumber}</td>
                <td class="patient-name">${prosthetic.patientName}</td>
                <td class="doctor-name">${prosthetic.doctorName}</td>
                <td class="prosthetic-type">${prosthetic.prostheticType || prosthetic.type}</td>
                <td class="teeth-count">${prosthetic.selectedTeeth ? prosthetic.selectedTeeth.length : '-'}</td>
                <td class="total-price">${(prosthetic.finalPrice || prosthetic.totalPrice || 0).toFixed(0)} ريال</td>
                <td class="status">
                    <span class="status-badge status-${prosthetic.status}">
                        ${this.getStatusText(prosthetic.status)}
                    </span>
                </td>
                <td class="created-date">${new Date(prosthetic.createdAt).toLocaleDateString('ar-SA')}</td>
                <td class="actions">
                    <div class="action-buttons">
                        <button class="btn-icon btn-view" onclick="Prosthetics.viewProstheticDetails('${prosthetic.id}')" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-icon btn-edit" onclick="Prosthetics.editWork('${prosthetic.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon btn-invoice" onclick="Prosthetics.generateInvoice('${prosthetic.id}')" title="طباعة فاتورة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn-icon btn-delete" onclick="Prosthetics.deleteWork('${prosthetic.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * تهيئة فلاتر قائمة الأعمال
     */
    static initializeWorksFilters() {
        console.log('🔧 تهيئة فلاتر قائمة الأعمال...');
        // يمكن إضافة تهيئة إضافية هنا إذا لزم الأمر
    }

    /**
     * فلترة الأعمال حسب الطبيب
     */
    static filterWorksByDoctor() {
        const doctorFilter = document.getElementById('doctorFilter').value;
        const rows = document.querySelectorAll('.work-row');

        rows.forEach(row => {
            const doctorName = row.getAttribute('data-doctor');
            if (!doctorFilter || doctorName === doctorFilter) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });

        this.updateFilteredStats();
    }

    /**
     * فلترة الأعمال حسب التاريخ
     */
    static filterWorksByDate() {
        const dateFilter = document.getElementById('dateFilter').value;
        if (!dateFilter) return;

        const filterDate = new Date(dateFilter);
        const rows = document.querySelectorAll('.work-row');

        rows.forEach(row => {
            const workDate = new Date(row.getAttribute('data-date'));
            const isSameDate = workDate.toDateString() === filterDate.toDateString();

            if (isSameDate) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });

        this.updateFilteredStats();
    }

    /**
     * فلترة الأعمال حسب الحالة
     */
    static filterWorksByStatus() {
        const statusFilter = document.getElementById('statusFilter').value;
        const rows = document.querySelectorAll('.work-row');

        rows.forEach(row => {
            const status = row.getAttribute('data-status');
            if (!statusFilter || status === statusFilter) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });

        this.updateFilteredStats();
    }

    /**
     * إعادة تعيين جميع الفلاتر
     */
    static resetFilters() {
        document.getElementById('doctorFilter').value = '';
        document.getElementById('dateFilter').value = '';
        document.getElementById('statusFilter').value = '';

        const rows = document.querySelectorAll('.work-row');
        rows.forEach(row => {
            row.style.display = '';
        });

        this.updateFilteredStats();
    }

    /**
     * تحديث الإحصائيات المفلترة
     */
    static updateFilteredStats() {
        const visibleRows = document.querySelectorAll('.work-row[style=""], .work-row:not([style])');
        const totalVisible = visibleRows.length;

        // تحديث العدد المعروض
        document.getElementById('totalWorks').textContent = totalVisible;

        // حساب الإحصائيات للصفوف المرئية
        let pendingCount = 0;
        let completedCount = 0;
        let totalRevenue = 0;

        visibleRows.forEach(row => {
            const status = row.getAttribute('data-status');
            if (status === 'pending') pendingCount++;
            if (status === 'completed') completedCount++;

            const priceText = row.querySelector('.total-price').textContent;
            const price = parseFloat(priceText.replace(/[^\d.]/g, '')) || 0;
            totalRevenue += price;
        });

        document.getElementById('pendingWorks').textContent = pendingCount;
        document.getElementById('completedWorks').textContent = completedCount;
        document.getElementById('totalRevenue').textContent = totalRevenue.toFixed(0);
    }

    /**
     * تعديل عمل
     */
    static editWork(workId) {
        console.log('✏️ تعديل العمل:', workId);
        const prosthetic = Database.getProsthetics().find(p => p.id === workId);
        if (!prosthetic) {
            alert('❌ لم يتم العثور على العمل');
            return;
        }

        // إغلاق النافذة الحالية وفتح نافذة التعديل
        Utils.closeModal();
        setTimeout(() => {
            NewWorkManager.showEditModal(prosthetic);
        }, 300);
    }

    /**
     * حذف عمل
     */
    static deleteWork(workId) {
        console.log('🗑️ حذف العمل:', workId);
        const prosthetic = Database.getProsthetics().find(p => p.id === workId);
        if (!prosthetic) {
            alert('❌ لم يتم العثور على العمل');
            return;
        }

        const confirmed = confirm(`⚠️ هل أنت متأكد من حذف العمل؟\n\nرقم الحالة: ${prosthetic.caseNumber}\nالمريض: ${prosthetic.patientName}\nالطبيب: ${prosthetic.doctorName}`);

        if (confirmed) {
            Database.deleteProsthetic(workId);
            Utils.showNotification('تم حذف العمل بنجاح', 'success');

            // إعادة تحميل النافذة
            this.showWorksListModal();
        }
    }

    /**
     * إنشاء فاتورة للعمل
     */
    static generateInvoice(workId) {
        console.log('🧾 إنشاء فاتورة للعمل:', workId);
        const prosthetic = Database.getProsthetics().find(p => p.id === workId);
        if (!prosthetic) {
            alert('❌ لم يتم العثور على العمل');
            return;
        }

        // فتح نافذة الطباعة
        const invoiceWindow = window.open('', '_blank', 'width=800,height=600');
        const invoiceHTML = this.generateInvoiceHTML(prosthetic);

        invoiceWindow.document.open();
        invoiceWindow.document.write(invoiceHTML);
        invoiceWindow.document.close();

        // طباعة الفاتورة تلقائياً
        setTimeout(() => {
            invoiceWindow.print();
        }, 500);
    }

    /**
     * تحديث زر قائمة التركيبات في الواجهة الرئيسية
     */
    static updateProstheticsListButton() {
        // البحث عن الزر في الواجهة الرئيسية وتحديثه
        const listButton = document.querySelector('button[onclick*="showProstheticsListModal"]');
        if (listButton) {
            listButton.setAttribute('onclick', 'Prosthetics.showWorksListModal()');
            listButton.innerHTML = '<i class="fas fa-list"></i> قائمة الأعمال';
        }
    }



    /**
     * فلترة الأعمال حسب الطبيب
     */
    static filterWorksByDoctor() {
        const doctorFilter = document.getElementById('doctorFilter').value;
        const rows = document.querySelectorAll('.work-row');

        rows.forEach(row => {
            const doctorName = row.getAttribute('data-doctor');
            if (!doctorFilter || doctorName === doctorFilter) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });

        this.updateFilteredStats();
    }

    /**
     * فلترة الأعمال حسب التاريخ
     */
    static filterWorksByDate() {
        const dateFilter = document.getElementById('dateFilter').value;
        if (!dateFilter) return;

        const filterDate = new Date(dateFilter);
        const rows = document.querySelectorAll('.work-row');

        rows.forEach(row => {
            const workDate = new Date(row.getAttribute('data-date'));
            const isSameDate = workDate.toDateString() === filterDate.toDateString();

            if (isSameDate) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });

        this.updateFilteredStats();
    }

    /**
     * فلترة الأعمال حسب الحالة
     */
    static filterWorksByStatus() {
        const statusFilter = document.getElementById('statusFilter').value;
        const rows = document.querySelectorAll('.work-row');

        rows.forEach(row => {
            const status = row.getAttribute('data-status');
            if (!statusFilter || status === statusFilter) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });

        this.updateFilteredStats();
    }

    /**
     * إعادة تعيين جميع الفلاتر
     */
    static resetFilters() {
        document.getElementById('doctorFilter').value = '';
        document.getElementById('dateFilter').value = '';
        document.getElementById('statusFilter').value = '';

        const rows = document.querySelectorAll('.work-row');
        rows.forEach(row => {
            row.style.display = '';
        });

        this.updateFilteredStats();
    }

    /**
     * تحديث الإحصائيات المفلترة
     */
    static updateFilteredStats() {
        const visibleRows = document.querySelectorAll('.work-row[style=""], .work-row:not([style])');
        const totalVisible = visibleRows.length;

        document.getElementById('totalWorks').textContent = totalVisible;

        let pendingCount = 0;
        let completedCount = 0;
        let totalRevenue = 0;

        visibleRows.forEach(row => {
            const status = row.getAttribute('data-status');
            if (status === 'pending') pendingCount++;
            if (status === 'completed') completedCount++;

            const priceText = row.querySelector('.total-price').textContent;
            const price = parseFloat(priceText.replace(/[^\d.]/g, '')) || 0;
            totalRevenue += price;
        });

        document.getElementById('pendingWorks').textContent = pendingCount;
        document.getElementById('completedWorks').textContent = completedCount;
        document.getElementById('totalRevenue').textContent = totalRevenue.toFixed(0);
    }

    /**
     * تعديل عمل
     */
    static editWork(workId) {
        console.log('✏️ تعديل العمل:', workId);
        const prosthetic = Database.getProsthetics().find(p => p.id === workId);
        if (!prosthetic) {
            alert('❌ لم يتم العثور على العمل');
            return;
        }

        Utils.closeModal();
        setTimeout(() => {
            NewWorkManager.showEditModal(prosthetic);
        }, 300);
    }

    /**
     * حذف عمل
     */
    static deleteWork(workId) {
        console.log('🗑️ حذف العمل:', workId);
        const prosthetic = Database.getProsthetics().find(p => p.id === workId);
        if (!prosthetic) {
            alert('❌ لم يتم العثور على العمل');
            return;
        }

        const confirmed = confirm(`⚠️ هل أنت متأكد من حذف العمل؟\n\nرقم الحالة: ${prosthetic.caseNumber}\nالمريض: ${prosthetic.patientName}\nالطبيب: ${prosthetic.doctorName}`);

        if (confirmed) {
            Database.deleteProsthetic(workId);
            Utils.showNotification('تم حذف العمل بنجاح', 'success');
            this.showWorksListModal();
        }
    }

    /**
     * إنشاء فاتورة للعمل
     */
    static generateInvoice(workId) {
        console.log('🧾 إنشاء فاتورة للعمل:', workId);
        const prosthetic = Database.getProsthetics().find(p => p.id === workId);
        if (!prosthetic) {
            alert('❌ لم يتم العثور على العمل');
            return;
        }

        const invoiceWindow = window.open('', '_blank', 'width=800,height=600');
        const invoiceHTML = this.generateInvoiceHTML(prosthetic);

        invoiceWindow.document.open();
        invoiceWindow.document.write(invoiceHTML);
        invoiceWindow.document.close();

        setTimeout(() => {
            invoiceWindow.print();
        }, 500);
    }

    /**
     * إنشاء HTML الفاتورة
     */
    static generateInvoiceHTML(prosthetic) {
        return `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>فاتورة - ${prosthetic.caseNumber}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .invoice-header { text-align: center; margin-bottom: 30px; }
                    .invoice-details { margin-bottom: 20px; }
                    .invoice-table { width: 100%; border-collapse: collapse; }
                    .invoice-table th, .invoice-table td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                    .total { font-weight: bold; font-size: 1.2em; }
                </style>
            </head>
            <body>
                <div class="invoice-header">
                    <h1>معمل الأسنان البراء</h1>
                    <h2>فاتورة رقم: ${prosthetic.caseNumber}</h2>
                </div>

                <div class="invoice-details">
                    <p><strong>اسم المريض:</strong> ${prosthetic.patientName}</p>
                    <p><strong>اسم الطبيب:</strong> د. ${prosthetic.doctorName}</p>
                    <p><strong>التاريخ:</strong> ${new Date(prosthetic.createdAt).toLocaleDateString('ar-SA')}</p>
                </div>

                <table class="invoice-table">
                    <thead>
                        <tr>
                            <th>الخدمة</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>${prosthetic.prostheticType}</td>
                            <td>${prosthetic.selectedTeeth ? prosthetic.selectedTeeth.length : 1}</td>
                            <td>${prosthetic.unitPrice || 0} ريال</td>
                            <td>${prosthetic.finalPrice || prosthetic.totalPrice || 0} ريال</td>
                        </tr>
                    </tbody>
                    <tfoot>
                        <tr class="total">
                            <td colspan="3">المجموع الإجمالي:</td>
                            <td>${prosthetic.finalPrice || prosthetic.totalPrice || 0} ريال</td>
                        </tr>
                    </tfoot>
                </table>
            </body>
            </html>
        `;
    }

    /**
     * عرض نافذة التقارير
     */
    static showReportsModal() {
        console.log('📊 عرض نافذة التقارير...');
        alert('🚧 نافذة التقارير قيد التطوير');
    }

    /**
     * تصدير البيانات
     */
    static exportData() {
        console.log('📤 تصدير البيانات...');
        alert('🚧 تصدير البيانات قيد التطوير');
    }

    /**
     * دالة للتوافق مع الاسم القديم
     */
    static showProstheticsListModal() {
        console.log('🔄 استدعاء الدالة بالاسم القديم، إعادة توجيه للاسم الجديد...');
        return this.showWorksListModal();
    }
}

// تصدير الكلاس للاستخدام العام
window.Prosthetics = Prosthetics;

// التحقق من وجود الدوال المطلوبة
if (typeof Prosthetics.showWorksListModal === 'function') {
    console.log('✅ دالة showWorksListModal متاحة');
} else {
    console.error('❌ دالة showWorksListModal غير متاحة');
}

console.log('✅ تم تحميل كلاس إدارة التركيبات بنجاح');
