/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;         /* أزرق رئيسي عصري */
    --primary-dark: #0f1c3f;          /* أزرق داكن */
    --primary-light: #60a5fa;         /* أزرق فاتح */
    --secondary-color: #b0b8c1;       /* رصاصي أنيق */
    --accent-color: #38bdf8;          /* أزرق سماوي فاتح */
    --success-color: #22d3ee;         /* أخضر أزرق هادئ */
    --warning-color: #fbbf24;         /* برتقالي عصري */
    --danger-color: #ef4444;          /* أحمر عصري */
    --light-bg: #f1f3f6;              /* رمادي فاتح جداً */
    --white: #ffffff;
    --text-dark: #1e293b;             /* أزرق داكن/رمادي غامق */
    --text-light: #64748b;            /* رمادي فاتح */
    --border-color: #e0e6ed;          /* رمادي فاتح جداً */
    --shadow: 0 2px 10px rgba(37, 99, 235, 0.08);
    --border-radius: 14px;
    --transition: all 0.3s cubic-bezier(.4,0,.2,1);
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: var(--light-bg);
    color: var(--text-dark);
    line-height: 1.6;
    direction: rtl;
}

/* شاشة تسجيل الدخول الجديدة */
.login-screen {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #6366f1, #8b5cf6, #a855f7);
    padding: 0;
    overflow: hidden;
}

.login-container-new {
    display: flex;
    width: 100%;
    max-width: 1200px;
    height: 100vh;
    background: transparent;
    border-radius: 0;
    box-shadow: none;
    overflow: hidden;
}

/* الجانب الأيسر - نموذج تسجيل الدخول */
.login-left {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 40px;
    border-radius: 0 30px 30px 0;
}

.login-form-container {
    width: 100%;
    max-width: 400px;
}

.welcome-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 10px;
    text-align: center;
}

.welcome-subtitle {
    font-size: 1.1rem;
    color: #6b7280;
    margin-bottom: 40px;
    text-align: center;
}

.modern-login-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.input-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 5px;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    right: 15px;
    color: #9ca3af;
    font-size: 1rem;
    z-index: 2;
}

.modern-input {
    width: 100%;
    padding: 15px 45px 15px 15px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    background: #f9fafb;
    transition: all 0.3s ease;
    outline: none;
}

.modern-input:focus {
    border-color: #3b82f6;
    background: #ffffff;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.toggle-password {
    position: absolute;
    left: 15px;
    color: #9ca3af;
    cursor: pointer;
    font-size: 1rem;
    z-index: 2;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: #3b82f6;
}

.form-options-new {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
}

.remember-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    color: #374151;
}

.checkbox-custom {
    width: 18px;
    height: 18px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.remember-checkbox input[type="checkbox"] {
    display: none;
}

.remember-checkbox input[type="checkbox"]:checked + .checkbox-custom {
    background: #3b82f6;
    border-color: #3b82f6;
}

.remember-checkbox input[type="checkbox"]:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password {
    color: #3b82f6;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #1d4ed8;
}

.modern-login-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border: none;
    padding: 15px 20px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.modern-login-btn:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.login-help {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e5e7eb;
}

.login-help p {
    font-size: 0.85rem;
    color: #6b7280;
    margin-bottom: 15px;
}

.reset-db-btn {
    background: #ef4444;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.reset-db-btn:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

/* الجانب الأيمن - معلومات النظام */
.login-right {
    flex: 1;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 40px;
    color: white;
    position: relative;
    overflow: hidden;
}

.login-right::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.system-info {
    text-align: center;
    position: relative;
    z-index: 2;
    max-width: 500px;
}

.system-logo {
    margin-bottom: 30px;
}

.logo-circle {
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.logo-circle i {
    font-size: 3.5rem;
    color: white;
}

.system-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 50px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: 30px;
    margin-top: 40px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(-5px);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.feature-icon i {
    font-size: 1.5rem;
    color: white;
}

.feature-content {
    text-align: right;
    flex: 1;
}

.feature-content h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: white;
}

.feature-content p {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .login-container-new {
        flex-direction: column;
        height: auto;
        min-height: 100vh;
    }

    .login-left,
    .login-right {
        flex: none;
        border-radius: 0;
    }

    .login-left {
        order: 2;
        padding: 40px 20px;
    }

    .login-right {
        order: 1;
        padding: 40px 20px;
        min-height: 40vh;
    }

    .features-list {
        flex-direction: row;
        overflow-x: auto;
        gap: 20px;
        padding-bottom: 10px;
    }

    .feature-item {
        min-width: 250px;
        flex-shrink: 0;
    }

    .system-title {
        font-size: 2rem;
        margin-bottom: 30px;
    }

    .welcome-title {
        font-size: 2rem;
    }
}

/* وظيفة إظهار/إخفاء كلمة المرور */
.toggle-password.active {
    color: #3b82f6;
}

/* الواجهة الرئيسية */
.main-app {
    display: grid;
    grid-template-areas: 
        "header header"
        "sidebar content";
    grid-template-columns: 250px 1fr;
    grid-template-rows: 60px 1fr;
    min-height: 100vh;
}

/* الشريط العلوي */
.top-header {
    grid-area: header;
    background: var(--white);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: var(--shadow);
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-dark);
    cursor: pointer;
    padding: 8px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background: var(--light-bg);
}

.header-left h1 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info span {
    font-weight: 600;
    color: var(--text-dark);
}

.logout-btn {
    background: var(--danger-color);
    color: var(--white);
    border: none;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.logout-btn:hover {
    background: #c0392b;
}

/* نافذة منبثقة (Modal) أساسية */
.modal-overlay {
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    padding: 20px;
}
.modal-content {
    background: #fff;
    border-radius: 14px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    min-width: 350px;
}

/* القائمة الجانبية */
.sidebar {
    grid-area: sidebar;
    background: linear-gradient(135deg, #2563eb, #7c3aed);
    border-left: 1px solid var(--border-color);
    overflow-y: auto;
    transition: all var(--transition);
    width: 280px;
    position: fixed;
    right: 0;
    top: 70px;
    height: calc(100vh - 70px);
    z-index: 100;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
}

.sidebar.collapsed {
    width: 60px;
}

.sidebar.collapsed .nav-link span {
    opacity: 0;
    visibility: hidden;
    width: 0;
}

.sidebar.collapsed .sidebar-title h2 {
    opacity: 0;
    visibility: hidden;
    width: 0;
}

/* إظهار الزر خارج الشريط المطوي */
.sidebar.collapsed .sidebar-toggle-btn {
    position: fixed !important;
    right: 10px !important;
    top: 80px !important;
    z-index: 9999 !important;
    background: #007bff !important;
    color: white !important;
    border: 2px solid #007bff !important;
    border-radius: 50% !important;
    width: 45px !important;
    height: 45px !important;
    box-shadow: 0 4px 15px rgba(0,123,255,0.4) !important;
    transform: none !important;
    animation: pulse 2s infinite;
}

.sidebar.collapsed .sidebar-toggle-btn i {
    transform: rotate(180deg);
}

.sidebar.collapsed .sidebar-toggle-btn:hover {
    background: #0056b3 !important;
    border-color: #0056b3 !important;
    transform: scale(1.1) !important;
    animation: none;
}

/* تأثير النبض للزر المطوي */
@keyframes pulse {
    0% {
        box-shadow: 0 4px 15px rgba(0,123,255,0.4), 0 0 0 0 rgba(0,123,255,0.7);
    }
    70% {
        box-shadow: 0 4px 15px rgba(0,123,255,0.4), 0 0 0 10px rgba(0,123,255,0);
    }
    100% {
        box-shadow: 0 4px 15px rgba(0,123,255,0.4), 0 0 0 0 rgba(0,123,255,0);
    }
}

/* رأس الشريط الجانبي */
.sidebar-header {
    padding: 2px 8px;
    border-bottom: 1px solid var(--border-color);
    background: var(--light-bg);
    min-height: 25px;
    position: relative;
    overflow: visible;
}

.sidebar-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 4px;
}

.sidebar-title h2 {
    margin: 0;
    font-size: 0.75rem;
    color: var(--text-dark);
    font-weight: 600;
    transition: all var(--transition);
    white-space: nowrap;
    line-height: 1.1;
}

.sidebar-toggle-btn {
    background: var(--light-bg);
    border: 1px solid var(--border-color);
    border-radius: 2px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition);
    color: var(--text-secondary);
    flex-shrink: 0;
    position: relative;
    z-index: 1000;
}

.sidebar-toggle-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    transform: scale(1.1);
}

.sidebar-toggle-btn i {
    font-size: 0.6rem;
    transition: transform var(--transition);
}

/* تحسين الزر عند الطي */
.sidebar.collapsed .sidebar-header {
    position: relative;
    overflow: visible;
}

.sidebar-nav ul {
    list-style: none;
    padding: 2px 0;
}

.sidebar-nav li {
    margin-bottom: 2px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 15px;
    color: var(--text-dark);
    text-decoration: none;
    transition: all var(--transition);
    border-right: 3px solid transparent;
    position: relative;
    overflow: hidden;
}

.nav-link:hover {
    background: var(--light-bg);
    color: var(--primary-color);
}

.nav-link.active {
    background: var(--light-bg);
    color: var(--primary-color);
    border-right-color: var(--primary-color);
    font-weight: 600;
}

.nav-link i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.nav-link span {
    transition: all var(--transition);
    white-space: nowrap;
}

/* تحسينات للشريط المطوي */
.sidebar.collapsed .nav-link {
    padding: 8px;
    justify-content: center;
    border-right: none;
    border-radius: 6px;
    margin: 3px;
}

.sidebar.collapsed .nav-link span {
    opacity: 0;
    visibility: hidden;
    width: 0;
    margin: 0;
}

.sidebar.collapsed .nav-link i {
    margin: 0;
}

/* المحتوى الرئيسي */
.main-content {
    grid-area: content;
    padding: 20px;
    overflow-y: auto;
    background: var(--light-bg);
    margin-right: 280px;
    margin-top: 70px;
    transition: margin-right 0.3s ease;
    min-height: calc(100vh - 70px);
    width: calc(100% - 280px);
}

.sidebar.collapsed ~ .main-content,
.sidebar-collapsed .main-content {
    margin-right: 60px !important;
    width: calc(100% - 60px) !important;
}

/* التطبيق الرئيسي */
.main-app {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
}

/* فئات مساعدة للتكيف */
.sidebar-collapsed .sidebar {
    width: 60px !important;
}

.sidebar-collapsed .main-content {
    margin-right: 60px !important;
    width: calc(100% - 60px) !important;
}

.page-content {
    max-width: 1200px;
    margin: 0 auto;
}

/* بطاقات الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

/* 📊 الإحصائيات في الرأس - تصميم موحد لجميع الكلاسات */
.header-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(255,255,255,0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.header-stat-item {
    text-align: center;
    padding: 0.75rem;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255,255,255,0.15);
}

.header-stat-item:hover {
    transform: translateY(-2px);
    background: rgba(255,255,255,0.2);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.header-stat-icon {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    opacity: 0.9;
    color: white;
}

.header-stat-number {
    display: block;
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: white;
}

.header-stat-label {
    font-size: 0.7rem;
    opacity: 0.8;
    color: white;
    line-height: 1.2;
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
}

.stat-icon.primary { background: var(--primary-color); }
.stat-icon.success { background: var(--success-color); }
.stat-icon.warning { background: var(--warning-color); }
.stat-icon.danger { background: var(--danger-color); }

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 5px;
}

.stat-label {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* الجداول */
.table-container {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    margin-bottom: 20px;
}

.table-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.table-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
}

.table-actions {
    display: flex;
    gap: 10px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: #1e3d6f;
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-success:hover {
    background: #1e8449;
}

.btn-warning {
    background: var(--warning-color);
    color: var(--white);
}

.btn-warning:hover {
    background: #d68910;
}

.btn-danger {
    background: var(--danger-color);
    color: var(--white);
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-secondary:hover {
    background: #2c3e50;
}

/* النوافذ المنبثقة */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.6);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 99999;
    padding: 20px;
    backdrop-filter: blur(5px);
}

.modal-overlay.show {
    display: flex !important;
}

.modal-content {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    max-width: 95vw;
    max-height: 95vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease;
    position: relative;
    z-index: 100000;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* الإشعارات */
.notifications-container {
    position: fixed;
    top: 80px;
    left: 20px;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.notification {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 15px 20px;
    box-shadow: var(--shadow);
    border-right: 4px solid;
    min-width: 300px;
    animation: notificationSlideIn 0.3s ease;
}

.notification.success { border-right-color: var(--success-color); }
.notification.warning { border-right-color: var(--warning-color); }
.notification.error { border-right-color: var(--danger-color); }
.notification.info { border-right-color: var(--accent-color); }

@keyframes notificationSlideIn {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .main-app {
        grid-template-areas: 
            "header"
            "content";
        grid-template-columns: 1fr;
        grid-template-rows: 60px 1fr;
    }
    
    .sidebar {
        position: fixed;
        top: 70px;
        right: -250px;
        width: 250px;
        height: calc(100vh - 70px);
        z-index: 1500;
        transition: all var(--transition);
    }

    .sidebar.open {
        right: 0;
    }

    .sidebar.collapsed {
        width: 60px;
        right: -60px;
    }

    .sidebar.collapsed.open {
        right: 0;
    }

    /* تحسين الزر للجوال */
    .sidebar.collapsed .sidebar-toggle-btn {
        left: -20px;
        width: 40px;
        height: 40px;
        border-radius: 50%;
    }

    .sidebar.collapsed .sidebar-toggle-btn i {
        font-size: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .main-content {
        margin-right: 0;
        margin-top: 70px;
        width: 100%;
    }
    
    .table-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .table-actions {
        justify-content: center;
    }
}

/* أنماط إضافية للنماذج */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
}

.search-box {
    position: relative;
    margin-bottom: 20px;
}

.search-box input {
    width: 100%;
    padding: 12px 40px 12px 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

/* أنماط الجداول */
.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background: var(--light-bg);
    font-weight: 600;
    color: var(--text-dark);
}

.data-table tbody tr:hover {
    background: var(--light-bg);
}

/* أنماط الحالات */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    display: inline-block;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-progress {
    background: #d1ecf1;
    color: #0c5460;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-delivered {
    background: #e2e3e5;
    color: #383d41;
}

/* 📱 التصميم المتجاوب للإحصائيات في الرأس */
@media (max-width: 768px) {
    .header-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
        margin-top: 0.75rem;
        padding: 0.75rem;
    }

    .header-stat-item {
        padding: 0.5rem;
    }

    .header-stat-number {
        font-size: 1rem;
    }

    .header-stat-label {
        font-size: 0.65rem;
    }

    .header-stat-icon {
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }
}

@media (max-width: 480px) {
    .header-stats {
        grid-template-columns: 1fr;
        gap: 0.5rem;
        padding: 0.5rem;
    }

    .header-stat-item {
        padding: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        text-align: right;
    }

    .header-stat-icon {
        font-size: 1.2rem;
        margin-bottom: 0;
        flex-shrink: 0;
    }

    .header-stat-content {
        flex: 1;
    }

    .header-stat-number {
        font-size: 1.1rem;
        margin-bottom: 0.1rem;
    }

    .header-stat-label {
        font-size: 0.7rem;
    }
}
