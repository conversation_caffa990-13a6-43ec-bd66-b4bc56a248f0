/* التصميم العصري الحديث */

/* المتغيرات العامة */
:root {
    /* الألوان الأساسية */
    --primary-color: #667eea;
    --primary-dark: #5a6fd8;
    --primary-light: #7c8ef0;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    
    /* ألوان الحالة */
    --success-color: #4ecdc4;
    --success-color-dark: #3bb5ae;
    --warning-color: #ffe066;
    --warning-color-dark: #e6c75a;
    --error-color: #ff6b6b;
    --info-color: #4dabf7;
    --accent-color-dark: #e084f5;
    
    /* ألوان الخلفية */
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --bg-dark: #1a202c;
    --bg-card: #ffffff;
    
    /* ألوان النص */
    --text-primary: #2d3748;
    --text-secondary: #718096;
    --text-muted: #a0aec0;
    --text-white: #ffffff;
    
    /* الحدود والظلال */
    --border-color: #e2e8f0;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    
    /* الظلال */
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* المسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* الخطوط */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* الانتقالات */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* متغيرات الوضع المظلم */
[data-theme="dark"] {
    /* الألوان الأساسية */
    --primary-color: #7c3aed;
    --primary-dark: #6d28d9;
    --primary-light: #8b5cf6;
    --secondary-color: #ec4899;
    --accent-color: #f472b6;

    /* ألوان الحالة */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;

    /* ألوان الخلفية */
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-dark: #030712;
    --bg-card: #1f2937;

    /* ألوان النص */
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --text-white: #ffffff;

    /* الحدود والظلال */
    --border-color: #374151;

    /* الظلال */
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* إعادة تعيين الأساسيات */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    background: linear-gradient(135deg, var(--bg-primary) 0%, #e3f2fd 100%);
    color: var(--text-primary);
    line-height: var(--line-height-normal);
    min-height: 100vh;
    direction: rtl;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* شاشة تسجيل الدخول */
#loginScreen {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

#loginScreen::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="50" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="30" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(1deg); }
}

.login-container {
    background: var(--bg-secondary);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    width: 100%;
    max-width: 400px;
    position: relative;
    z-index: 1;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.login-header h1 {
    color: var(--primary-color);
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.login-header p {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

/* التطبيق الرئيسي */
#mainApp {
    display: flex;
    min-height: 100vh;
    background: var(--bg-primary);
}

/* الشريط الجانبي */
#sidebar {
    width: 280px;
    background: linear-gradient(180deg, var(--bg-secondary) 0%, #f1f5f9 100%);
    box-shadow: var(--shadow-lg);
    transition: transform var(--transition-normal);
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    z-index: 1000;
    border-left: 1px solid var(--border-color);
}

.sidebar-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-white);
}

.sidebar-header h2 {
    font-family: var(--font-primary);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-normal);
    margin-bottom: var(--spacing-xs);
}

.sidebar-header p {
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

.sidebar-nav {
    padding: var(--spacing-lg) 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-xl);
    color: var(--text-primary);
    text-decoration: none;
    transition: all var(--transition-fast);
    position: relative;
    margin: 0 var(--spacing-md);
    border-radius: var(--border-radius);
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    letter-spacing: var(--letter-spacing-normal);
}

.nav-link:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--text-white);
    transform: translateX(-5px);
    box-shadow: var(--shadow-md);
}

.nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--text-white);
    box-shadow: var(--shadow-md);
    font-weight: var(--font-weight-semibold);
}

.nav-link.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background: var(--accent-color);
    border-radius: 2px;
}

.nav-link i {
    margin-left: var(--spacing-md);
    font-size: var(--font-size-lg);
    width: 24px;
    text-align: center;
}

/* المحتوى الرئيسي */
#mainContent {
    flex: 1;
    margin-right: 280px;
    height: 100vh;
    background: var(--bg-primary);
    overflow-y: auto;
    overflow-x: hidden;
}

/* محتوى الصفحة */
.page-content {
    padding: var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
    min-height: calc(100vh - 70px);
    width: auto;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    border: 2px solid var(--primary-color);
    box-shadow: var(--shadow-lg);
}

/* حاويات الصفحات الرئيسية */
.dashboard-container,
.prosthetics-container,
.doctors-container,
.employees-container,
.financial-container,
.settings-container,
.inventory-container {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    border: 2px solid var(--primary-color);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-lg);
    margin: 0;
}

/* تنسيقات خاصة للجداول والبطاقات */
.table-container,
.stats-grid,
.quick-actions {
    background: var(--bg-primary);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

/* تأثيرات التفاعل */
.page-content:hover {
    border-color: var(--primary-dark);
    box-shadow: 0 8px 25px rgba(108, 92, 231, 0.15);
}

.table-container:hover,
.stats-grid:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

/* حاوي لوحة التحكم */
.dashboard-container {
    width: 100%;
    max-width: none;
    padding: 0;
}

/* الشريط العلوي */
.top-bar {
    background: var(--bg-secondary);
    padding: var(--spacing-lg) var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    z-index: 200;
    backdrop-filter: blur(10px);
}

.top-bar-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.top-bar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* معلومات المعمل */
.lab-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.lab-logo {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-size: var(--font-size-xl);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.lab-logo::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    animation: logoGlow 3s ease-in-out infinite;
}

@keyframes logoGlow {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.1); }
}

.lab-details h2 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.lab-details small {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

/* زر تغيير الوضع */
.theme-toggle {
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.theme-toggle:hover {
    transform: rotate(15deg) scale(1.1);
}

.theme-toggle .theme-icon {
    transition: all var(--transition-normal);
}

.theme-toggle.dark-mode .theme-icon {
    transform: rotate(180deg);
}

/* تأثير الانتقال للوضع */
.theme-toggle::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, var(--warning-color), var(--primary-color));
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all var(--transition-slow);
    z-index: -1;
}

.theme-toggle:hover::before {
    width: 100%;
    height: 100%;
}

/* زر الإعدادات السريعة */
.quick-settings-toggle {
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.quick-settings-toggle:hover {
    transform: rotate(90deg) scale(1.1);
}

.quick-settings-toggle .settings-icon {
    transition: all var(--transition-normal);
}

.quick-settings-toggle.active .settings-icon {
    transform: rotate(180deg);
}

/* تأثير الانتقال للإعدادات */
.quick-settings-toggle::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, var(--info-color), var(--secondary-color));
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all var(--transition-slow);
    z-index: -1;
}

.quick-settings-toggle:hover::before {
    width: 100%;
    height: 100%;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-weight: 600;
}

/* محتوى الصفحة */
#pageContent {
    padding: var(--spacing-xl);
    min-height: calc(100vh - 140px); /* تم تقليل الارتفاع لإفساح المجال للشريط السفلي */
    padding-bottom: calc(var(--spacing-xl) + 60px); /* مساحة إضافية للشريط السفلي */
}

/* رؤوس الصفحات */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-xl);
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.page-title {
    font-family: var(--font-primary);
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title i {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-actions {
    display: flex;
    gap: var(--spacing-md);
}

/* الأزرار الحديثة */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius);
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    letter-spacing: var(--letter-spacing-wide);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-normal);
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-white);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: var(--text-white);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #45b7aa);
    color: var(--text-white);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #ffd93d);
    color: var(--text-primary);
}

.btn-danger {
    background: linear-gradient(135deg, var(--error-color), #ff5252);
    color: var(--text-white);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color), #339af0);
    color: var(--text-white);
}

/* أزرار الأيقونات */
.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* البطاقات الحديثة */
.card, .dashboard-card, .table-container {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.card:hover, .dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

/* إزالة تأثير hover من بطاقة النشاطات */
.dashboard-card.activities-card:hover {
    transform: none !important;
    box-shadow: var(--shadow-md) !important;
}

/* بديل للمتصفحات التي لا تدعم :has */
.activities-card {
    transition: none !important;
}

.activities-card:hover {
    transform: none !important;
    box-shadow: var(--shadow-md) !important;
}

.card-header, .table-header {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-primary));
}

.card-title, .table-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.card-content {
    padding: var(--spacing-xl);
}

/* الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

/* إحصائيات مدمجة للوحة التحكم */
.dashboard-stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.dashboard-stats-grid .stat-card {
    padding: var(--spacing-md) var(--spacing-lg);
    min-height: auto;
    text-align: center;
}

.dashboard-stats-grid .stat-header {
    margin-bottom: var(--spacing-sm);
    justify-content: center;
}

.dashboard-stats-grid .stat-icon {
    width: 35px;
    height: 35px;
    font-size: var(--font-size-lg);
    margin: 0 auto var(--spacing-sm);
}

.dashboard-stats-grid .stat-value {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-xs);
    font-weight: 700;
}

.dashboard-stats-grid .stat-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.stat-card {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: var(--text-white);
}

.stat-icon.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.stat-icon.success {
    background: linear-gradient(135deg, var(--success-color), #45b7aa);
}

.stat-icon.warning {
    background: linear-gradient(135deg, var(--warning-color), #ffd93d);
}

.stat-icon.danger {
    background: linear-gradient(135deg, var(--error-color), #ff5252);
}

.stat-icon.secondary {
    background: linear-gradient(135deg, var(--secondary-color), #6a4c93);
}

.stat-value {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    font-weight: 500;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--success-color);
    font-weight: 600;
}

/* الجداول الحديثة */
.table-wrapper {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-secondary);
}

.data-table th {
    background: linear-gradient(135deg, var(--bg-primary), #e2e8f0);
    padding: var(--spacing-lg);
    text-align: right;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table td {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
}

.data-table tr {
    transition: all var(--transition-fast);
}

.data-table tr:hover {
    background: linear-gradient(135deg, var(--bg-primary), #f8fafc);
    transform: scale(1.01);
}

.table-row-clickable {
    cursor: pointer;
}

.table-row-clickable:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--text-white);
    box-shadow: var(--shadow-md);
}

/* الشارات والعلامات */
.badge, .status-badge, .prosthetic-type-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background: linear-gradient(135deg, var(--warning-color), #ffd93d);
    color: var(--text-primary);
}

.status-progress {
    background: linear-gradient(135deg, var(--info-color), #339af0);
    color: var(--text-white);
}

.status-completed {
    background: linear-gradient(135deg, var(--success-color), #45b7aa);
    color: var(--text-white);
}

.status-delivered {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: var(--text-white);
}

/* الرسوم المتحركة */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes statCardPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

.fade-in {
    animation: fadeIn var(--transition-normal) ease-out;
}

.slide-in {
    animation: slideIn var(--transition-normal) ease-out;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    #sidebar {
        transform: translateX(100%);
    }
    
    #sidebar.open {
        transform: translateX(0);
    }
    
    #mainContent {
        margin-right: 0;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xs);
    }

    .dashboard-stats-grid .stat-card {
        padding: var(--spacing-sm);
    }

    .dashboard-stats-grid .stat-icon {
        width: 30px;
        height: 30px;
        font-size: var(--font-size-base);
    }

    .dashboard-stats-grid .stat-value {
        font-size: var(--font-size-xl);
    }

    .dashboard-stats-grid .stat-label {
        font-size: var(--font-size-xs);
    }
}

/* شريط المعلومات السريعة الأفقي العصري */
.quick-info-bar {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.quick-info-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--success-color), var(--warning-color));
}

.quick-info-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    align-items: center;
}

.quick-info-bar .info-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.7);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.quick-info-bar .info-item:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.quick-info-bar .info-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--text-on-primary);
    flex-shrink: 0;
}

.quick-info-bar .info-item:nth-child(1) .info-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark));
}

.quick-info-bar .info-item:nth-child(2) .info-icon {
    background: linear-gradient(135deg, var(--success-color), var(--success-color-dark));
}

.quick-info-bar .info-item:nth-child(3) .info-icon {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-color-dark));
}

.quick-info-bar .info-item:nth-child(4) .info-icon {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-color-dark));
}

.quick-info-bar .info-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    flex: 1;
}

.quick-info-bar .info-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
    margin: 0;
}

.quick-info-bar .info-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

/* تصميم متجاوب للشريط */
@media (max-width: 768px) {
    .quick-info-container {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }

    .quick-info-bar .info-item {
        padding: var(--spacing-sm);
        gap: var(--spacing-sm);
    }

    .quick-info-bar .info-icon {
        width: 35px;
        height: 35px;
        font-size: var(--font-size-md);
    }

    .quick-info-bar .info-label {
        font-size: var(--font-size-xs);
    }

    .quick-info-bar .info-value {
        font-size: var(--font-size-md);
    }
}

@media (max-width: 480px) {
    .quick-info-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .quick-info-bar {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }
}

/* شريط إحصائيات العمل */
.work-stats-bar {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.work-stats-bar .stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.work-stats-bar .stat-item:hover {
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
    cursor: pointer;
}

.work-stats-bar .stat-item.active {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    border: 2px solid var(--primary-color);
}

.work-stats-bar .stat-icon {
    width: 45px;
    height: 45px;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--text-on-primary);
    flex-shrink: 0;
}

.work-stats-bar .stat-icon.pending {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-color-dark));
}

.work-stats-bar .stat-icon.progress {
    background: linear-gradient(135deg, var(--info-color), #3b82f6);
}

.work-stats-bar .stat-icon.completed {
    background: linear-gradient(135deg, var(--success-color), var(--success-color-dark));
}

.work-stats-bar .stat-icon.delivered {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.work-stats-bar .stat-item.total .stat-icon {
    background: linear-gradient(135deg, var(--secondary-color), #6b46c1);
}

.work-stats-bar .stat-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    flex: 1;
}

.work-stats-bar .stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
    margin: 0;
}

.work-stats-bar .stat-value {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

/* تصميم متجاوب لشريط الإحصائيات */
@media (max-width: 768px) {
    .work-stats-bar {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
    }

    .work-stats-bar .stat-item {
        padding: var(--spacing-sm);
        gap: var(--spacing-sm);
    }

    .work-stats-bar .stat-icon {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-md);
    }
}

@media (max-width: 480px) {
    .work-stats-bar {
        grid-template-columns: 1fr;
    }
}

/* النموذج المطور - تسجيل العمل */
.modern-form-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0;
}

/* شريط التقدم */
.form-progress-bar {
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    margin: 0 var(--spacing-lg);
    position: relative;
    transition: all var(--transition-normal);
}

.progress-step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    right: -30px;
    width: 60px;
    height: 2px;
    background: var(--border-color);
    transition: all var(--transition-normal);
}

.progress-step.active:not(:last-child)::after {
    background: var(--primary-color);
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-full);
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: var(--text-secondary);
    transition: all var(--transition-normal);
}

.progress-step.active .step-number {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-on-primary);
    transform: scale(1.1);
}

.step-label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-secondary);
    text-align: center;
    transition: all var(--transition-normal);
}

.progress-step.active .step-label {
    color: var(--primary-color);
}

/* خطوات النموذج */
.form-step {
    display: none;
    animation: fadeIn 0.3s ease;
}

.form-step.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* أقسام النموذج المطور */
.modern-form .form-section {
    margin-bottom: var(--spacing-xl);
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.modern-form .section-header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--primary-color);
}

.modern-form .section-header h3 {
    color: var(--primary-color);
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin: 0 0 var(--spacing-xs) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.modern-form .section-description {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
    font-style: italic;
}

/* حقول النموذج المطور */
.modern-form .form-group {
    margin-bottom: var(--spacing-lg);
}

.modern-form .form-group label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    display: block;
    font-size: var(--font-size-base);
}

.modern-form .form-group input,
.modern-form .form-group select,
.modern-form .form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    transition: all var(--transition-normal);
    background: var(--bg-primary);
}

.modern-form .form-group input:focus,
.modern-form .form-group select:focus,
.modern-form .form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
    transform: translateY(-1px);
}

.modern-form .form-group input.valid {
    border-color: var(--success-color);
}

.modern-form .form-group input.invalid {
    border-color: var(--error-color);
}

.field-hint {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
    font-style: italic;
}

/* ملخص التكلفة المطور */
.cost-summary-advanced {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    color: var(--text-on-primary);
}

.cost-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.cost-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
}

.cost-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.cost-item.total {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    font-weight: 700;
}

.cost-icon {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius-full);
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.cost-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    flex: 1;
}

.cost-label {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    font-weight: 500;
}

.cost-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
}

.cost-details input {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--text-on-primary);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

.cost-details input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

/* أزرار التنقل */
.form-navigation {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-xl) 0;
    border-top: 2px solid var(--border-color);
    margin-top: var(--spacing-xl);
}

.form-navigation .btn {
    min-width: 120px;
    padding: var(--spacing-md) var(--spacing-lg);
    font-weight: 600;
}

.btn-large {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: var(--font-size-lg);
    min-width: 150px;
}

/* تصميم متجاوب للنموذج المطور */
@media (max-width: 768px) {
    .modern-form-container {
        padding: var(--spacing-md);
    }

    .form-progress-bar {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .progress-step {
        margin: 0;
    }

    .progress-step:not(:last-child)::after {
        display: none;
    }

    .cost-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .form-navigation {
        flex-direction: column;
        align-items: stretch;
    }

    .form-navigation .btn {
        min-width: auto;
    }
}

/* النموذج الشامل لتسجيل العمل */
.comprehensive-work-form {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    background: var(--bg-primary);
}

/* رأس النموذج */
.work-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--text-on-primary);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
}

.work-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    align-items: end;
}

.work-number-section,
.work-date-section,
.doctor-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.work-number-section label,
.work-date-section label,
.doctor-section label {
    font-weight: 600;
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

.work-number-display,
.work-date-display {
    background: rgba(255, 255, 255, 0.2);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-lg);
    font-weight: 700;
    text-align: center;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.doctor-section select {
    background: rgba(255, 255, 255, 0.9);
    color: var(--text-primary);
    border: none;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    font-weight: 600;
}

/* النموذج الرئيسي */
.comprehensive-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.form-main-section {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.patient-prosthetic-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.patient-info-section,
.prosthetic-details-section {
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
}

.patient-info-section h3,
.prosthetic-details-section h3 {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
}

/* مخطط الأسنان الشامل */
.dental-chart-section {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.dental-chart-section h3 {
    color: var(--primary-color);
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.dental-chart-container {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    border: 2px solid var(--primary-color);
}

.jaw-section {
    margin-bottom: var(--spacing-xl);
}

.jaw-label {
    text-align: center;
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--text-on-primary);
    border-radius: var(--border-radius-md);
}

.jaw-sides {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.jaw-side {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.side-label {
    text-align: center;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-base);
}

.teeth-row {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: var(--spacing-sm);
}

.tooth {
    aspect-ratio: 1;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    user-select: none;
    min-height: 60px;
}

.tooth:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.tooth.selected {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-color: var(--primary-color);
    color: var(--text-on-primary);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.tooth-number {
    font-weight: 700;
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.tooth-visual {
    width: 20px;
    height: 25px;
    background: currentColor;
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    opacity: 0.7;
}

/* معلومات الأسنان المختارة */
.selected-teeth-info {
    background: var(--bg-primary);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
    border: 1px solid var(--border-color);
    display: grid;
    grid-template-columns: auto 1fr;
    gap: var(--spacing-lg);
    align-items: center;
}

.selected-count {
    font-weight: 700;
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.selected-teeth-list {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* قسم الحسابات */
.calculations-section {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.calculations-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.dates-section,
.financial-section {
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
}

.dates-section h3,
.financial-section h3 {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
}

.calculation-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.calculation-row:last-child {
    border-bottom: none;
}

.calculation-row.total {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%);
    color: var(--text-on-primary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-weight: 700;
    font-size: var(--font-size-lg);
    margin-top: var(--spacing-md);
}

.calculation-row input {
    width: 80px;
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    text-align: center;
}

/* قسم الملاحظات */
.notes-section {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.notes-section h3 {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
}

.notes-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

/* أزرار الحفظ */
.form-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
}

.form-actions .btn {
    min-width: 150px;
    padding: var(--spacing-md) var(--spacing-lg);
    font-weight: 600;
}

/* تصميم متجاوب */
@media (max-width: 1200px) {
    .patient-prosthetic-grid,
    .calculations-grid,
    .notes-grid {
        grid-template-columns: 1fr;
    }

    .jaw-sides {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .comprehensive-work-form {
        padding: var(--spacing-md);
    }

    .work-info-grid {
        grid-template-columns: 1fr;
    }

    .teeth-row {
        grid-template-columns: repeat(4, 1fr);
    }

    .selected-teeth-info {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .form-actions .btn {
        min-width: auto;
    }
}

/* نموذج تسجيل العمل المبسط */
.work-registration-form {
    max-width: 1000px;
    margin: 0 auto;
    padding: var(--spacing-lg);
}

.work-header-info {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--text-on-primary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-lg);
}

.work-info-row {
    display: flex;
    justify-content: space-around;
    gap: var(--spacing-lg);
}

.info-item {
    text-align: center;
}

.info-item label {
    display: block;
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin-bottom: var(--spacing-xs);
}

.work-number,
.work-date {
    font-size: var(--font-size-xl);
    font-weight: 700;
    background: rgba(255, 255, 255, 0.2);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
}

.work-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.form-section {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
}

.form-section h3 {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.form-group label {
    font-weight: 600;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    transition: all var(--transition-normal);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

/* مخطط الأسنان المبسط */
.dental-chart {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    border: 2px solid var(--primary-color);
}

.jaw {
    margin-bottom: var(--spacing-lg);
}

.jaw-title {
    text-align: center;
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border-radius: var(--border-radius-md);
}

.jaw-sides {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.jaw-side {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
}

.side-title {
    text-align: center;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.teeth-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: var(--spacing-xs);
}

.tooth {
    aspect-ratio: 1;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    min-height: 50px;
}

.tooth:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.tooth.selected {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-color: var(--primary-color);
    color: var(--text-on-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.tooth-number {
    font-weight: 700;
    font-size: var(--font-size-sm);
}

.selected-teeth-info {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-top: var(--spacing-md);
    border: 1px solid var(--border-color);
}

.teeth-count {
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.teeth-list {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* الحسابات */
.calculations {
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
}

.calc-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.calc-row:last-child {
    border-bottom: none;
}

.calc-row.total {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%);
    color: var(--text-on-primary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-weight: 700;
    font-size: var(--font-size-lg);
    margin-top: var(--spacing-md);
}

.calc-row input {
    width: 80px;
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    text-align: center;
}

/* أزرار الحفظ */
.form-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
}

.form-actions .btn {
    min-width: 120px;
    padding: var(--spacing-md) var(--spacing-lg);
    font-weight: 600;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .work-registration-form {
        padding: var(--spacing-md);
    }

    .work-info-row {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .jaw-sides {
        grid-template-columns: 1fr;
    }

    .teeth-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .form-actions .btn {
        min-width: auto;
    }
}

/* صفحة إضافة عمل جديد */
.add-work-page {
    min-height: 100vh;
    background: var(--bg-primary);
    font-family: 'Cairo', sans-serif;
}

/* Header */
.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--text-on-primary);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    align-items: center;
    gap: var(--spacing-lg);
}

.header-left,
.header-right {
    display: flex;
    align-items: center;
}

.header-right {
    justify-content: flex-end;
}

.header-center {
    text-align: center;
}

.page-title {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.work-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.work-number,
.work-date {
    background: rgba(255, 255, 255, 0.2);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    text-align: center;
}

/* Main Content */
.main-content {
    margin-right: 280px;
    margin-top: 70px;
    padding: var(--spacing-lg);
    transition: margin-right 0.3s ease;
    min-height: calc(100vh - 70px);
    width: calc(100% - 280px);
    background: var(--bg-primary);
}

/* تنسيقات الشريط الجانبي المطوي */
.sidebar.collapsed ~ .main-content,
.sidebar-collapsed .main-content {
    margin-right: 60px !important;
    width: calc(100% - 60px) !important;
}

.sidebar-collapsed .sidebar {
    width: 60px !important;
}

.work-form-container {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
}

.work-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

/* Form Sections */
.form-section {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.section-header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 3px solid var(--primary-color);
}

.section-header h2 {
    color: var(--primary-color);
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.section-description {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: var(--spacing-xs) 0 0 0;
    font-style: italic;
}

.section-content {
    padding: 0;
}

/* Form Grid */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.form-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-base);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    font-family: inherit;
    transition: all var(--transition-normal);
    background: var(--bg-primary);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
    transform: translateY(-1px);
}

.form-group input:required:invalid {
    border-color: var(--error-color);
}

.form-group input:required:valid {
    border-color: var(--success-color);
}

/* Dental Chart */
.dental-chart {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    border: 2px solid var(--primary-color);
}

.jaw {
    margin-bottom: var(--spacing-xl);
}

.jaw:last-child {
    margin-bottom: 0;
}

.jaw-title {
    text-align: center;
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--text-on-primary);
    border-radius: var(--border-radius-md);
}

.jaw-sides {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.jaw-side {
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
}

.side-title {
    text-align: center;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
    font-size: var(--font-size-base);
}

.teeth-row {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: var(--spacing-sm);
}

.tooth {
    aspect-ratio: 1;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    user-select: none;
    min-height: 60px;
    position: relative;
}

.tooth:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.tooth.selected {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-color: var(--primary-color);
    color: var(--text-on-primary);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.tooth-number {
    font-weight: 700;
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.tooth-visual {
    width: 20px;
    height: 25px;
    background: currentColor;
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    opacity: 0.7;
}

.selected-teeth-info {
    background: var(--bg-primary);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.teeth-summary {
    font-size: var(--font-size-base);
    color: var(--text-primary);
}

/* Calculations */
.calculations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.calc-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
}

.calc-item.total {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%);
    color: var(--text-on-primary);
    font-weight: 700;
    font-size: var(--font-size-lg);
    grid-column: 1 / -1;
}

.calc-item label {
    font-weight: 600;
    margin: 0;
}

.calc-item input {
    width: 80px;
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    text-align: center;
    margin: 0;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
    margin-top: var(--spacing-lg);
}

.form-actions .btn {
    min-width: 150px;
    padding: var(--spacing-md) var(--spacing-lg);
    font-weight: 600;
    font-size: var(--font-size-base);
}

.btn-large {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: var(--font-size-lg);
    min-width: 180px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-content {
        grid-template-columns: auto 1fr auto;
        gap: var(--spacing-md);
    }

    .page-title {
        font-size: var(--font-size-xl);
    }
}

@media (max-width: 768px) {
    .add-work-page {
        font-size: var(--font-size-sm);
    }

    .page-header {
        padding: var(--spacing-md);
    }

    .header-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .main-content {
        margin-right: 0;
        margin-top: 70px;
        width: 100%;
        padding: var(--spacing-md);
    }

    .page-content {
        max-width: 100%;
        padding: var(--spacing-sm);
        margin: var(--spacing-xs);
        border-radius: var(--border-radius-md);
        border-width: 1px;
    }

    .work-form-container {
        padding: var(--spacing-md);
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .jaw-sides {
        grid-template-columns: 1fr;
    }

    .teeth-row {
        grid-template-columns: repeat(4, 1fr);
    }

    .calculations-grid {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .form-actions .btn {
        min-width: auto;
    }
}

/* الواجهة المتكاملة لتسجيل العمل */
.integrated-work-interface {
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    min-height: calc(100vh - 140px);
    max-width: 1200px;
    margin: 0 auto;
    width: auto;
    border-radius: var(--border-radius-lg);
    border: 2px solid var(--primary-color);
    box-shadow: var(--shadow-lg);
}

.interface-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--text-on-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

.interface-title {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.interface-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    height: calc(100vh - 200px);
}

/* Left Panel - Work List */
.work-list-panel {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
    display: flex;
    flex-direction: column;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--primary-color);
}

.panel-header h2 {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.panel-stats {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 600;
}

/* Quick Stats */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.stat-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-card.pending {
    border-left: 4px solid var(--warning-color);
}

.stat-card.progress {
    border-left: 4px solid var(--info-color);
}

.stat-card.completed {
    border-left: 4px solid var(--success-color);
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--text-on-primary);
}

.stat-card.pending .stat-icon {
    background: var(--warning-color);
}

.stat-card.progress .stat-icon {
    background: var(--info-color);
}

.stat-card.completed .stat-icon {
    background: var(--success-color);
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-label {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.stat-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
}

/* Works List */
.works-list-container {
    flex: 1;
    overflow-y: auto;
}

.works-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.work-item {
    background: var(--bg-primary);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.work-item:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
    border-color: var(--primary-color);
}

.work-item.pending {
    border-left: 4px solid var(--warning-color);
}

.work-item.progress {
    border-left: 4px solid var(--info-color);
}

.work-item.completed {
    border-left: 4px solid var(--success-color);
}

.work-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.work-number {
    font-weight: 700;
    color: var(--primary-color);
    font-size: var(--font-size-base);
}

.work-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    color: var(--text-on-primary);
}

.work-status.pending {
    background: var(--warning-color);
}

.work-status.progress {
    background: var(--info-color);
}

.work-status.completed {
    background: var(--success-color);
}

.work-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.work-details > div {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.work-actions {
    display: flex;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-sm);
    justify-content: flex-end;
}

.btn-small {
    padding: var(--spacing-xs);
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
    color: var(--text-on-primary);
    font-size: var(--font-size-xs);
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-small.btn-info {
    background: var(--info-color);
}

.btn-small.btn-danger {
    background: var(--error-color);
}

.btn-small:hover {
    transform: scale(1.1);
}

.no-works {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.no-works i {
    font-size: var(--font-size-xxl);
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

/* Right Panel - New Work Form */
.new-work-panel {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
    display: flex;
    flex-direction: column;
}

.work-info-display {
    display: flex;
    gap: var(--spacing-lg);
    font-size: var(--font-size-sm);
}

.work-number-info,
.work-date-info {
    background: var(--bg-primary);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-color);
}

.new-work-form-container {
    flex: 1;
    overflow-y: auto;
}

.integrated-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.form-section.compact {
    background: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
}

.form-section.compact h3 {
    color: var(--primary-color);
    font-size: var(--font-size-base);
    font-weight: 600;
    margin: 0 0 var(--spacing-md) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding-bottom: var(--spacing-xs);
    border-bottom: 1px solid var(--border-color);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.form-group label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.form-group input,
.form-group select {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    transition: all var(--transition-normal);
}

.form-group input:focus,
.form-group select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    outline: none;
}

/* Integrated Form Actions */
.integrated-form-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.btn-block {
    width: 100%;
    padding: var(--spacing-md);
    font-weight: 600;
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .interface-content {
        grid-template-columns: 1fr;
        height: auto;
    }

    .quick-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .form-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .integrated-work-interface {
        padding: var(--spacing-md);
        max-width: 100%;
        margin: 0 auto;
    }

    .interface-header {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .quick-stats {
        grid-template-columns: 1fr;
    }

    .work-details {
        grid-template-columns: 1fr;
    }
}

/* تصميم النموذج المطور */
.prosthetic-form.modern-form {
    max-width: none;
    padding: 0;
}

.prosthetic-form .form-section {
    margin-bottom: var(--spacing-xl);
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.prosthetic-form .section-header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--primary-color);
}

.prosthetic-form .section-header h3 {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.prosthetic-form .section-description {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: var(--spacing-xs) 0 0 0;
}

.prosthetic-form .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.prosthetic-form .form-group.full-width {
    grid-column: 1 / -1;
}

.prosthetic-form .form-group label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    display: block;
}

.prosthetic-form .form-group input,
.prosthetic-form .form-group select,
.prosthetic-form .form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    transition: all var(--transition-normal);
}

.prosthetic-form .form-group input:focus,
.prosthetic-form .form-group select:focus,
.prosthetic-form .form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

/* أولوية العمل */
.prosthetic-form.priority-urgent .section-header {
    border-bottom-color: var(--warning-color);
}

.prosthetic-form.priority-urgent .section-header h3 {
    color: var(--warning-color);
}

.prosthetic-form.priority-emergency .section-header {
    border-bottom-color: var(--error-color);
}

.prosthetic-form.priority-emergency .section-header h3 {
    color: var(--error-color);
}

/* ملخص التكلفة المطور */
.price-summary.enhanced {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    border: 2px solid var(--primary-color);
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.summary-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
}

.summary-item:hover {
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-2px);
}

.summary-item.total {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-on-primary);
    font-weight: 700;
}

.summary-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: var(--text-on-primary);
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.summary-item.total .summary-icon {
    background: rgba(255, 255, 255, 0.2);
}

.summary-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.summary-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.summary-item.total .summary-label {
    color: rgba(255, 255, 255, 0.9);
}

.summary-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
}

.summary-item.total .summary-value {
    color: var(--text-on-primary);
    font-size: var(--font-size-xl);
}

/* أزرار النموذج المطورة */
.modal-actions.enhanced {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    padding: var(--spacing-xl) 0;
    border-top: 2px solid var(--border-color);
    margin-top: var(--spacing-xl);
}

.btn.btn-large {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: var(--font-size-lg);
    font-weight: 600;
    min-width: 150px;
}

/* حاوي مخطط الأسنان */
.dental-chart-container {
    background: var(--bg-primary);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    border: 2px dashed var(--border-color);
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تصميم متجاوب للنموذج */
@media (max-width: 768px) {
    .prosthetic-form .form-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .summary-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .modal-actions.enhanced {
        flex-direction: column;
        align-items: stretch;
    }

    .btn.btn-large {
        min-width: auto;
    }
}

/* تحسينات الـ scroll */
#mainContent {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) var(--bg-secondary);
}

#mainContent::-webkit-scrollbar {
    width: 8px;
}

#mainContent::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

#mainContent::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
    transition: background var(--transition-normal);
}

#mainContent::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* تحسينات النشاطات الأخيرة */
.activities-list {
    max-height: 400px;
    overflow-y: auto;
    padding-right: var(--spacing-sm);
}

.activities-list::-webkit-scrollbar {
    width: 6px;
}

.activities-list::-webkit-scrollbar-track {
    background: var(--bg-primary);
    border-radius: 3px;
}

.activities-list::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.activities-list::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* تحسين مظهر بطاقات النشاطات */
.activity-item {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    /* إزالة التأثيرات المتحركة */
}

.activity-item:last-child {
    border-bottom: none;
}

/* تصميم متجاوب مع الـ scroll */
@media (max-width: 768px) {
    #mainContent {
        margin-right: 0;
        padding-top: calc(var(--header-height) + var(--spacing-md));
    }

    .page-content {
        padding: var(--spacing-md);
        max-width: 100%;
        margin: var(--spacing-xs);
        border-radius: var(--border-radius-md);
        border-width: 1px;
    }

    .dashboard-container,
    .prosthetics-container,
    .doctors-container,
    .employees-container,
    .financial-container,
    .settings-container,
    .inventory-container {
        border-radius: var(--border-radius-md);
        border-width: 1px;
        padding: var(--spacing-md);
    }

    .activities-list {
        max-height: 300px;
    }
}

/* تصميم متجاوب للأجهزة اللوحية */
@media (max-width: 1024px) and (min-width: 769px) {
    .dashboard-stats-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-md);
    }

    .dashboard-stats-grid .stat-card {
        padding: var(--spacing-md);
    }

    .dashboard-stats-grid .stat-value {
        font-size: var(--font-size-xl);
    }
}

/* تصميم للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .dashboard-stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
    }

    .dashboard-stats-grid .stat-card {
        padding: var(--spacing-sm) var(--spacing-md);
        display: flex;
        align-items: center;
        text-align: right;
        gap: var(--spacing-md);
    }

    .dashboard-stats-grid .stat-header {
        margin-bottom: 0;
    }

    .dashboard-stats-grid .stat-icon {
        margin: 0;
        flex-shrink: 0;
    }

    .dashboard-stats-grid .stat-content {
        flex: 1;
    }

    .dashboard-stats-grid .stat-value {
        font-size: var(--font-size-lg);
        margin-bottom: 0;
    }

    .dashboard-stats-grid .stat-label {
        font-size: var(--font-size-xs);
        margin-top: var(--spacing-xs);
    }
}

@media (max-width: 768px) {
    #sidebar {
        transform: translateX(100%);
    }

    #sidebar.open {
        transform: translateX(0);
    }

    #mainContent {
        margin-right: 0;
        width: 100%;
    }

    .main-content {
        margin-right: 0;
        width: 100%;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-stats-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-xs);
    }

    .dashboard-stats-grid .stat-card {
        padding: var(--spacing-sm);
    }

    .dashboard-stats-grid .stat-icon {
        width: 30px;
        height: 30px;
        font-size: var(--font-size-base);
    }

    .dashboard-stats-grid .stat-value {
        font-size: var(--font-size-xl);
    }

    .dashboard-stats-grid .stat-label {
        font-size: var(--font-size-xs);
    }

    .page-header {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    .page-actions {
        flex-wrap: wrap;
        justify-content: center;
    }

    /* تحسينات الشريط العلوي للجوال */
    .top-bar-left {
        gap: var(--spacing-sm);
    }

    .lab-info {
        gap: var(--spacing-sm);
    }

    .lab-logo {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-lg);
    }

    .lab-details h2 {
        font-size: var(--font-size-lg);
    }

    .lab-details small {
        font-size: var(--font-size-xs);
    }

    .top-bar-right {
        gap: var(--spacing-sm);
    }

    .user-details {
        display: none; /* إخفاء تفاصيل المستخدم في الجوال */
    }
}

@media (max-width: 480px) {
    .lab-details h2 {
        font-size: var(--font-size-base);
    }

    .lab-details small {
        display: none; /* إخفاء العنوان الفرعي في الشاشات الصغيرة جداً */
    }

    .lab-logo {
        width: 35px;
        height: 35px;
        font-size: var(--font-size-base);
    }
}

/* لوحة الإعدادات السريعة */
.quick-settings-panel {
    position: fixed;
    width: 700px;
    max-width: calc(100vw - 40px);
    max-height: calc(100vh - 120px);
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    z-index: 1000;
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
    transition: all var(--transition-normal);
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.quick-settings-panel.show {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.quick-settings-panel.closing {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
}

/* تأكد من أن الزر له موضع نسبي */
.quick-settings-toggle {
    position: relative;
}

/* تحسين موضع النافذة للتأكد من عدم خروجها من الشاشة */
.quick-settings-panel {
    /* تحديد الحد الأدنى للمسافة من حافة الشاشة */
    min-width: 350px;
}

/* تعديل موضع النافذة إذا كانت ستخرج من الشاشة */
@media (max-width: 480px) {
    .quick-settings-panel {
        position: fixed;
        top: 80px;
        left: 10px;
        right: 10px;
        width: auto;
        min-width: auto;
        transform: translateY(-10px) scale(0.95);
    }

    .quick-settings-panel.show {
        transform: translateY(0) scale(1);
    }
}

.quick-settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--text-white);
    border-bottom: 1px solid var(--border-color);
}

.quick-settings-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.quick-settings-header .close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: var(--text-white);
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.quick-settings-header .close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* جسم الإعدادات مع القائمة الجانبية */
.quick-settings-body {
    display: flex;
    height: calc(100vh - 200px);
    max-height: 500px;
}

/* القائمة الجانبية */
.settings-sidebar {
    width: 200px;
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    overflow-y: auto;
}

.settings-nav {
    padding: var(--spacing-md);
}

.settings-nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    width: 100%;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-xs);
    background: transparent;
    border: none;
    border-radius: var(--border-radius-md);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-align: right;
}

.settings-nav-item:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.settings-nav-item.active {
    background: var(--primary-color);
    color: var(--text-white);
    box-shadow: 0 2px 8px rgba(var(--primary-color), 0.3);
}

.settings-nav-item i {
    font-size: var(--font-size-base);
    width: 20px;
    text-align: center;
}

/* محتوى الإعدادات */
.settings-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.settings-tab {
    display: none;
}

.settings-tab.active {
    display: block;
}

.settings-section {
    margin-bottom: var(--spacing-xl);
}

.settings-section h4 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid rgba(var(--border-color), 0.3);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item label {
    font-weight: 500;
    color: var(--text-primary);
    flex: 1;
}

.setting-control {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-width: 150px;
    justify-content: flex-end;
}

/* أزرار الوضع */
.theme-option {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.theme-option:hover {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

.theme-option.active {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
    box-shadow: 0 0 10px rgba(var(--primary-color), 0.3);
}

/* مفاتيح التبديل */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--text-muted);
    transition: var(--transition-fast);
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: var(--transition-fast);
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* حقول الإدخال */
.setting-control input[type="text"],
.setting-control input[type="number"],
.setting-control select {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    min-width: 120px;
}

.setting-control input[type="range"] {
    flex: 1;
    margin-left: var(--spacing-sm);
}

.range-value {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    min-width: 40px;
    text-align: center;
}

/* رفع الشعار */
.logo-upload-section {
    padding: var(--spacing-lg);
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius-lg);
    text-align: center;
    background: var(--bg-primary);
}

.current-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    justify-content: center;
}

.logo-preview {
    width: 80px;
    height: 80px;
    border-radius: var(--border-radius-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-size: var(--font-size-2xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.logo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.logo-info {
    text-align: right;
}

.logo-info p {
    margin: 0;
    font-weight: 600;
    color: var(--text-primary);
}

.logo-info small {
    color: var(--text-secondary);
}

.logo-upload-controls {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
    flex-wrap: wrap;
}

/* حقول النص الطويلة */
.setting-control textarea {
    width: 100%;
    min-height: 80px;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    font-family: inherit;
    resize: vertical;
}

/* أزرار النسخ الاحتياطي */
.backup-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
    flex-wrap: wrap;
}

.backup-actions .btn {
    flex: 1;
    min-width: 150px;
}

.quick-settings-footer {
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.quick-settings-footer .btn {
    flex: 1;
    min-width: 100px;
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .quick-settings-panel {
        top: 70px;
        right: 10px;
        left: 10px;
        width: auto;
        max-width: none;
    }

    .quick-settings-body {
        flex-direction: column;
        height: auto;
        max-height: calc(100vh - 160px);
    }

    .settings-sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }

    .settings-nav {
        display: flex;
        overflow-x: auto;
        padding: var(--spacing-sm);
        gap: var(--spacing-xs);
    }

    .settings-nav-item {
        flex-shrink: 0;
        margin-bottom: 0;
        white-space: nowrap;
        min-width: 120px;
        justify-content: center;
    }

    .settings-nav-item span {
        display: none;
    }

    .settings-nav-item i {
        margin: 0;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .setting-control {
        width: 100%;
        justify-content: flex-start;
    }

    .quick-settings-footer {
        flex-direction: column;
    }

    .quick-settings-footer .btn {
        width: 100%;
    }

    .current-logo {
        flex-direction: column;
        text-align: center;
    }

    .logo-upload-controls {
        flex-direction: column;
    }

    .backup-actions {
        flex-direction: column;
    }
}

/* تأثيرات إضافية */
.no-animations * {
    animation: none !important;
    transition: none !important;
}

/* تحسينات إضافية */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.loading-spinner i {
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.no-data-message {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.no-data-message i {
    font-size: var(--font-size-4xl);
    color: var(--text-muted);
    margin-bottom: var(--spacing-md);
}

/* تأثيرات الهوفر المتقدمة */
.hover-lift {
    transition: all var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
}

/* الخطوط المخصصة */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

/* تحسينات الأداء */
* {
    will-change: auto;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* الشريط السفلي */
.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 280px; /* مساحة للشريط الجانبي */
    height: 60px;
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-primary));
    border-top: 1px solid var(--border-color);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    z-index: 100;
    transition: all var(--transition-normal);
}

.bottom-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 var(--spacing-xl);
}

.current-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-primary);
    font-weight: 600;
    font-size: var(--font-size-base);
}

.current-section i {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.datetime-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.time-display {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-primary);
    font-weight: 600;
    font-size: var(--font-size-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.time-display i {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.date-display {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.gregorian-date,
.hijri-date {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.gregorian-date i {
    color: var(--success-color);
}

.hijri-date i {
    color: var(--warning-color);
}

/* تأثيرات الحركة للشريط السفلي */
.bottom-bar {
    animation: slideUpFromBottom 0.5s ease-out;
}

@keyframes slideUpFromBottom {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* تحديث الوقت بتأثير نبضة */
.time-display.updating {
    animation: timePulse 0.3s ease-in-out;
}

@keyframes timePulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* التصميم المتجاوب للشريط السفلي */
@media (max-width: 768px) {
    .bottom-bar {
        right: 0; /* إزالة مساحة الشريط الجانبي */
    }

    .bottom-bar-content {
        padding: 0 var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-xs);
        height: auto;
        padding-top: var(--spacing-sm);
        padding-bottom: var(--spacing-sm);
    }

    .bottom-bar {
        height: auto;
        min-height: 60px;
    }

    .datetime-info {
        gap: var(--spacing-md);
        flex-wrap: wrap;
        justify-content: center;
    }

    .date-display {
        flex-direction: row;
        gap: var(--spacing-md);
    }

    .current-section {
        font-size: var(--font-size-sm);
    }

    .time-display {
        font-size: var(--font-size-base);
    }

    .gregorian-date,
    .hijri-date {
        font-size: var(--font-size-xs);
    }
}

@media (max-width: 480px) {
    .bottom-bar-content {
        padding: var(--spacing-sm);
    }

    .datetime-info {
        flex-direction: column;
        gap: var(--spacing-xs);
        align-items: center;
    }

    .date-display {
        flex-direction: column;
        gap: var(--spacing-xs);
        align-items: center;
    }
}
