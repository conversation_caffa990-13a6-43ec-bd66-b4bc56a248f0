// إدارة الأطباء
class Doctors {
    static currentDoctors = [];
    static filteredDoctors = [];
    static currentPage = 1;
    static pageSize = 10;
    static searchQuery = '';
    static searchTimeout = null;

    // عرض صفحة الأطباء
    static render() {
        const pageContent = document.getElementById('pageContent');
        
        pageContent.innerHTML = `
            <div class="doctors-container">
                <!-- عنوان الإدارة المطور -->
                <div class="department-header doctors">
                    <div class="department-header-content">
                        <div class="department-icon">
                            <i class="fas fa-user-md"></i>
                        </div>
                        <div class="department-info">
                            <h1 class="department-name" data-text="إدارة الأطباء">إدارة الأطباء</h1>
                            <p class="department-description">إدارة شاملة لجميع الأطباء المتعاملين مع المعمل ومتابعة أعمالهم</p>

                            <!-- 📊 الإحصائيات تحت العنوان مباشرة -->
                            <div class="header-stats">
                                <div class="header-stat-item">
                                    <div class="header-stat-icon"><i class="fas fa-user-md"></i></div>
                                    <div class="header-stat-content">
                                        <span class="header-stat-number" id="total-doctors-count">0</span>
                                        <span class="header-stat-label">إجمالي الأطباء</span>
                                    </div>
                                </div>
                                <div class="header-stat-item">
                                    <div class="header-stat-icon"><i class="fas fa-heartbeat"></i></div>
                                    <div class="header-stat-content">
                                        <span class="header-stat-number" id="active-doctors-count">0</span>
                                        <span class="header-stat-label">نشطين</span>
                                    </div>
                                </div>
                                <div class="header-stat-item">
                                    <div class="header-stat-icon"><i class="fas fa-tooth"></i></div>
                                    <div class="header-stat-content">
                                        <span class="header-stat-number" id="doctors-prosthetics-count">0</span>
                                        <span class="header-stat-label">إجمالي الأعمال</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="page-actions-container">
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="Doctors.showAddModal()">
                            <i class="fas fa-plus"></i>
                            إضافة طبيب جديد
                        </button>
                        <button class="btn btn-info" onclick="Doctors.showDoctorsListModal()">
                            <i class="fas fa-list"></i>
                            قائمة الأطباء
                        </button>
                        <button class="btn btn-success" onclick="Doctors.exportData()">
                            <i class="fas fa-download"></i>
                            تصدير البيانات
                        </button>
                    </div>
                </div>

                <!-- أدوات البحث -->
                <div class="filters-container">
                    <div class="search-box">
                        <input type="search" id="doctors-search" placeholder="البحث في الأطباء..." 
                               value="${this.searchQuery}">
                        <i class="fas fa-search"></i>
                    </div>
                    
                    <button class="btn btn-secondary" onclick="Doctors.resetFilters()">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </button>
                </div>

                <!-- جدول الأطباء -->
                <div class="table-container">
                    <div class="table-header">
                        <h3 class="table-title">قائمة الأطباء</h3>
                        <div class="table-info">
                            <span id="doctors-count">0 طبيب</span>
                        </div>
                    </div>
                    
                    <div class="table-wrapper">
                        <table class="data-table" id="doctors-table">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>رقم الهاتف الأساسي</th>
                                    <th>رقم هاتف إضافي</th>
                                    <th>عنوان العيادة</th>
                                    <th>عدد التركيبات</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="doctors-tbody">
                                <!-- سيتم تحميل البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="table-pagination" id="doctors-pagination">
                        <!-- سيتم تحميل التصفح هنا -->
                    </div>
                </div>
            </div>
        `;

        this.loadData();
        this.setupEventListeners();
    }

    // تحميل البيانات
    static loadData() {
        this.currentDoctors = Database.getDoctors();
        this.applyFilters();
        this.updateTable();
        this.updateStats();
    }

    // إعداد مستمعي الأحداث
    static setupEventListeners() {
        // البحث
        const searchInput = document.getElementById('doctors-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchQuery = e.target.value;
                this.currentPage = 1;
                this.applyFilters();
                this.updateTable();
            });
        }
    }

    // تطبيق الفلاتر
    static applyFilters() {
        let filtered = [...this.currentDoctors];

        // فلتر البحث
        if (this.searchQuery) {
            const query = this.searchQuery.toLowerCase();
            filtered = filtered.filter(doctor => 
                doctor.name.toLowerCase().includes(query) ||
                doctor.phone.toLowerCase().includes(query) ||
                doctor.phone2?.toLowerCase().includes(query) ||
                doctor.clinicAddress?.toLowerCase().includes(query)
            );
        }

        this.filteredDoctors = filtered;
    }

    // تحديث الجدول
    static updateTable() {
        const tbody = document.getElementById('doctors-tbody');
        if (!tbody) return;

        // حساب البيانات للصفحة الحالية
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageData = this.filteredDoctors.slice(startIndex, endIndex);

        // حساب عدد التركيبات لكل طبيب
        const prosthetics = Database.getProsthetics();
        const doctorProstheticsCount = {};
        prosthetics.forEach(p => {
            doctorProstheticsCount[p.doctorId] = (doctorProstheticsCount[p.doctorId] || 0) + 1;
        });

        // إنشاء صفوف الجدول
        tbody.innerHTML = pageData.map(doctor => `
            <tr onclick="Doctors.viewDetails('${doctor.id}')" class="table-row-clickable">
                <td>
                    <div class="doctor-info">
                        <strong>${doctor.name}</strong>
                        ${doctor.specialization ? `<div class="doctor-specialization">${doctor.specialization}</div>` : ''}
                    </div>
                </td>
                <td>
                    <a href="tel:${doctor.phone}" class="phone-link">
                        <i class="fas fa-phone"></i>
                        ${doctor.phone}
                    </a>
                </td>
                <td>
                    ${doctor.phone2 ? `
                        <a href="tel:${doctor.phone2}" class="phone-link">
                            <i class="fas fa-phone"></i>
                            ${doctor.phone2}
                        </a>
                    ` : '-'}
                </td>
                <td>${doctor.clinicAddress || '-'}</td>
                <td>
                    <span class="prosthetics-count-badge">
                        ${doctorProstheticsCount[doctor.id] || 0}
                    </span>
                </td>
                <td>${Utils.formatDate(doctor.createdAt, 'DD/MM/YYYY')}</td>
                <td class="actions-cell" onclick="event.stopPropagation()">
                    <div class="action-buttons">
                        <button class="btn-icon btn-primary" onclick="Doctors.editDoctor('${doctor.id}')" 
                                title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon btn-warning" onclick="Doctors.managePrices('${doctor.id}')" 
                                title="إدارة الأسعار">
                            <i class="fas fa-dollar-sign"></i>
                        </button>
                        <button class="btn-icon btn-info" onclick="Doctors.viewProsthetics('${doctor.id}')" 
                                title="عرض التركيبات">
                            <i class="fas fa-tooth"></i>
                        </button>
                        <button class="btn-icon btn-danger" onclick="Doctors.deleteDoctor('${doctor.id}')" 
                                title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        // إذا لم توجد بيانات
        if (pageData.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="no-data">
                        <div class="no-data-message">
                            <i class="fas fa-user-md"></i>
                            <p>لا يوجد أطباء مطابقون للبحث</p>
                        </div>
                    </td>
                </tr>
            `;
        }

        this.updatePagination();
    }

    // تحديث التصفح
    static updatePagination() {
        const totalPages = Math.ceil(this.filteredDoctors.length / this.pageSize);
        const paginationContainer = document.getElementById('doctors-pagination');
        
        if (!paginationContainer) return;

        let paginationHTML = `
            <div class="pagination-info">
                عرض ${((this.currentPage - 1) * this.pageSize) + 1} إلى 
                ${Math.min(this.currentPage * this.pageSize, this.filteredDoctors.length)} 
                من ${this.filteredDoctors.length} طبيب
            </div>
            <div class="pagination-controls">
        `;

        // زر السابق
        if (this.currentPage > 1) {
            paginationHTML += `
                <button class="btn btn-secondary btn-sm" onclick="Doctors.changePage(${this.currentPage - 1})">
                    <i class="fas fa-chevron-right"></i>
                    السابق
                </button>
            `;
        }

        // أرقام الصفحات
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === this.currentPage ? 'btn-primary' : 'btn-secondary';
            paginationHTML += `
                <button class="btn ${activeClass} btn-sm" onclick="Doctors.changePage(${i})">
                    ${i}
                </button>
            `;
        }

        // زر التالي
        if (this.currentPage < totalPages) {
            paginationHTML += `
                <button class="btn btn-secondary btn-sm" onclick="Doctors.changePage(${this.currentPage + 1})">
                    التالي
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;
        }

        paginationHTML += '</div>';
        paginationContainer.innerHTML = paginationHTML;
    }

    // تغيير الصفحة
    static changePage(page) {
        this.currentPage = page;
        this.updateTable();
    }

    // تحديث الإحصائيات
    static updateStats() {
        const countElement = document.getElementById('doctors-count');
        if (countElement) {
            countElement.textContent = `${this.filteredDoctors.length} طبيب`;
        }

        // تحديث إحصائيات العنوان
        this.updateDepartmentHeaderStats();
    }

    // تحديث إحصائيات عنوان الإدارة
    static updateDepartmentHeaderStats() {
        const allDoctors = this.currentDoctors;
        const prosthetics = Database.getProsthetics();

        // حساب الإحصائيات
        const stats = {
            total: allDoctors.length,
            active: allDoctors.filter(d => {
                // طبيب نشط إذا كان له أعمال في آخر 30 يوم
                const recentWorks = prosthetics.filter(p =>
                    p.doctorId === d.id &&
                    new Date(p.createdAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
                );
                return recentWorks.length > 0;
            }).length,
            totalProsthetics: prosthetics.length
        };

        // تحديث العناصر
        const totalDoctorsCount = document.getElementById('total-doctors-count');
        const activeDoctorsCount = document.getElementById('active-doctors-count');
        const doctorsProstheticsCount = document.getElementById('doctors-prosthetics-count');

        if (totalDoctorsCount) totalDoctorsCount.textContent = stats.total;
        if (activeDoctorsCount) activeDoctorsCount.textContent = stats.active;
        if (doctorsProstheticsCount) doctorsProstheticsCount.textContent = stats.totalProsthetics;
    }

    // إعادة تعيين الفلاتر
    static resetFilters() {
        this.searchQuery = '';
        this.currentPage = 1;

        const searchInput = document.getElementById('doctors-search');
        if (searchInput) searchInput.value = '';

        this.applyFilters();
        this.updateTable();
    }

    // عرض نافذة إضافة طبيب جديد
    static showAddModal() {
        const modalOverlay = document.getElementById('modalOverlay');
        const modalContent = document.getElementById('modalContent');

        if (!modalOverlay || !modalContent) {
            console.error('❌ Modal elements not found!');
            return;
        }

        // إضافة المحتوى
        modalContent.innerHTML = this.getAddDoctorModalHTML();

        // عرض النافذة
        modalOverlay.style.display = 'flex';
        modalOverlay.classList.add('show');

        // إضافة مستمع النقر للإغلاق
        modalOverlay.onclick = function(e) {
            if (e.target === modalOverlay) {
                Doctors.closeModal();
            }
        };

        // تهيئة النموذج
        this.setupAddForm();
    }

    // إغلاق النافذة
    static closeModal() {
        const modalOverlay = document.getElementById('modalOverlay');
        if (modalOverlay) {
            modalOverlay.style.display = 'none';
            modalOverlay.classList.remove('show');
        }
    }

    // توليد رقم سجل جديد
    static generateDoctorId() {
        const doctors = Database.getDoctors() || [];
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');

        const prefix = `DR-${year}${month}${day}`;
        const todayDoctors = doctors.filter(d => d.doctorId && d.doctorId.startsWith(prefix));
        const nextNumber = String(todayDoctors.length + 1).padStart(3, '0');

        return `${prefix}-${nextNumber}`;
    }

    // HTML نافذة إضافة طبيب
    static getAddDoctorModalHTML() {
        return `
        <div class="modal-frame">
            <div class="modal-frame-header">
                <div class="modal-frame-title">
                    <i class="fas fa-user-md"></i>
                    إضافة طبيب جديد
                </div>
                <button type="button" class="modal-frame-close" onclick="Doctors.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-frame-content">
                <div class="add-doctor-modal">
                    <form id="add-doctor-form" class="doctor-form" onsubmit="Doctors.handleSubmit(event)">
                        <!-- معلومات أساسية -->
                        <div class="form-section">
                            <h3><i class="fas fa-info-circle"></i> المعلومات الأساسية</h3>
                            <div class="form-grid">
                                <div class="form-row">
                                    <label><i class="fas fa-hashtag"></i> رقم السجل</label>
                                    <input type="text" id="doctorId" name="doctorId" readonly value="${this.generateDoctorId()}" class="readonly-input">
                                </div>
                                <div class="form-row">
                                    <label><i class="fas fa-user"></i> اسم الطبيب *</label>
                                    <input type="text" id="doctorName" name="doctorName" required placeholder="الاسم الكامل للطبيب">
                                </div>
                                <div class="form-row">
                                    <label><i class="fas fa-phone"></i> رقم التليفون *</label>
                                    <input type="tel" id="doctorPhone" name="doctorPhone" required placeholder="رقم الهاتف الأساسي">
                                </div>
                                <div class="form-row">
                                    <label><i class="fas fa-phone-alt"></i> رقم هاتف آخر</label>
                                    <input type="tel" id="doctorPhone2" name="doctorPhone2" placeholder="رقم هاتف إضافي">
                                </div>
                            </div>
                        </div>

                        <!-- عنوان العيادة -->
                        <div class="form-section">
                            <h3><i class="fas fa-map-marker-alt"></i> عنوان العيادة</h3>
                            <div class="form-grid">
                                <div class="form-row full-width">
                                    <label><i class="fas fa-building"></i> العنوان الكامل</label>
                                    <textarea id="clinicAddress" name="clinicAddress" rows="3" placeholder="عنوان العيادة بالتفصيل"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="form-section">
                            <h3><i class="fas fa-plus-circle"></i> معلومات إضافية</h3>
                            <div class="form-grid">
                                <div class="form-row">
                                    <label><i class="fas fa-stethoscope"></i> التخصص</label>
                                    <input type="text" id="specialization" name="specialization" placeholder="مثل: طب الأسنان العام">
                                </div>
                                <div class="form-row">
                                    <label><i class="fas fa-envelope"></i> البريد الإلكتروني</label>
                                    <input type="email" id="email" name="email" placeholder="<EMAIL>">
                                </div>
                                <div class="form-row full-width">
                                    <label><i class="fas fa-sticky-note"></i> ملاحظات</label>
                                    <textarea id="notes" name="notes" rows="2" placeholder="أي ملاحظات إضافية"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="form-actions">
                            <button type="submit" class="btn-action btn-save">
                                <i class="fas fa-save"></i> حفظ الطبيب
                            </button>
                            <button type="button" class="btn-action btn-clear" onclick="Doctors.clearForm()">
                                <i class="fas fa-eraser"></i> إخلاء الحقول
                            </button>
                            <button type="button" class="btn-action btn-list" onclick="Doctors.showDoctorsList()">
                                <i class="fas fa-list"></i> قائمة الأطباء
                            </button>
                            <button type="button" class="btn-action btn-close" onclick="Doctors.closeModal()">
                                <i class="fas fa-times"></i> إغلاق
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <style>
        /* إطار النافذة المنبثقة */
        .modal-frame {
            width: 95vw;
            max-width: 800px;
            height: 90vh;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            margin: auto;
            position: relative;
            overflow: hidden;
            border: 3px solid #e5e7eb;
            display: flex;
            flex-direction: column;
        }

        /* رأس الإطار */
        .modal-frame-header {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            padding: 1rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #065f46;
            flex-shrink: 0;
        }

        .modal-frame-title {
            font-size: 1.25rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .modal-frame-title i {
            font-size: 1.5rem;
            color: #d1fae5;
        }

        .modal-frame-close {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-frame-close:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: scale(1.05);
        }

        /* محتوى الإطار مع التمرير */
        .modal-frame-content {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 0;
        }

        /* شريط التمرير المخصص */
        .modal-frame-content::-webkit-scrollbar {
            width: 12px;
        }

        .modal-frame-content::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 6px;
        }

        .modal-frame-content::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            border-radius: 6px;
            border: 2px solid #f1f5f9;
        }

        .modal-frame-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #047857 0%, #065f46 100%);
        }

        /* النافذة الرئيسية */
        .add-doctor-modal {
            background: transparent;
            border-radius: 0;
            padding: 2rem;
            box-shadow: none;
            margin: 0;
            position: relative;
            overflow: visible;
        }

        /* أقسام النموذج */
        .form-section {
            background: #ffffff;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid #e5e7eb;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .form-section h3 {
            margin: 0 0 1rem 0;
            color: #059669;
            font-size: 1.1rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-bottom: 2px solid #d1fae5;
            padding-bottom: 0.5rem;
        }

        .form-section h3 i {
            color: #047857;
        }

        /* شبكة النموذج */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }

        .form-row {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-row.full-width {
            grid-column: 1 / -1;
        }

        .form-row label {
            font-weight: 600;
            color: #374151;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-row label i {
            color: #059669;
            width: 16px;
        }

        .form-row input,
        .form-row textarea {
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            background: #ffffff;
        }

        .form-row input:focus,
        .form-row textarea:focus {
            outline: none;
            border-color: #059669;
            box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
        }

        .readonly-input {
            background: #f9fafb !important;
            color: #6b7280 !important;
            cursor: not-allowed;
        }

        /* أزرار التحكم */
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 2px solid #e5e7eb;
        }

        .btn-action {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            min-width: 120px;
            justify-content: center;
        }

        .btn-save {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
        }

        .btn-save:hover {
            background: linear-gradient(135deg, #047857 0%, #065f46 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(5, 150, 105, 0.3);
        }

        .btn-clear {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .btn-clear:hover {
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
        }

        .btn-list {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }

        .btn-list:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
        }

        .btn-close {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            color: white;
        }

        .btn-close:hover {
            background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(107, 114, 128, 0.3);
        }

        /* تجاوب */
        @media (max-width: 768px) {
            .modal-frame {
                width: 98vw;
                height: 95vh;
                border-radius: 15px;
            }

            .add-doctor-modal {
                padding: 1rem;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .form-actions {
                flex-direction: column;
                gap: 0.75rem;
            }

            .btn-action {
                width: 100%;
                min-width: auto;
            }
        }

        @media (max-width: 480px) {
            .modal-frame {
                width: 100vw;
                height: 100vh;
                border-radius: 0;
                border: none;
            }

            .modal-frame-header {
                padding: 0.75rem 1rem;
            }

            .modal-frame-title {
                font-size: 1.1rem;
            }

            .add-doctor-modal {
                padding: 0.75rem;
            }

            .form-section {
                padding: 1rem;
                margin-bottom: 1rem;
            }
        }
        </style>
        `;
    }

    // معالجة إرسال النموذج
    static async handleSubmit(event) {
        event.preventDefault();

        // إظهار مؤشر التحميل
        const submitBtn = event.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
        submitBtn.disabled = true;

        try {
            const formData = new FormData(event.target);
            const doctorData = {
                doctorId: formData.get('doctorId'),
                name: formData.get('doctorName'),
                phone: formData.get('doctorPhone'),
                phone2: formData.get('doctorPhone2'),
                clinicAddress: formData.get('clinicAddress'),
                specialization: formData.get('specialization'),
                email: formData.get('email'),
                notes: formData.get('notes'),
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // التحقق من البيانات المطلوبة
            if (!doctorData.name || !doctorData.phone) {
                throw new Error('يرجى ملء جميع الحقول المطلوبة');
            }

            // حفظ الطبيب بشكل غير متزامن
            await new Promise(resolve => {
                setTimeout(() => {
                    const doctors = Database.getDoctors() || [];
                    doctors.push(doctorData);
                    Database.saveDoctors(doctors);
                    resolve();
                }, 100);
            });

            // إظهار رسالة نجاح
            this.showSuccessMessage('تم حفظ الطبيب بنجاح');

            // إغلاق النافذة وتحديث الجدول
            this.closeModal();

            // تحديث البيانات بشكل غير متزامن
            setTimeout(() => {
                this.refreshDoctorsData();
            }, 200);

        } catch (error) {
            console.error('خطأ في حفظ الطبيب:', error);
            alert(`❌ ${error.message || 'حدث خطأ في حفظ الطبيب'}`);
        } finally {
            // استعادة الزر
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    // إخلاء الحقول
    static clearForm() {
        const form = document.getElementById('add-doctor-form');
        if (!form) return;

        // إخلاء جميع الحقول عدا رقم السجل
        const inputs = form.querySelectorAll('input:not([readonly]), textarea');
        inputs.forEach(input => {
            input.value = '';
        });

        // التركيز على حقل الاسم
        const nameInput = document.getElementById('doctorName');
        if (nameInput) {
            nameInput.focus();
        }
    }

    // إعداد نموذج الإضافة
    static setupAddForm() {
        const form = document.getElementById('add-doctor-form');
        if (!form) return;

        // التركيز على حقل الاسم
        const nameInput = document.getElementById('doctorName');
        if (nameInput) {
            setTimeout(() => nameInput.focus(), 100);
        }

        // إضافة مستمعات للتحقق من صحة البيانات
        const requiredInputs = form.querySelectorAll('input[required]');
        requiredInputs.forEach(input => {
            input.addEventListener('blur', this.validateField);
            input.addEventListener('input', this.clearFieldError);
        });
    }

    // التحقق من صحة الحقل
    static validateField(event) {
        const field = event.target;
        const value = field.value.trim();

        if (field.hasAttribute('required') && !value) {
            field.style.borderColor = '#ef4444';
            field.style.boxShadow = '0 0 0 3px rgba(239, 68, 68, 0.1)';
        } else {
            field.style.borderColor = '#059669';
            field.style.boxShadow = '0 0 0 3px rgba(5, 150, 105, 0.1)';
        }
    }

    // إزالة خطأ الحقل
    static clearFieldError(event) {
        const field = event.target;
        field.style.borderColor = '#e5e7eb';
        field.style.boxShadow = 'none';
    }

    // عرض قائمة الأطباء
    static showDoctorsList() {
        // إغلاق النافذة الحالية
        this.closeModal();

        // إظهار رسالة تحميل
        const loadingMessage = document.createElement('div');
        loadingMessage.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            padding: 1rem 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            z-index: 10000;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        `;
        loadingMessage.innerHTML = `
            <i class="fas fa-spinner fa-spin"></i>
            جاري تحميل قائمة الأطباء...
        `;
        document.body.appendChild(loadingMessage);

        // الانتقال لقائمة الأطباء بعد تأخير قصير
        setTimeout(() => {
            document.body.removeChild(loadingMessage);

            // تحديث الجدول وإظهار قائمة الأطباء
            this.refreshDoctorsData();

            // التمرير لأعلى الصفحة
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });

            // إظهار رسالة نجاح
            this.showSuccessMessage('تم تحديث قائمة الأطباء بنجاح');

        }, 800);
    }

    // إظهار رسالة نجاح
    static showSuccessMessage(message) {
        const successMessage = document.createElement('div');
        successMessage.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
            z-index: 10000;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            animation: slideInRight 0.3s ease-out;
        `;
        successMessage.innerHTML = `
            <i class="fas fa-check-circle"></i>
            ${message}
        `;

        // إضافة CSS للحركة
        if (!document.getElementById('success-animation-styles')) {
            const style = document.createElement('style');
            style.id = 'success-animation-styles';
            style.textContent = `
                @keyframes slideInRight {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                @keyframes slideOutRight {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(successMessage);

        // إزالة الرسالة بعد 3 ثوان
        setTimeout(() => {
            successMessage.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (document.body.contains(successMessage)) {
                    document.body.removeChild(successMessage);
                }
            }, 300);
        }, 3000);
    }

    // عرض نافذة قائمة الأطباء
    static async showDoctorsListModal() {
        const modalOverlay = document.getElementById('modalOverlay');
        const modalContent = document.getElementById('modalContent');

        if (!modalOverlay || !modalContent) {
            console.error('❌ Modal elements not found!');
            return;
        }

        // إظهار مؤشر التحميل
        modalContent.innerHTML = `
            <div style="display: flex; justify-content: center; align-items: center; height: 400px; flex-direction: column; gap: 1rem;">
                <i class="fas fa-spinner fa-spin" style="font-size: 3rem; color: #3b82f6;"></i>
                <div style="font-size: 1.2rem; color: #6b7280;">جاري تحميل قائمة الأطباء...</div>
            </div>
        `;

        // عرض النافذة
        modalOverlay.style.display = 'flex';
        modalOverlay.classList.add('show');

        // تحميل المحتوى بشكل غير متزامن
        setTimeout(async () => {
            try {
                // إضافة المحتوى
                modalContent.innerHTML = this.getDoctorsListModalHTML();

                // إضافة مستمع النقر للإغلاق
                modalOverlay.onclick = function(e) {
                    if (e.target === modalOverlay) {
                        Doctors.closeModal();
                    }
                };

                // تحميل البيانات بشكل غير متزامن
                await new Promise(resolve => {
                    setTimeout(() => {
                        this.loadDoctorsTable();
                        resolve();
                    }, 100);
                });

            } catch (error) {
                console.error('خطأ في تحميل قائمة الأطباء:', error);
                modalContent.innerHTML = `
                    <div style="display: flex; justify-content: center; align-items: center; height: 400px; flex-direction: column; gap: 1rem;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #ef4444;"></i>
                        <div style="font-size: 1.2rem; color: #6b7280;">حدث خطأ في تحميل البيانات</div>
                        <button onclick="Doctors.closeModal()" style="padding: 0.5rem 1rem; background: #6b7280; color: white; border: none; border-radius: 5px;">إغلاق</button>
                    </div>
                `;
            }
        }, 50);
    }

    // HTML نافذة قائمة الأطباء
    static getDoctorsListModalHTML() {
        return `
        <div class="modal-frame">
            <div class="modal-frame-header">
                <div class="modal-frame-title">
                    <i class="fas fa-users"></i>
                    قائمة الأطباء المسجلين
                </div>
                <button type="button" class="modal-frame-close" onclick="Doctors.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-frame-content">
                <div class="doctors-list-modal">
                    <!-- شريط البحث والفلاتر -->
                    <div class="search-section">
                        <div class="search-bar">
                            <input type="text" id="doctorSearch" placeholder="البحث عن طبيب..." onkeyup="Doctors.filterDoctorsTable()">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="filter-buttons">
                            <button class="filter-btn active" onclick="Doctors.filterByStatus('all')">
                                <i class="fas fa-users"></i> الكل
                            </button>
                            <button class="filter-btn" onclick="Doctors.filterByStatus('active')">
                                <i class="fas fa-user-check"></i> نشط
                            </button>
                            <button class="filter-btn" onclick="Doctors.filterByStatus('inactive')">
                                <i class="fas fa-user-times"></i> غير نشط
                            </button>
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="stats-section">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number" id="totalDoctors">0</div>
                                <div class="stat-label">إجمالي الأطباء</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number" id="activeDoctors">0</div>
                                <div class="stat-label">أطباء نشطين</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-calendar-plus"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number" id="newDoctors">0</div>
                                <div class="stat-label">جدد هذا الشهر</div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول الأطباء -->
                    <div class="table-section">
                        <div class="table-container">
                            <table class="doctors-table" id="doctorsTable">
                                <thead>
                                    <tr>
                                        <th>رقم السجل</th>
                                        <th>اسم الطبيب</th>
                                        <th>التخصص</th>
                                        <th>رقم الهاتف</th>
                                        <th>عنوان العيادة</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="doctorsTableBody">
                                    <!-- سيتم ملء البيانات هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="modal-actions">
                        <button type="button" class="btn-action btn-add" onclick="Doctors.showAddModal()">
                            <i class="fas fa-plus"></i> إضافة طبيب جديد
                        </button>
                        <button type="button" class="btn-action btn-refresh" onclick="Doctors.loadDoctorsTable()">
                            <i class="fas fa-sync-alt"></i> تحديث القائمة
                        </button>
                        <button type="button" class="btn-action btn-export" onclick="Doctors.exportDoctorsData()">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                        <button type="button" class="btn-action btn-close" onclick="Doctors.closeModal()">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <style>
        /* نافذة قائمة الأطباء */
        .doctors-list-modal {
            background: transparent;
            padding: 1.5rem;
            overflow: visible;
        }

        /* قسم البحث */
        .search-section {
            background: #ffffff;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid #e5e7eb;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .search-bar {
            position: relative;
            margin-bottom: 1rem;
        }

        .search-bar input {
            width: 100%;
            padding: 0.75rem 3rem 0.75rem 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .search-bar input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .search-bar i {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 0.5rem 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: #ffffff;
            color: #6b7280;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .filter-btn:hover {
            border-color: #3b82f6;
            color: #3b82f6;
        }

        .filter-btn.active {
            background: #3b82f6;
            border-color: #3b82f6;
            color: white;
        }

        /* قسم الإحصائيات */
        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .stat-card {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            display: flex;
            align-items: center;
            gap: 1rem;
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.2);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(59, 130, 246, 0.3);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .stat-info {
            flex: 1;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            line-height: 1;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
            font-weight: 500;
        }

        /* قسم الجدول */
        .table-section {
            background: #ffffff;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid #e5e7eb;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .table-container {
            overflow-x: auto;
            border-radius: 10px;
            border: 1px solid #e5e7eb;
        }

        .doctors-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
        }

        .doctors-table th {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #374151;
            font-weight: 700;
            padding: 1rem 0.75rem;
            text-align: right;
            border-bottom: 2px solid #e5e7eb;
            white-space: nowrap;
        }

        .doctors-table td {
            padding: 1rem 0.75rem;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
        }

        .doctors-table tbody tr {
            transition: all 0.2s ease;
        }

        .doctors-table tbody tr:hover {
            background: #f8fafc;
        }

        /* أزرار الإجراءات في الجدول */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 0.4rem 0.8rem;
            border: none;
            border-radius: 6px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.3rem;
            font-weight: 600;
        }

        .action-btn.edit {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .action-btn.edit:hover {
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
            transform: translateY(-1px);
        }

        .action-btn.prices {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .action-btn.prices:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
        }

        .action-btn.delete {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .action-btn.delete:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
        }

        /* حالة الطبيب */
        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-align: center;
        }

        .status-badge.active {
            background: #d1fae5;
            color: #065f46;
        }

        .status-badge.inactive {
            background: #fee2e2;
            color: #991b1b;
        }

        /* أزرار التحكم */
        .modal-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            padding-top: 1rem;
            border-top: 2px solid #e5e7eb;
        }

        .btn-add {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
        }

        .btn-add:hover {
            background: linear-gradient(135deg, #047857 0%, #065f46 100%);
        }

        .btn-refresh {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }

        .btn-refresh:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
        }

        .btn-export {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .btn-export:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }

        /* تجاوب */
        @media (max-width: 768px) {
            .stats-section {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .action-btn {
                width: 100%;
                justify-content: center;
            }

            .modal-actions {
                flex-direction: column;
            }

            .btn-action {
                width: 100%;
            }
        }
        </style>
        `;
    }

    // تحميل بيانات جدول الأطباء
    static async loadDoctorsTable() {
        const tbody = document.getElementById('doctorsTableBody');

        if (!tbody) return;

        // إظهار مؤشر التحميل
        tbody.innerHTML = `
            <tr>
                <td colspan="8" style="text-align: center; padding: 2rem;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #3b82f6;"></i>
                    <br><br>
                    جاري تحميل البيانات...
                </td>
            </tr>
        `;

        // تحميل البيانات بشكل غير متزامن
        setTimeout(async () => {
            try {
                const doctors = Database.getDoctors() || [];

                // تحديث الإحصائيات
                this.updateDoctorsStats(doctors);

                // مسح الجدول
                tbody.innerHTML = '';

                if (doctors.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="8" style="text-align: center; padding: 2rem; color: #6b7280;">
                                <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                                <br>
                                لا توجد أطباء مسجلين حتى الآن
                                <br>
                                <button class="btn-action btn-add" onclick="Doctors.showAddModal()" style="margin-top: 1rem;">
                                    <i class="fas fa-plus"></i> إضافة أول طبيب
                                </button>
                            </td>
                        </tr>
                    `;
                    return;
                }

                // ملء الجدول بشكل تدريجي لتحسين الأداء
                const fragment = document.createDocumentFragment();

                doctors.forEach((doctor, index) => {
                    const row = document.createElement('tr');

                    // إنشاء الخلايا
                    const doctorIdCell = document.createElement('td');
                    doctorIdCell.textContent = doctor.doctorId || `DR-${index + 1}`;

                    const nameCell = document.createElement('td');
                    nameCell.innerHTML = `
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">
                                ${doctor.name ? doctor.name.charAt(0) : 'د'}
                            </div>
                            <div>
                                <div style="font-weight: 600;">${doctor.name || 'غير محدد'}</div>
                                <div style="font-size: 0.8rem; color: #6b7280;">${doctor.email || ''}</div>
                            </div>
                        </div>
                    `;

                    const specializationCell = document.createElement('td');
                    specializationCell.textContent = doctor.specialization || 'غير محدد';

                    const phoneCell = document.createElement('td');
                    phoneCell.innerHTML = `
                        <div>${doctor.phone || 'غير محدد'}</div>
                        ${doctor.phone2 ? `<div style="font-size: 0.8rem; color: #6b7280;">${doctor.phone2}</div>` : ''}
                    `;

                    const addressCell = document.createElement('td');
                    addressCell.style.maxWidth = '200px';
                    addressCell.style.overflow = 'hidden';
                    addressCell.style.textOverflow = 'ellipsis';
                    addressCell.title = doctor.clinicAddress || 'غير محدد';
                    addressCell.textContent = doctor.clinicAddress || 'غير محدد';

                    const dateCell = document.createElement('td');
                    dateCell.textContent = doctor.createdAt ? new Date(doctor.createdAt).toLocaleDateString('ar-EG') : 'غير محدد';

                    const statusCell = document.createElement('td');
                    statusCell.innerHTML = `
                        <span class="status-badge ${doctor.status || 'active'}">
                            ${doctor.status === 'inactive' ? 'غير نشط' : 'نشط'}
                        </span>
                    `;

                    const actionsCell = document.createElement('td');
                    const actionsDiv = document.createElement('div');
                    actionsDiv.className = 'action-buttons';

                    // زر التعديل
                    const editBtn = document.createElement('button');
                    editBtn.className = 'action-btn edit';
                    editBtn.title = 'تعديل بيانات الطبيب';
                    editBtn.innerHTML = '<i class="fas fa-edit"></i> تعديل';
                    editBtn.onclick = (e) => {
                        e.stopPropagation();
                        const doctorId = doctor.doctorId || doctor.id || `DR-${index + 1}`;
                        console.log('🔧 نقر على زر التعديل للطبيب:', doctorId);
                        Doctors.editDoctor(doctorId);
                    };

                    // زر الأسعار
                    const pricesBtn = document.createElement('button');
                    pricesBtn.className = 'action-btn prices';
                    pricesBtn.title = 'إدارة قائمة الأسعار';
                    pricesBtn.innerHTML = '<i class="fas fa-dollar-sign"></i> قائمة الأسعار';
                    pricesBtn.onclick = (e) => {
                        console.log('🎯 تم النقر على زر الأسعار!');
                        e.stopPropagation();
                        const doctorId = doctor.doctorId || doctor.id || `DR-${index + 1}`;
                        console.log('💰 نقر على زر قائمة الأسعار للطبيب:', doctorId);
                        console.log('🔄 استدعاء دالة managePrices...');
                        console.log('🔍 نوع Doctors:', typeof Doctors);
                        console.log('🔍 نوع managePrices:', typeof Doctors.managePrices);

                        try {
                            if (typeof Doctors.managePrices === 'function') {
                                console.log('✅ استدعاء managePrices...');
                                console.log('🔄 قبل الاستدعاء مباشرة...');

                                // طريقة الاستدعاء الأولى
                                console.log('🔄 الطريقة الأولى: Doctors.managePrices');
                                Doctors.managePrices(doctorId);

                                console.log('🔄 بعد الاستدعاء الأول...');

                                // طريقة بديلة
                                console.log('🔄 الطريقة البديلة: this.managePrices');
                                setTimeout(() => {
                                    try {
                                        Doctors.managePrices.call(Doctors, doctorId);
                                    } catch (err) {
                                        console.error('❌ خطأ في الطريقة البديلة:', err);
                                    }
                                }, 100);

                            } else {
                                console.error('❌ managePrices ليست دالة!');
                                console.log('🔍 نوع managePrices:', typeof Doctors.managePrices);
                                console.log('🔍 محتوى Doctors:', Object.keys(Doctors));
                            }
                        } catch (error) {
                            console.error('❌ خطأ في استدعاء managePrices:', error);
                            console.error('📋 تفاصيل الخطأ:', error.stack);
                        }
                    };

                    // زر الحذف
                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'action-btn delete';
                    deleteBtn.title = 'حذف الطبيب';
                    deleteBtn.innerHTML = '<i class="fas fa-trash"></i> حذف';
                    deleteBtn.onclick = (e) => {
                        e.stopPropagation();
                        const doctorId = doctor.doctorId || doctor.id || `DR-${index + 1}`;
                        console.log('🗑️ نقر على زر الحذف للطبيب:', doctorId);
                        Doctors.deleteDoctor(doctorId);
                    };

                    actionsDiv.appendChild(editBtn);
                    actionsDiv.appendChild(pricesBtn);
                    actionsDiv.appendChild(deleteBtn);
                    actionsCell.appendChild(actionsDiv);

                    // إضافة جميع الخلايا للصف
                    row.appendChild(doctorIdCell);
                    row.appendChild(nameCell);
                    row.appendChild(specializationCell);
                    row.appendChild(phoneCell);
                    row.appendChild(addressCell);
                    row.appendChild(dateCell);
                    row.appendChild(statusCell);
                    row.appendChild(actionsCell);

                    fragment.appendChild(row);
                });

                // إضافة جميع الصفوف دفعة واحدة
                tbody.appendChild(fragment);

            } catch (error) {
                console.error('خطأ في تحميل جدول الأطباء:', error);
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 2rem; color: #ef4444;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                            <br>
                            حدث خطأ في تحميل البيانات
                        </td>
                    </tr>
                `;
            }
        }, 100);
    }

    // تحديث إحصائيات الأطباء
    static updateDoctorsStats(doctors) {
        const totalDoctors = doctors.length;
        const activeDoctors = doctors.filter(d => d.status !== 'inactive').length;

        // حساب الأطباء الجدد هذا الشهر
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();
        const newDoctors = doctors.filter(d => {
            if (!d.createdAt) return false;
            const createdDate = new Date(d.createdAt);
            return createdDate.getMonth() === currentMonth && createdDate.getFullYear() === currentYear;
        }).length;

        // تحديث العناصر
        const totalEl = document.getElementById('totalDoctors');
        const activeEl = document.getElementById('activeDoctors');
        const newEl = document.getElementById('newDoctors');

        if (totalEl) totalEl.textContent = totalDoctors;
        if (activeEl) activeEl.textContent = activeDoctors;
        if (newEl) newEl.textContent = newDoctors;
    }

    // فلترة جدول الأطباء مع تحسين الأداء
    static filterDoctorsTable() {
        // استخدام debouncing لتحسين الأداء
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            const searchTerm = document.getElementById('doctorSearch')?.value?.toLowerCase() || '';
            const table = document.getElementById('doctorsTable');

            if (!table) return;

            const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
            const fragment = document.createDocumentFragment();
            const visibleRows = [];

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let found = false;

                if (searchTerm === '') {
                    found = true;
                } else {
                    for (let j = 0; j < cells.length - 1; j++) { // استثناء عمود الإجراءات
                        if (cells[j].textContent.toLowerCase().includes(searchTerm)) {
                            found = true;
                            break;
                        }
                    }
                }

                row.style.display = found ? '' : 'none';
            }
        }, 300); // تأخير 300ms لتحسين الأداء
    }

    // فلترة حسب الحالة
    static filterByStatus(status) {
        // تحديث أزرار الفلتر
        const filterBtns = document.querySelectorAll('.filter-btn');
        filterBtns.forEach(btn => btn.classList.remove('active'));
        event.target.classList.add('active');

        const table = document.getElementById('doctorsTable');
        const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

        for (let i = 0; i < rows.length; i++) {
            const row = rows[i];
            const statusCell = row.cells[6]; // عمود الحالة

            if (status === 'all') {
                row.style.display = '';
            } else if (status === 'active') {
                row.style.display = statusCell.textContent.includes('نشط') ? '' : 'none';
            } else if (status === 'inactive') {
                row.style.display = statusCell.textContent.includes('غير نشط') ? '' : 'none';
            }
        }
    }

    // تعديل بيانات طبيب
    static editDoctor(doctorId) {
        console.log('🔧 تعديل الطبيب:', doctorId);

        const doctors = Database.getDoctors() || [];
        const doctor = doctors.find(d => d.doctorId === doctorId || d.id === doctorId);

        if (!doctor) {
            alert('❌ لم يتم العثور على الطبيب');
            console.error('الطبيب غير موجود:', doctorId);
            return;
        }

        console.log('✅ تم العثور على الطبيب:', doctor);

        // إغلاق النافذة الحالية
        this.closeModal();

        // عرض نافذة التعديل
        setTimeout(() => {
            this.showEditModal(doctor);
        }, 300);
    }

    // عرض نافذة تعديل الطبيب
    static showEditModal(doctor) {
        const modalOverlay = document.getElementById('modalOverlay');
        const modalContent = document.getElementById('modalContent');

        if (!modalOverlay || !modalContent) {
            console.error('❌ Modal elements not found!');
            return;
        }

        // إضافة المحتوى
        modalContent.innerHTML = this.getEditDoctorModalHTML(doctor);

        // عرض النافذة
        modalOverlay.style.display = 'flex';
        modalOverlay.classList.add('show');

        // إضافة مستمع النقر للإغلاق
        modalOverlay.onclick = function(e) {
            if (e.target === modalOverlay) {
                Doctors.closeModal();
            }
        };

        // تهيئة النموذج
        this.setupEditForm();
    }

    // HTML نافذة تعديل الطبيب
    static getEditDoctorModalHTML(doctor) {
        return `
        <div class="modal-frame">
            <div class="modal-frame-header">
                <div class="modal-frame-title">
                    <i class="fas fa-user-edit"></i>
                    تعديل بيانات الطبيب
                </div>
                <button type="button" class="modal-frame-close" onclick="Doctors.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-frame-content">
                <div class="edit-doctor-modal">
                    <form id="edit-doctor-form" class="doctor-form" onsubmit="Doctors.handleEditSubmit(event)">
                        <input type="hidden" id="editDoctorId" value="${doctor.doctorId || doctor.id}">

                        <!-- معلومات أساسية -->
                        <div class="form-section">
                            <h3><i class="fas fa-info-circle"></i> المعلومات الأساسية</h3>
                            <div class="form-grid">
                                <div class="form-row">
                                    <label><i class="fas fa-hashtag"></i> رقم السجل</label>
                                    <input type="text" readonly value="${doctor.doctorId}" class="readonly-input">
                                </div>
                                <div class="form-row">
                                    <label><i class="fas fa-user"></i> اسم الطبيب *</label>
                                    <input type="text" id="editDoctorName" name="doctorName" required value="${doctor.name || ''}" placeholder="الاسم الكامل للطبيب">
                                </div>
                                <div class="form-row">
                                    <label><i class="fas fa-phone"></i> رقم التليفون *</label>
                                    <input type="tel" id="editDoctorPhone" name="doctorPhone" required value="${doctor.phone || ''}" placeholder="رقم الهاتف الأساسي">
                                </div>
                                <div class="form-row">
                                    <label><i class="fas fa-phone-alt"></i> رقم هاتف آخر</label>
                                    <input type="tel" id="editDoctorPhone2" name="doctorPhone2" value="${doctor.phone2 || ''}" placeholder="رقم هاتف إضافي">
                                </div>
                            </div>
                        </div>

                        <!-- عنوان العيادة -->
                        <div class="form-section">
                            <h3><i class="fas fa-map-marker-alt"></i> عنوان العيادة</h3>
                            <div class="form-grid">
                                <div class="form-row full-width">
                                    <label><i class="fas fa-building"></i> العنوان الكامل</label>
                                    <textarea id="editClinicAddress" name="clinicAddress" rows="3" placeholder="عنوان العيادة بالتفصيل">${doctor.clinicAddress || ''}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="form-section">
                            <h3><i class="fas fa-plus-circle"></i> معلومات إضافية</h3>
                            <div class="form-grid">
                                <div class="form-row">
                                    <label><i class="fas fa-stethoscope"></i> التخصص</label>
                                    <input type="text" id="editSpecialization" name="specialization" value="${doctor.specialization || ''}" placeholder="مثل: طب الأسنان العام">
                                </div>
                                <div class="form-row">
                                    <label><i class="fas fa-envelope"></i> البريد الإلكتروني</label>
                                    <input type="email" id="editEmail" name="email" value="${doctor.email || ''}" placeholder="<EMAIL>">
                                </div>
                                <div class="form-row">
                                    <label><i class="fas fa-toggle-on"></i> حالة الطبيب</label>
                                    <select id="editStatus" name="status" style="padding: 0.75rem; border: 2px solid #e5e7eb; border-radius: 10px;">
                                        <option value="active" ${doctor.status !== 'inactive' ? 'selected' : ''}>نشط</option>
                                        <option value="inactive" ${doctor.status === 'inactive' ? 'selected' : ''}>غير نشط</option>
                                    </select>
                                </div>
                                <div class="form-row full-width">
                                    <label><i class="fas fa-sticky-note"></i> ملاحظات</label>
                                    <textarea id="editNotes" name="notes" rows="2" placeholder="أي ملاحظات إضافية">${doctor.notes || ''}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="form-actions">
                            <button type="submit" class="btn-action btn-save">
                                <i class="fas fa-save"></i> حفظ التعديلات
                            </button>
                            <button type="button" class="btn-action btn-list" onclick="Doctors.showDoctorsListModal()">
                                <i class="fas fa-list"></i> قائمة الأطباء
                            </button>
                            <button type="button" class="btn-action btn-close" onclick="Doctors.closeModal()">
                                <i class="fas fa-times"></i> إغلاق
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        `;
    }

    // إعداد نموذج التعديل
    static setupEditForm() {
        const form = document.getElementById('edit-doctor-form');
        if (!form) return;

        // التركيز على حقل الاسم
        const nameInput = document.getElementById('editDoctorName');
        if (nameInput) {
            setTimeout(() => nameInput.focus(), 100);
        }
    }

    // معالجة إرسال نموذج التعديل
    static handleEditSubmit(event) {
        event.preventDefault();

        const formData = new FormData(event.target);
        const doctorId = document.getElementById('editDoctorId').value;

        const updatedData = {
            doctorId: doctorId,
            name: formData.get('doctorName'),
            phone: formData.get('doctorPhone'),
            phone2: formData.get('doctorPhone2'),
            clinicAddress: formData.get('clinicAddress'),
            specialization: formData.get('specialization'),
            email: formData.get('email'),
            status: formData.get('status'),
            notes: formData.get('notes'),
            updatedAt: new Date().toISOString()
        };

        // التحقق من البيانات المطلوبة
        if (!updatedData.name || !updatedData.phone) {
            alert('⚠️ يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        // تحديث البيانات
        try {
            const doctors = Database.getDoctors() || [];
            const doctorIndex = doctors.findIndex(d => d.doctorId === doctorId || d.id === doctorId);

            if (doctorIndex === -1) {
                alert('❌ لم يتم العثور على الطبيب');
                console.error('الطبيب غير موجود للتحديث:', doctorId);
                return;
            }

            console.log('✅ تحديث بيانات الطبيب:', updatedData);

            // الاحتفاظ بالبيانات الأصلية
            doctors[doctorIndex] = { ...doctors[doctorIndex], ...updatedData };
            Database.saveDoctors(doctors);

            // إظهار رسالة نجاح
            alert('✅ تم تحديث بيانات الطبيب بنجاح');

            // العودة لقائمة الأطباء
            this.showDoctorsListModal();

        } catch (error) {
            console.error('خطأ في تحديث الطبيب:', error);
            alert('❌ حدث خطأ في تحديث البيانات');
        }
    }

    // إدارة قائمة أسعار الطبيب
    static managePrices(doctorId) {
        console.log('🚀🚀🚀 ENTERED managePrices function with doctorId:', doctorId);
        console.log('💰 بدء إدارة أسعار الطبيب:', doctorId);

        // إضافة تأكيد إضافي
        console.log('✅ تم دخول الدالة بنجاح!');

        try {
            const doctors = Database.getDoctors() || [];
            console.log('📋 عدد الأطباء المحملين:', doctors.length);

            const doctor = doctors.find(d => d.doctorId === doctorId || d.id === doctorId);

            if (!doctor) {
                console.error('❌ الطبيب غير موجود:', doctorId);
                console.log('📋 الأطباء المتاحين:', doctors.map(d => ({id: d.id, doctorId: d.doctorId, name: d.name})));
                alert('❌ لم يتم العثور على الطبيب');
                return;
            }

            console.log('✅ تم العثور على الطبيب لقائمة الأسعار:', doctor);

            // إغلاق النافذة الحالية
            console.log('🔄 إغلاق النافذة الحالية...');
            this.closeModal();

            // عرض نافذة إدارة الأسعار
            console.log('⏳ انتظار 300ms قبل فتح نافذة الأسعار...');
            setTimeout(() => {
                console.log('🚀 فتح نافذة الأسعار...');
                this.showPricesModal(doctor);
            }, 300);

        } catch (error) {
            console.error('❌ خطأ في managePrices:', error);
            alert('❌ حدث خطأ في فتح نافذة الأسعار');
        }
    }

    // عرض نافذة إدارة الأسعار
    static showPricesModal(doctor) {
        console.log('💰 بدء عرض نافذة قائمة الأسعار للطبيب:', doctor);

        const modalOverlay = document.getElementById('modalOverlay');
        const modalContent = document.getElementById('modalContent');

        console.log('🔍 فحص عناصر النافذة:', {
            modalOverlay: !!modalOverlay,
            modalContent: !!modalContent
        });

        if (!modalOverlay || !modalContent) {
            console.error('❌ عناصر النافذة غير موجودة!', {
                modalOverlay: modalOverlay,
                modalContent: modalContent
            });
            alert('❌ خطأ في عناصر النافذة - تأكد من وجود modalOverlay و modalContent');
            return;
        }

        try {
            // إضافة المحتوى
            console.log('📝 بدء إنشاء محتوى نافذة قائمة الأسعار...');
            const htmlContent = this.getPricesModalHTML(doctor);
            console.log('📄 تم إنشاء HTML بنجاح، الطول:', htmlContent.length);

            modalContent.innerHTML = htmlContent;
            console.log('✅ تم إضافة المحتوى للنافذة');

            // عرض النافذة
            console.log('👁️ عرض النافذة...');
            modalOverlay.style.display = 'flex';
            modalOverlay.classList.add('show');
            console.log('✅ تم عرض النافذة');

            // إضافة مستمع النقر للإغلاق
            modalOverlay.onclick = function(e) {
                if (e.target === modalOverlay) {
                    Doctors.closeModal();
                }
            };
            console.log('✅ تم إضافة مستمع الإغلاق');

            // تحميل قائمة الأسعار
            console.log('📊 بدء تحميل أسعار الطبيب...');
            setTimeout(() => {
                this.loadDoctorPrices(doctor);
            }, 100);

            console.log('🎉 تم عرض نافذة قائمة الأسعار بنجاح');

        } catch (error) {
            console.error('❌ خطأ في عرض نافذة قائمة الأسعار:', error);
            console.error('📋 تفاصيل الخطأ:', error.stack);
            alert('❌ حدث خطأ في عرض نافذة قائمة الأسعار: ' + error.message);
        }
    }

    // HTML نافذة إدارة الأسعار
    static getPricesModalHTML(doctor) {
        console.log('🔧 إنشاء HTML لنافذة الأسعار للطبيب:', doctor);

        try {
            return `
        <div class="modal-frame">
            <div class="modal-frame-header">
                <div class="modal-frame-title">
                    <i class="fas fa-dollar-sign"></i>
                    قائمة الأسعار - د. ${doctor.name}
                </div>
                <button type="button" class="modal-frame-close" onclick="Doctors.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-frame-content">
                <div class="prices-modal">
                    <input type="hidden" id="pricesDoctorId" value="${doctor.doctorId || doctor.id}">

                    <!-- معلومات الطبيب -->
                    <div class="doctor-info-card">
                        <div class="doctor-avatar">
                            ${doctor.name ? doctor.name.charAt(0) : 'د'}
                        </div>
                        <div class="doctor-details">
                            <h3>${doctor.name}</h3>
                            <p>${doctor.specialization || 'طبيب أسنان'}</p>
                            <p><i class="fas fa-phone"></i> ${doctor.phone}</p>
                        </div>
                    </div>

                    <!-- قائمة أسعار التركيبات -->
                    <div class="prices-grid-section">
                        <h3><i class="fas fa-dollar-sign"></i> قائمة أسعار التركيبات</h3>
                        <form id="prices-form" onsubmit="Doctors.savePrices(event)">
                            <div class="prices-grid" id="pricesGrid">
                                <!-- سيتم ملء الشبكة هنا -->
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn-action btn-save-prices">
                                    <i class="fas fa-save"></i> حفظ قائمة الأسعار
                                </button>
                                <button type="button" class="btn-action btn-reset-prices" onclick="Doctors.resetPrices('${doctor.doctorId || doctor.id}')">
                                    <i class="fas fa-undo"></i> إعادة تعيين قائمة الأسعار
                                </button>
                            </div>
                        </form>
                    </div>



                    <!-- أزرار التحكم -->
                    <div class="modal-actions">
                        <button type="button" class="btn-action btn-list" onclick="Doctors.showDoctorsListModal()">
                            <i class="fas fa-list"></i> قائمة الأطباء
                        </button>
                        <button type="button" class="btn-action btn-export" onclick="Doctors.exportDoctorPrices('${doctor.doctorId || doctor.id}')">
                            <i class="fas fa-file-excel"></i> تصدير قائمة الأسعار
                        </button>
                        <button type="button" class="btn-action btn-close" onclick="Doctors.closeModal()">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <style>
        /* نافذة إدارة الأسعار */
        .prices-modal {
            background: transparent;
            padding: 1.5rem;
            overflow: visible;
        }

        /* بطاقة معلومات الطبيب */
        .doctor-info-card {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .doctor-avatar {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .doctor-details h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.3rem;
        }

        .doctor-details p {
            margin: 0.25rem 0;
            opacity: 0.9;
        }

        /* قسم إضافة سعر */
        .add-price-section {
            background: #ffffff;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid #e5e7eb;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .price-form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }

        .btn-add-price {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            width: 100%;
        }

        .btn-add-price:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }

        /* شبكة الأسعار */
        .prices-grid-section {
            background: #ffffff;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid #e5e7eb;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .prices-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .price-item {
            background: #f8fafc;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 1rem;
            transition: all 0.3s ease;
        }

        .price-item:hover {
            border-color: #3b82f6;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
        }

        .price-item.has-value {
            border-color: #10b981;
            background: #f0fdf4;
        }

        .price-item-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
            font-weight: 600;
            color: #374151;
        }

        .price-item-icon {
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }

        .price-input-group {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .price-input {
            flex: 1;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
        }

        .price-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .price-input.has-value {
            border-color: #10b981;
            background: #ffffff;
        }

        .currency-label {
            font-size: 0.9rem;
            color: #6b7280;
            font-weight: 600;
        }

        .btn-save-prices {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 700;
        }

        .btn-save-prices:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
        }

        .btn-reset-prices {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .btn-reset-prices:hover {
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
        }

        /* جدول الأسعار */
        .prices-list-section {
            background: #ffffff;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid #e5e7eb;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .prices-table-container {
            overflow-x: auto;
            border-radius: 10px;
            border: 1px solid #e5e7eb;
        }

        .prices-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
        }

        .prices-table th {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #374151;
            font-weight: 700;
            padding: 1rem 0.75rem;
            text-align: right;
            border-bottom: 2px solid #e5e7eb;
        }

        .prices-table td {
            padding: 1rem 0.75rem;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
        }

        .prices-table tbody tr:hover {
            background: #f8fafc;
        }

        .price-amount {
            font-weight: 700;
            color: #059669;
            font-size: 1.1rem;
        }

        /* تجاوب */
        @media (max-width: 768px) {
            .price-form-grid {
                grid-template-columns: 1fr;
            }

            .doctor-info-card {
                flex-direction: column;
                text-align: center;
            }
        }
        </style>
        `;
        } catch (error) {
            console.error('❌ خطأ في إنشاء HTML نافذة الأسعار:', error);
            return `
                <div style="padding: 2rem; text-align: center;">
                    <h3>❌ خطأ في تحميل نافذة الأسعار</h3>
                    <p>حدث خطأ في إنشاء محتوى النافذة</p>
                    <button onclick="Doctors.closeModal()" class="btn btn-secondary">إغلاق</button>
                </div>
            `;
        }
    }

    // حفظ الطبيب
    static async saveDoctor() {
        const form = document.getElementById('add-doctor-form');
        if (!form) return;

        const formData = new FormData(form);

        // التحقق من صحة البيانات
        const name = formData.get('name').trim();
        const phone = formData.get('phone').trim();

        if (!name) {
            Utils.showNotification('يرجى إدخال اسم الطبيب', 'warning');
            return;
        }

        if (!phone) {
            Utils.showNotification('يرجى إدخال رقم الهاتف', 'warning');
            return;
        }

        if (!Utils.isValidPhone(phone)) {
            Utils.showNotification('رقم الهاتف غير صحيح', 'warning');
            return;
        }

        // التحقق من عدم تكرار رقم الهاتف
        const existingDoctor = this.currentDoctors.find(d => d.phone === phone);
        if (existingDoctor) {
            Utils.showNotification('رقم الهاتف مستخدم بالفعل', 'warning');
            return;
        }

        // جمع بيانات الطبيب
        const doctorData = {
            name: name,
            specialization: formData.get('specialization')?.trim() || '',
            phone: phone,
            phone2: formData.get('phone2')?.trim() || '',
            clinicAddress: formData.get('clinicAddress')?.trim() || '',
            email: formData.get('email')?.trim() || '',
            website: formData.get('website')?.trim() || '',
            notes: formData.get('notes')?.trim() || '',
            customPrices: {} // قائمة أسعار مخصصة فارغة
        };

        // التحقق من صحة البريد الإلكتروني
        if (doctorData.email && !Utils.isValidEmail(doctorData.email)) {
            Utils.showNotification('البريد الإلكتروني غير صحيح', 'warning');
            return;
        }

        // حفظ في قاعدة البيانات
        const saved = Database.addDoctor(doctorData);

        if (saved) {
            Utils.showNotification('تم حفظ الطبيب بنجاح', 'success');
            Utils.closeModal();
            this.loadData();
        } else {
            Utils.showNotification('فشل في حفظ الطبيب', 'error');
        }
    }

    // عرض تفاصيل الطبيب
    static viewDetails(doctorId) {
        const doctor = Database.getDoctorById(doctorId);
        if (!doctor) {
            Utils.showNotification('الطبيب غير موجود', 'error');
            return;
        }

        // حساب إحصائيات الطبيب
        const prosthetics = Database.getProsthetics().filter(p => p.doctorId === doctorId);
        const totalProsthetics = prosthetics.length;
        const pendingProsthetics = prosthetics.filter(p => p.status === 'pending').length;
        const completedProsthetics = prosthetics.filter(p => p.status === 'completed').length;
        const totalRevenue = prosthetics.reduce((sum, p) => sum + (p.totalPrice || 0), 0);

        Utils.createModal({
            title: `تفاصيل الطبيب - ${doctor.name}`,
            content: `
                <div class="doctor-details">
                    <div class="details-grid">
                        <div class="detail-item">
                            <label>الاسم:</label>
                            <span>${doctor.name}</span>
                        </div>
                        ${doctor.specialization ? `
                            <div class="detail-item">
                                <label>التخصص:</label>
                                <span>${doctor.specialization}</span>
                            </div>
                        ` : ''}
                        <div class="detail-item">
                            <label>رقم الهاتف الأساسي:</label>
                            <span>
                                <a href="tel:${doctor.phone}" class="phone-link">
                                    <i class="fas fa-phone"></i>
                                    ${doctor.phone}
                                </a>
                            </span>
                        </div>
                        ${doctor.phone2 ? `
                            <div class="detail-item">
                                <label>رقم هاتف إضافي:</label>
                                <span>
                                    <a href="tel:${doctor.phone2}" class="phone-link">
                                        <i class="fas fa-phone"></i>
                                        ${doctor.phone2}
                                    </a>
                                </span>
                            </div>
                        ` : ''}
                        ${doctor.email ? `
                            <div class="detail-item">
                                <label>البريد الإلكتروني:</label>
                                <span>
                                    <a href="mailto:${doctor.email}" class="email-link">
                                        <i class="fas fa-envelope"></i>
                                        ${doctor.email}
                                    </a>
                                </span>
                            </div>
                        ` : ''}
                        ${doctor.website ? `
                            <div class="detail-item">
                                <label>الموقع الإلكتروني:</label>
                                <span>
                                    <a href="${doctor.website}" target="_blank" class="website-link">
                                        <i class="fas fa-globe"></i>
                                        ${doctor.website}
                                    </a>
                                </span>
                            </div>
                        ` : ''}
                        ${doctor.clinicAddress ? `
                            <div class="detail-item full-width">
                                <label>عنوان العيادة:</label>
                                <span>${doctor.clinicAddress}</span>
                            </div>
                        ` : ''}
                        <div class="detail-item">
                            <label>تاريخ الإضافة:</label>
                            <span>${Utils.formatDate(doctor.createdAt, 'DD/MM/YYYY HH:mm')}</span>
                        </div>
                    </div>

                    <!-- إحصائيات الطبيب -->
                    <div class="doctor-stats">
                        <h4>الإحصائيات</h4>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value">${totalProsthetics}</div>
                                <div class="stat-label">إجمالي التركيبات</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${pendingProsthetics}</div>
                                <div class="stat-label">في الانتظار</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${completedProsthetics}</div>
                                <div class="stat-label">مكتملة</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${Utils.formatCurrency(totalRevenue)}</div>
                                <div class="stat-label">إجمالي الإيرادات</div>
                            </div>
                        </div>
                    </div>

                    ${doctor.notes ? `
                        <div class="detail-section">
                            <label>الملاحظات:</label>
                            <div class="notes-content">${doctor.notes}</div>
                        </div>
                    ` : ''}

                    <div class="modal-actions">
                        <button class="btn btn-primary" onclick="Doctors.editDoctor('${doctor.id}')">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </button>
                        <button class="btn btn-warning" onclick="Doctors.managePrices('${doctor.id}')">
                            <i class="fas fa-dollar-sign"></i>
                            إدارة الأسعار
                        </button>
                        <button class="btn btn-info" onclick="Doctors.viewProsthetics('${doctor.id}')">
                            <i class="fas fa-tooth"></i>
                            عرض التركيبات
                        </button>
                        <button class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            `,
            size: 'large'
        });
    }



    // تحديث الطبيب
    static async updateDoctor() {
        const form = document.getElementById('edit-doctor-form');
        if (!form) return;

        const formData = new FormData(form);
        const doctorId = formData.get('doctorId');

        // التحقق من صحة البيانات
        const name = formData.get('name').trim();
        const phone = formData.get('phone').trim();

        if (!name) {
            Utils.showNotification('يرجى إدخال اسم الطبيب', 'warning');
            return;
        }

        if (!phone) {
            Utils.showNotification('يرجى إدخال رقم الهاتف', 'warning');
            return;
        }

        if (!Utils.isValidPhone(phone)) {
            Utils.showNotification('رقم الهاتف غير صحيح', 'warning');
            return;
        }

        // التحقق من عدم تكرار رقم الهاتف (باستثناء الطبيب الحالي)
        const existingDoctor = this.currentDoctors.find(d => d.phone === phone && d.id !== doctorId);
        if (existingDoctor) {
            Utils.showNotification('رقم الهاتف مستخدم بالفعل', 'warning');
            return;
        }

        const updateData = {
            name: name,
            specialization: formData.get('specialization')?.trim() || '',
            phone: phone,
            phone2: formData.get('phone2')?.trim() || '',
            clinicAddress: formData.get('clinicAddress')?.trim() || '',
            email: formData.get('email')?.trim() || '',
            website: formData.get('website')?.trim() || '',
            notes: formData.get('notes')?.trim() || ''
        };

        // التحقق من صحة البريد الإلكتروني
        if (updateData.email && !Utils.isValidEmail(updateData.email)) {
            Utils.showNotification('البريد الإلكتروني غير صحيح', 'warning');
            return;
        }

        // تحديث في قاعدة البيانات
        const updated = Database.updateDoctor(doctorId, updateData);

        if (updated) {
            Utils.showNotification('تم تحديث الطبيب بنجاح', 'success');
            Utils.closeModal();
            this.loadData();
        } else {
            Utils.showNotification('فشل في تحديث الطبيب', 'error');
        }
    }

    // إدارة أسعار الطبيب
    static managePrices(doctorId) {
        const doctor = Database.getDoctorById(doctorId);
        if (!doctor) return;

        const prostheticTypes = Database.getProstheticTypes();

        Utils.closeModal(); // إغلاق النافذة الحالية

        Utils.createModal({
            title: `إدارة أسعار الطبيب - ${doctor.name}`,
            content: `
                <div class="price-management">
                    <div class="price-info">
                        <p>يمكنك تخصيص أسعار خاصة لهذا الطبيب. إذا لم يتم تحديد سعر خاص، سيتم استخدام السعر الافتراضي.</p>
                    </div>

                    <form id="doctor-prices-form">
                        <input type="hidden" name="doctorId" value="${doctor.id}">

                        ${Object.keys(prostheticTypes).map(category => `
                            <div class="price-category">
                                <h4>${this.getCategoryDisplayName(category)}</h4>
                                <div class="price-items">
                                    ${Object.keys(prostheticTypes[category]).map(item => {
                                        const defaultPrice = prostheticTypes[category][item];
                                        const customPrice = doctor.customPrices?.[category]?.[item] || '';
                                        return `
                                            <div class="price-item">
                                                <label>${item}</label>
                                                <div class="price-inputs">
                                                    <span class="default-price">افتراضي: ${Utils.formatCurrency(defaultPrice)}</span>
                                                    <input type="number"
                                                           name="price_${category}_${item}"
                                                           value="${customPrice}"
                                                           placeholder="سعر مخصص"
                                                           step="0.01">
                                                </div>
                                            </div>
                                        `;
                                    }).join('')}
                                </div>
                            </div>
                        `).join('')}

                        <div class="modal-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                حفظ الأسعار
                            </button>
                            <button type="button" class="btn btn-warning" onclick="Doctors.resetPrices('${doctor.id}')">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين للافتراضي
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="Utils.closeModal()">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            `,
            size: 'large'
        });

        // إعداد نموذج الأسعار
        const form = document.getElementById('doctor-prices-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveDoctorPrices();
            });
        }
    }

    // الحصول على اسم عرض الفئة
    static getCategoryDisplayName(category) {
        const categoryNames = {
            porcelain: 'تركيبات البورسلين',
            zircon: 'تركيبات الزيركون',
            metal: 'التركيبات المعدنية',
            removable: 'الأطقم المتحركة',
            orthodontic: 'أجهزة التقويم',
            additional: 'أعمال إضافية'
        };
        return categoryNames[category] || category;
    }

    // حفظ أسعار الطبيب
    static saveDoctorPrices() {
        const form = document.getElementById('doctor-prices-form');
        if (!form) return;

        const formData = new FormData(form);
        const doctorId = formData.get('doctorId');
        const prostheticTypes = Database.getProstheticTypes();

        const customPrices = {};

        // جمع الأسعار المخصصة
        Object.keys(prostheticTypes).forEach(category => {
            Object.keys(prostheticTypes[category]).forEach(item => {
                const priceValue = formData.get(`price_${category}_${item}`);
                if (priceValue && parseFloat(priceValue) > 0) {
                    if (!customPrices[category]) {
                        customPrices[category] = {};
                    }
                    customPrices[category][item] = parseFloat(priceValue);
                }
            });
        });

        // تحديث في قاعدة البيانات
        const updated = Database.updateDoctor(doctorId, { customPrices });

        if (updated) {
            Utils.showNotification('تم حفظ الأسعار بنجاح', 'success');
            Utils.closeModal();
            this.loadData();
        } else {
            Utils.showNotification('فشل في حفظ الأسعار', 'error');
        }
    }

    // إعادة تعيين أسعار الطبيب للافتراضي
    static async resetPrices(doctorId) {
        const confirmed = await Utils.confirm(
            'هل أنت متأكد من إعادة تعيين جميع الأسعار للقيم الافتراضية؟',
            'تأكيد إعادة التعيين'
        );

        if (confirmed) {
            const updated = Database.updateDoctor(doctorId, { customPrices: {} });

            if (updated) {
                Utils.showNotification('تم إعادة تعيين الأسعار بنجاح', 'success');
                Utils.closeModal();
                this.loadData();
            } else {
                Utils.showNotification('فشل في إعادة تعيين الأسعار', 'error');
            }
        }
    }

    // عرض تركيبات الطبيب
    static viewProsthetics(doctorId) {
        const doctor = Database.getDoctorById(doctorId);
        if (!doctor) return;

        const prosthetics = Database.getProsthetics().filter(p => p.doctorId === doctorId);

        Utils.closeModal(); // إغلاق النافذة الحالية

        Utils.createModal({
            title: `تركيبات الطبيب - ${doctor.name}`,
            content: `
                <div class="doctor-prosthetics">
                    <div class="prosthetics-summary">
                        <div class="summary-stats">
                            <div class="stat-item">
                                <span class="stat-value">${prosthetics.length}</span>
                                <span class="stat-label">إجمالي التركيبات</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">${prosthetics.filter(p => p.status === 'pending').length}</span>
                                <span class="stat-label">في الانتظار</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">${prosthetics.filter(p => p.status === 'completed').length}</span>
                                <span class="stat-label">مكتملة</span>
                            </div>
                        </div>
                    </div>

                    <div class="prosthetics-table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم الحالة</th>
                                    <th>اسم المريض</th>
                                    <th>نوع التركيبة</th>
                                    <th>السعر</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${prosthetics.length > 0 ? prosthetics.map(p => `
                                    <tr onclick="Navigation.loadPage('prosthetics'); Prosthetics.viewDetails('${p.id}')"
                                        class="table-row-clickable">
                                        <td><strong>${p.caseNumber}</strong></td>
                                        <td>${p.patientName}</td>
                                        <td>${Prosthetics.getTypeDisplayName(p.type)}</td>
                                        <td>${Utils.formatCurrency(p.totalPrice)}</td>
                                        <td>
                                            <span class="status-badge status-${p.status}">
                                                ${Prosthetics.getStatusDisplayName(p.status)}
                                            </span>
                                        </td>
                                        <td>${Utils.formatDate(p.createdAt, 'DD/MM/YYYY')}</td>
                                    </tr>
                                `).join('') : `
                                    <tr>
                                        <td colspan="6" class="no-data">
                                            <div class="no-data-message">
                                                <i class="fas fa-tooth"></i>
                                                <p>لا توجد تركيبات لهذا الطبيب</p>
                                            </div>
                                        </td>
                                    </tr>
                                `}
                            </tbody>
                        </table>
                    </div>

                    <div class="modal-actions">
                        <button class="btn btn-primary" onclick="Navigation.loadPage('prosthetics')">
                            <i class="fas fa-tooth"></i>
                            إدارة التركيبات
                        </button>
                        <button class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            `,
            size: 'large'
        });
    }

    // حذف الطبيب
    static async deleteDoctor(doctorId) {
        const doctor = Database.getDoctorById(doctorId);
        if (!doctor) return;

        const confirmed = await Utils.confirm(
            `هل أنت متأكد من حذف الطبيب "${doctor.name}"؟`,
            'تأكيد الحذف'
        );

        if (confirmed) {
            const result = Database.deleteDoctor(doctorId);

            if (result.success) {
                Utils.showNotification('تم حذف الطبيب بنجاح', 'success');
                this.loadData();
            } else {
                Utils.showNotification(result.message || 'فشل في حذف الطبيب', 'error');
            }
        }
    }

    // تصدير البيانات
    static exportData() {
        const data = this.filteredDoctors.map(doctor => ({
            'الاسم': doctor.name,
            'التخصص': doctor.specialization || '',
            'رقم الهاتف الأساسي': doctor.phone,
            'رقم هاتف إضافي': doctor.phone2 || '',
            'عنوان العيادة': doctor.clinicAddress || '',
            'البريد الإلكتروني': doctor.email || '',
            'الموقع الإلكتروني': doctor.website || '',
            'تاريخ الإضافة': Utils.formatDate(doctor.createdAt, 'DD/MM/YYYY'),
            'الملاحظات': doctor.notes || ''
        }));

        const filename = `doctors_${Utils.formatDate(new Date(), 'YYYY-MM-DD')}.csv`;
        Utils.exportToCSV(data, filename);
    }

    // تحديث بيانات الأطباء
    static refreshDoctorsData() {
        // إذا كانت هناك صفحة أطباء مفتوحة، قم بتحديثها
        const doctorsPage = document.querySelector('.doctors-page');
        if (doctorsPage) {
            this.updateTable();
        }
    }

    // تحديث الجدول الرئيسي
    static updateTable() {
        // تحديث الجدول في الصفحة الرئيسية إذا كان موجوداً
        const tableBody = document.querySelector('#doctorsTableBody');
        if (tableBody) {
            this.loadDoctorsTable();
        }

        // تحديث أي جداول أخرى
        this.updateMainTable();
    }

    // تحديث الجدول الرئيسي في صفحة الأطباء
    static updateMainTable() {
        const doctors = Database.getDoctors() || [];

        // تحديث عدد الأطباء في الإحصائيات
        const statsElements = document.querySelectorAll('.stat-number');
        if (statsElements.length > 0) {
            statsElements[0].textContent = doctors.length;
        }

        // تحديث أي عناصر أخرى تحتاج تحديث
        console.log(`✅ تم تحديث بيانات ${doctors.length} طبيب`);
    }

    // حذف طبيب
    static deleteDoctor(doctorId) {
        if (!confirm('⚠️ هل أنت متأكد من حذف هذا الطبيب؟\nسيتم حذف جميع بياناته وأسعاره.')) {
            return;
        }

        try {
            const doctors = Database.getDoctors() || [];
            const doctorIndex = doctors.findIndex(d => d.doctorId === doctorId);

            if (doctorIndex === -1) {
                alert('❌ لم يتم العثور على الطبيب');
                return;
            }

            const doctorName = doctors[doctorIndex].name;
            doctors.splice(doctorIndex, 1);
            Database.saveDoctors(doctors);

            // حذف أسعار الطبيب أيضاً
            const prices = Database.getItem('doctorPrices') || {};
            delete prices[doctorId];
            Database.setItem('doctorPrices', prices);

            alert(`✅ تم حذف الطبيب "${doctorName}" بنجاح`);

            // تحديث الجدول
            this.loadDoctorsTable();

        } catch (error) {
            console.error('خطأ في حذف الطبيب:', error);
            alert('❌ حدث خطأ في حذف الطبيب');
        }
    }

    // تحميل أسعار الطبيب
    static loadDoctorPrices(doctor) {
        console.log('📊 بدء تحميل أسعار الطبيب:', doctor);

        try {
            const doctorId = doctor.doctorId || doctor.id;
            console.log('🆔 معرف الطبيب المستخدم:', doctorId);

            const prices = Database.getItem('doctorPrices') || {};
            const doctorPrices = prices[doctorId] || {};

            console.log('💰 أسعار الطبيب المحفوظة:', doctorPrices);
            console.log('📊 عدد الأسعار المحفوظة:', Object.keys(doctorPrices).length);

            const pricesGrid = document.getElementById('pricesGrid');
            console.log('🔍 فحص عنصر pricesGrid:', !!pricesGrid);

            if (!pricesGrid) {
                console.error('❌ عنصر pricesGrid غير موجود في DOM');
                console.log('🔍 البحث عن عناصر مشابهة:', document.querySelectorAll('[id*="price"]'));
                return;
            }

            console.log('✅ تم العثور على عنصر pricesGrid');

        // قائمة جميع أنواع التركيبات
        const prostheticTypes = [
            { key: 'porcelain', name: 'بورسلين جى سرام', icon: 'tooth' },
            { key: 'porcelain-vita', name: 'بورسلين فيتا', icon: 'tooth' },
            { key: 'porcelain-face', name: 'بورسلين فيس', icon: 'tooth' },
            { key: 'porcelain-company', name: 'بورسلين شركة', icon: 'tooth' },
            { key: 'space-maintainer', name: 'Space maintainer', icon: 'cog' },
            { key: 'partial', name: 'برشل', icon: 'tooth' },
            { key: 'post', name: 'post', icon: 'anchor' },
            { key: 'lingual-arch', name: 'lingual arch', icon: 'link' },
            { key: 'zircon-copy', name: 'zircon copy', icon: 'gem' },
            { key: 'temporary', name: 'مؤقت', icon: 'clock' },
            { key: 'zircon-full', name: 'ZIRCON FULLANTOMY', icon: 'gem' },
            { key: 'wax-proof', name: 'بروفا شمع', icon: 'fire' },
            { key: 'retainer-jaw', name: 'Retainer فكين', icon: 'grip-horizontal' },
            { key: 'night-guard', name: 'NIGHT GUARD', icon: 'shield-alt' },
            { key: 'pand', name: 'Pand', icon: 'tools' },
            { key: 'implant-zircon', name: 'implant zircon', icon: 'screw' },
            { key: 'nance', name: 'Nance appliance', icon: 'cog' },
            { key: 'crown-loop', name: 'crown & loop', icon: 'circle' },
            { key: 'retainer-hard', name: 'Retainer hard', icon: 'grip-horizontal' },
            { key: 'retainer-wire', name: 'سلك Retainer', icon: 'grip-lines' },
            { key: 'zircon-onlay', name: 'Zircon onlay', icon: 'gem' },
            { key: 'cast-measure', name: 'صب مقاس', icon: 'ruler' },
            { key: 'vitallium', name: 'vitallium', icon: 'atom' }
        ];

        // إنشاء شبكة الأسعار
        pricesGrid.innerHTML = '';

        prostheticTypes.forEach(type => {
            const currentPrice = doctorPrices[type.key] || '';
            const hasValue = currentPrice !== '';

            const priceItem = document.createElement('div');
            priceItem.className = `price-item ${hasValue ? 'has-value' : ''}`;
            priceItem.innerHTML = `
                <div class="price-item-header">
                    <div class="price-item-icon">
                        <i class="fas fa-${type.icon}"></i>
                    </div>
                    <span>${type.name}</span>
                </div>
                <div class="price-input-group">
                    <input
                        type="number"
                        class="price-input ${hasValue ? 'has-value' : ''}"
                        name="price_${type.key}"
                        value="${currentPrice}"
                        placeholder="0.00"
                        min="0"
                        step="0.01"
                        onchange="Doctors.updatePriceItem(this)"
                    >
                    <span class="currency-label">ريال</span>
                </div>
            `;
            pricesGrid.appendChild(priceItem);
        });

        console.log(`✅ تم تحميل ${prostheticTypes.length} نوع تركيبة بنجاح`);

        } catch (error) {
            console.error('❌ خطأ في تحميل أسعار الطبيب:', error);
            console.error('📋 تفاصيل الخطأ:', error.stack);

            const pricesGrid = document.getElementById('pricesGrid');
            if (pricesGrid) {
                pricesGrid.innerHTML = `
                    <div style="text-align: center; padding: 2rem; color: #ef4444;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                        <h3>خطأ في تحميل الأسعار</h3>
                        <p>حدث خطأ في تحميل قائمة الأسعار</p>
                        <button onclick="Doctors.loadDoctorPrices(${JSON.stringify(doctor)})" class="btn btn-primary">
                            إعادة المحاولة
                        </button>
                    </div>
                `;
            }
        }
    }

    // الحصول على اسم الخدمة
    static getServiceName(serviceType) {
        const serviceNames = {
            'porcelain': 'بورسلين جى سرام',
            'porcelain-vita': 'بورسلين فيتا',
            'porcelain-face': 'بورسلين فيس',
            'porcelain-company': 'بورسلين شركة',
            'space-maintainer': 'Space maintainer',
            'partial': 'برشل',
            'post': 'post',
            'lingual-arch': 'lingual arch',
            'zircon-copy': 'zircon copy',
            'temporary': 'مؤقت',
            'zircon-full': 'ZIRCON FULLANTOMY',
            'wax-proof': 'بروفا شمع',
            'retainer-jaw': 'Retainer فكين',
            'night-guard': 'NIGHT GUARD',
            'pand': 'Pand',
            'implant-zircon': 'implant zircon',
            'nance': 'Nance appliance',
            'crown-loop': 'crown & loop',
            'retainer-hard': 'Retainer hard',
            'retainer-wire': 'سلك Retainer',
            'zircon-onlay': 'Zircon onlay',
            'cast-measure': 'صب مقاس',
            'vitallium': 'vitallium'
        };

        return serviceNames[serviceType] || serviceType;
    }

    // تحديث عنصر السعر
    static updatePriceItem(input) {
        const priceItem = input.closest('.price-item');
        const hasValue = input.value && parseFloat(input.value) > 0;

        if (hasValue) {
            priceItem.classList.add('has-value');
            input.classList.add('has-value');
        } else {
            priceItem.classList.remove('has-value');
            input.classList.remove('has-value');
        }
    }

    // حفظ جميع الأسعار
    static async savePrices(event) {
        event.preventDefault();
        console.log('💾 بدء حفظ قائمة الأسعار...');

        const submitBtn = event.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
        submitBtn.disabled = true;

        try {
            const formData = new FormData(event.target);
            const doctorId = document.getElementById('pricesDoctorId').value;

            console.log('👨‍⚕️ معرف الطبيب:', doctorId);

            // جمع جميع الأسعار
            const newPrices = {};
            let pricesCount = 0;

            for (const [key, value] of formData.entries()) {
                if (key.startsWith('price_') && value && parseFloat(value) > 0) {
                    const serviceType = key.replace('price_', '');
                    newPrices[serviceType] = parseFloat(value);
                    pricesCount++;
                }
            }

            // حفظ البيانات
            await new Promise(resolve => {
                setTimeout(() => {
                    const allPrices = Database.getItem('doctorPrices') || {};
                    allPrices[doctorId] = newPrices;
                    Database.setItem('doctorPrices', allPrices);
                    resolve();
                }, 200);
            });

            this.showSuccessMessage(`تم حفظ ${pricesCount} سعر بنجاح`);

        } catch (error) {
            console.error('خطأ في حفظ قائمة الأسعار:', error);
            alert('❌ حدث خطأ في حفظ قائمة الأسعار');
        } finally {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }

    // إعادة تعيين الأسعار
    static resetPrices(doctorId) {
        if (!confirm('⚠️ هل أنت متأكد من إعادة تعيين قائمة الأسعار؟\nسيتم مسح جميع الأسعار المحفوظة.')) {
            return;
        }

        try {
            // مسح أسعار الطبيب
            const allPrices = Database.getItem('doctorPrices') || {};
            delete allPrices[doctorId];
            Database.setItem('doctorPrices', allPrices);

            // إعادة تحميل الشبكة
            const doctor = Database.getDoctors().find(d => d.doctorId === doctorId);
            this.loadDoctorPrices(doctor);

            this.showSuccessMessage('تم إعادة تعيين قائمة الأسعار بنجاح');

        } catch (error) {
            console.error('خطأ في إعادة تعيين قائمة الأسعار:', error);
            alert('❌ حدث خطأ في إعادة تعيين قائمة الأسعار');
        }
    }



    // تصدير أسعار الطبيب
    static exportDoctorPrices(doctorId) {
        try {
            const doctor = Database.getDoctors().find(d => d.doctorId === doctorId);
            const prices = Database.getItem('doctorPrices') || {};
            const doctorPrices = prices[doctorId] || {};

            if (Object.keys(doctorPrices).length === 0) {
                alert('⚠️ لا توجد قائمة أسعار لهذا الطبيب للتصدير');
                return;
            }

            // تحضير البيانات للتصدير
            const exportData = Object.entries(doctorPrices).map(([serviceType, price]) => ({
                'نوع الخدمة': this.getServiceName(serviceType),
                'السعر (ريال)': price,
                'رمز الخدمة': serviceType
            }));

            // تحويل إلى CSV
            const csvContent = this.convertToCSV(exportData);

            // تحميل الملف
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `prices_${doctor.name}_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            alert('✅ تم تصدير قائمة الأسعار بنجاح');

        } catch (error) {
            console.error('خطأ في تصدير قائمة الأسعار:', error);
            alert('❌ حدث خطأ في تصدير قائمة الأسعار');
        }
    }

    // تحويل البيانات إلى CSV
    static convertToCSV(data) {
        if (!data || data.length === 0) return '';

        const headers = Object.keys(data[0]);
        const csvRows = [];

        // إضافة الرؤوس
        csvRows.push(headers.join(','));

        // إضافة البيانات
        for (const row of data) {
            const values = headers.map(header => {
                const value = row[header] || '';
                return `"${value.toString().replace(/"/g, '""')}"`;
            });
            csvRows.push(values.join(','));
        }

        return csvRows.join('\n');
    }

    // دالة اختبار لتشخيص المشاكل
    static testPricesModal() {
        console.log('🧪 اختبار نافذة الأسعار...');

        // إنشاء طبيب وهمي للاختبار
        const testDoctor = {
            doctorId: 'TEST-001',
            id: 'TEST-001',
            name: 'د. اختبار',
            phone: '123456789',
            specialization: 'طب الأسنان'
        };

        console.log('👨‍⚕️ طبيب الاختبار:', testDoctor);

        // اختبار فتح النافذة
        this.managePrices('TEST-001');
    }
}

// إضافة دالة اختبار عامة للتشخيص
window.testDoctorPrices = function(doctorId) {
    console.log('🧪 اختبار مباشر لدالة الأسعار...');
    console.log('📋 معرف الطبيب:', doctorId);

    try {
        console.log('🔍 فحص وجود كلاس Doctors:', typeof Doctors);
        console.log('🔍 فحص وجود دالة managePrices:', typeof Doctors.managePrices);
        console.log('🔍 قائمة دوال Doctors:', Object.getOwnPropertyNames(Doctors));

        if (typeof Doctors.managePrices === 'function') {
            console.log('✅ الدالة موجودة، استدعاؤها...');
            console.log('🔄 قبل الاستدعاء المباشر...');
            Doctors.managePrices(doctorId);
            console.log('🔄 بعد الاستدعاء المباشر...');
        } else {
            console.error('❌ دالة managePrices غير موجودة');
        }
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error);
        console.error('📋 تفاصيل الخطأ:', error.stack);
    }
};

// اختبار فوري عند تحميل الملف
window.testManagePricesDirectly = function() {
    console.log('🔥 اختبار فوري لدالة managePrices...');
    try {
        Doctors.managePrices('TEST-123');
    } catch (error) {
        console.error('❌ خطأ في الاختبار الفوري:', error);
    }
};

console.log('🔧 تم تحميل كلاس Doctors وإضافة دالة الاختبار');
console.log('💡 لاختبار الأسعار، استخدم: testDoctorPrices("DR-20250626-001")');
console.log('🔍 فحص دالة managePrices عند التحميل:', typeof Doctors.managePrices);
console.log('📋 دوال Doctors المتاحة:', Object.getOwnPropertyNames(Doctors).filter(name => typeof Doctors[name] === 'function'));