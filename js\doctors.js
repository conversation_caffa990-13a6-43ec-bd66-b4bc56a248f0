// إدارة الأطباء
class Doctors {
    static currentDoctors = [];
    static filteredDoctors = [];
    static currentPage = 1;
    static pageSize = 10;
    static searchQuery = '';

    // عرض صفحة الأطباء
    static render() {
        const pageContent = document.getElementById('pageContent');
        
        pageContent.innerHTML = `
            <div class="doctors-container">
                <!-- عنوان الإدارة المطور -->
                <div class="department-header doctors">
                    <div class="department-header-content">
                        <div class="department-icon">
                            <i class="fas fa-user-md"></i>
                        </div>
                        <div class="department-info">
                            <h1 class="department-name" data-text="إدارة الأطباء">إدارة الأطباء</h1>
                            <p class="department-description">إدارة شاملة لجميع الأطباء المتعاملين مع المعمل ومتابعة أعمالهم</p>

                            <!-- 📊 الإحصائيات تحت العنوان مباشرة -->
                            <div class="header-stats">
                                <div class="header-stat-item">
                                    <div class="header-stat-icon"><i class="fas fa-user-md"></i></div>
                                    <div class="header-stat-content">
                                        <span class="header-stat-number" id="total-doctors-count">0</span>
                                        <span class="header-stat-label">إجمالي الأطباء</span>
                                    </div>
                                </div>
                                <div class="header-stat-item">
                                    <div class="header-stat-icon"><i class="fas fa-heartbeat"></i></div>
                                    <div class="header-stat-content">
                                        <span class="header-stat-number" id="active-doctors-count">0</span>
                                        <span class="header-stat-label">نشطين</span>
                                    </div>
                                </div>
                                <div class="header-stat-item">
                                    <div class="header-stat-icon"><i class="fas fa-tooth"></i></div>
                                    <div class="header-stat-content">
                                        <span class="header-stat-number" id="doctors-prosthetics-count">0</span>
                                        <span class="header-stat-label">إجمالي الأعمال</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="page-actions-container">
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="Doctors.showAddModal()">
                            <i class="fas fa-plus"></i>
                            إضافة طبيب جديد
                        </button>
                        <button class="btn btn-success" onclick="Doctors.exportData()">
                            <i class="fas fa-download"></i>
                            تصدير البيانات
                        </button>
                    </div>
                </div>

                <!-- أدوات البحث -->
                <div class="filters-container">
                    <div class="search-box">
                        <input type="search" id="doctors-search" placeholder="البحث في الأطباء..." 
                               value="${this.searchQuery}">
                        <i class="fas fa-search"></i>
                    </div>
                    
                    <button class="btn btn-secondary" onclick="Doctors.resetFilters()">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </button>
                </div>

                <!-- جدول الأطباء -->
                <div class="table-container">
                    <div class="table-header">
                        <h3 class="table-title">قائمة الأطباء</h3>
                        <div class="table-info">
                            <span id="doctors-count">0 طبيب</span>
                        </div>
                    </div>
                    
                    <div class="table-wrapper">
                        <table class="data-table" id="doctors-table">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>رقم الهاتف الأساسي</th>
                                    <th>رقم هاتف إضافي</th>
                                    <th>عنوان العيادة</th>
                                    <th>عدد التركيبات</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="doctors-tbody">
                                <!-- سيتم تحميل البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="table-pagination" id="doctors-pagination">
                        <!-- سيتم تحميل التصفح هنا -->
                    </div>
                </div>
            </div>
        `;

        this.loadData();
        this.setupEventListeners();
    }

    // تحميل البيانات
    static loadData() {
        this.currentDoctors = Database.getDoctors();
        this.applyFilters();
        this.updateTable();
        this.updateStats();
    }

    // إعداد مستمعي الأحداث
    static setupEventListeners() {
        // البحث
        const searchInput = document.getElementById('doctors-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchQuery = e.target.value;
                this.currentPage = 1;
                this.applyFilters();
                this.updateTable();
            });
        }
    }

    // تطبيق الفلاتر
    static applyFilters() {
        let filtered = [...this.currentDoctors];

        // فلتر البحث
        if (this.searchQuery) {
            const query = this.searchQuery.toLowerCase();
            filtered = filtered.filter(doctor => 
                doctor.name.toLowerCase().includes(query) ||
                doctor.phone.toLowerCase().includes(query) ||
                doctor.phone2?.toLowerCase().includes(query) ||
                doctor.clinicAddress?.toLowerCase().includes(query)
            );
        }

        this.filteredDoctors = filtered;
    }

    // تحديث الجدول
    static updateTable() {
        const tbody = document.getElementById('doctors-tbody');
        if (!tbody) return;

        // حساب البيانات للصفحة الحالية
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageData = this.filteredDoctors.slice(startIndex, endIndex);

        // حساب عدد التركيبات لكل طبيب
        const prosthetics = Database.getProsthetics();
        const doctorProstheticsCount = {};
        prosthetics.forEach(p => {
            doctorProstheticsCount[p.doctorId] = (doctorProstheticsCount[p.doctorId] || 0) + 1;
        });

        // إنشاء صفوف الجدول
        tbody.innerHTML = pageData.map(doctor => `
            <tr onclick="Doctors.viewDetails('${doctor.id}')" class="table-row-clickable">
                <td>
                    <div class="doctor-info">
                        <strong>${doctor.name}</strong>
                        ${doctor.specialization ? `<div class="doctor-specialization">${doctor.specialization}</div>` : ''}
                    </div>
                </td>
                <td>
                    <a href="tel:${doctor.phone}" class="phone-link">
                        <i class="fas fa-phone"></i>
                        ${doctor.phone}
                    </a>
                </td>
                <td>
                    ${doctor.phone2 ? `
                        <a href="tel:${doctor.phone2}" class="phone-link">
                            <i class="fas fa-phone"></i>
                            ${doctor.phone2}
                        </a>
                    ` : '-'}
                </td>
                <td>${doctor.clinicAddress || '-'}</td>
                <td>
                    <span class="prosthetics-count-badge">
                        ${doctorProstheticsCount[doctor.id] || 0}
                    </span>
                </td>
                <td>${Utils.formatDate(doctor.createdAt, 'DD/MM/YYYY')}</td>
                <td class="actions-cell" onclick="event.stopPropagation()">
                    <div class="action-buttons">
                        <button class="btn-icon btn-primary" onclick="Doctors.editDoctor('${doctor.id}')" 
                                title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon btn-warning" onclick="Doctors.managePrices('${doctor.id}')" 
                                title="إدارة الأسعار">
                            <i class="fas fa-dollar-sign"></i>
                        </button>
                        <button class="btn-icon btn-info" onclick="Doctors.viewProsthetics('${doctor.id}')" 
                                title="عرض التركيبات">
                            <i class="fas fa-tooth"></i>
                        </button>
                        <button class="btn-icon btn-danger" onclick="Doctors.deleteDoctor('${doctor.id}')" 
                                title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        // إذا لم توجد بيانات
        if (pageData.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="no-data">
                        <div class="no-data-message">
                            <i class="fas fa-user-md"></i>
                            <p>لا يوجد أطباء مطابقون للبحث</p>
                        </div>
                    </td>
                </tr>
            `;
        }

        this.updatePagination();
    }

    // تحديث التصفح
    static updatePagination() {
        const totalPages = Math.ceil(this.filteredDoctors.length / this.pageSize);
        const paginationContainer = document.getElementById('doctors-pagination');
        
        if (!paginationContainer) return;

        let paginationHTML = `
            <div class="pagination-info">
                عرض ${((this.currentPage - 1) * this.pageSize) + 1} إلى 
                ${Math.min(this.currentPage * this.pageSize, this.filteredDoctors.length)} 
                من ${this.filteredDoctors.length} طبيب
            </div>
            <div class="pagination-controls">
        `;

        // زر السابق
        if (this.currentPage > 1) {
            paginationHTML += `
                <button class="btn btn-secondary btn-sm" onclick="Doctors.changePage(${this.currentPage - 1})">
                    <i class="fas fa-chevron-right"></i>
                    السابق
                </button>
            `;
        }

        // أرقام الصفحات
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === this.currentPage ? 'btn-primary' : 'btn-secondary';
            paginationHTML += `
                <button class="btn ${activeClass} btn-sm" onclick="Doctors.changePage(${i})">
                    ${i}
                </button>
            `;
        }

        // زر التالي
        if (this.currentPage < totalPages) {
            paginationHTML += `
                <button class="btn btn-secondary btn-sm" onclick="Doctors.changePage(${this.currentPage + 1})">
                    التالي
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;
        }

        paginationHTML += '</div>';
        paginationContainer.innerHTML = paginationHTML;
    }

    // تغيير الصفحة
    static changePage(page) {
        this.currentPage = page;
        this.updateTable();
    }

    // تحديث الإحصائيات
    static updateStats() {
        const countElement = document.getElementById('doctors-count');
        if (countElement) {
            countElement.textContent = `${this.filteredDoctors.length} طبيب`;
        }

        // تحديث إحصائيات العنوان
        this.updateDepartmentHeaderStats();
    }

    // تحديث إحصائيات عنوان الإدارة
    static updateDepartmentHeaderStats() {
        const allDoctors = this.currentDoctors;
        const prosthetics = Database.getProsthetics();

        // حساب الإحصائيات
        const stats = {
            total: allDoctors.length,
            active: allDoctors.filter(d => {
                // طبيب نشط إذا كان له أعمال في آخر 30 يوم
                const recentWorks = prosthetics.filter(p =>
                    p.doctorId === d.id &&
                    new Date(p.createdAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
                );
                return recentWorks.length > 0;
            }).length,
            totalProsthetics: prosthetics.length
        };

        // تحديث العناصر
        const totalDoctorsCount = document.getElementById('total-doctors-count');
        const activeDoctorsCount = document.getElementById('active-doctors-count');
        const doctorsProstheticsCount = document.getElementById('doctors-prosthetics-count');

        if (totalDoctorsCount) totalDoctorsCount.textContent = stats.total;
        if (activeDoctorsCount) activeDoctorsCount.textContent = stats.active;
        if (doctorsProstheticsCount) doctorsProstheticsCount.textContent = stats.totalProsthetics;
    }

    // إعادة تعيين الفلاتر
    static resetFilters() {
        this.searchQuery = '';
        this.currentPage = 1;

        const searchInput = document.getElementById('doctors-search');
        if (searchInput) searchInput.value = '';

        this.applyFilters();
        this.updateTable();
    }

    // عرض نافذة إضافة طبيب جديد
    static showAddModal() {
        const modalOverlay = document.getElementById('modalOverlay');
        const modalContent = document.getElementById('modalContent');

        if (!modalOverlay || !modalContent) {
            console.error('❌ Modal elements not found!');
            return;
        }

        // إضافة المحتوى
        modalContent.innerHTML = this.getAddDoctorModalHTML();

        // عرض النافذة
        modalOverlay.style.display = 'flex';
        modalOverlay.classList.add('show');

        // إضافة مستمع النقر للإغلاق
        modalOverlay.onclick = function(e) {
            if (e.target === modalOverlay) {
                Doctors.closeModal();
            }
        };

        // تهيئة النموذج
        this.setupAddForm();
    }

    // إغلاق النافذة
    static closeModal() {
        const modalOverlay = document.getElementById('modalOverlay');
        if (modalOverlay) {
            modalOverlay.style.display = 'none';
            modalOverlay.classList.remove('show');
        }
    }

    // توليد رقم سجل جديد
    static generateDoctorId() {
        const doctors = Database.getDoctors() || [];
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');

        const prefix = `DR-${year}${month}${day}`;
        const todayDoctors = doctors.filter(d => d.doctorId && d.doctorId.startsWith(prefix));
        const nextNumber = String(todayDoctors.length + 1).padStart(3, '0');

        return `${prefix}-${nextNumber}`;
    }

    // HTML نافذة إضافة طبيب
    static getAddDoctorModalHTML() {
        return `
        <div class="modal-frame">
            <div class="modal-frame-header">
                <div class="modal-frame-title">
                    <i class="fas fa-user-md"></i>
                    إضافة طبيب جديد
                </div>
                <button type="button" class="modal-frame-close" onclick="Doctors.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-frame-content">
                <div class="add-doctor-modal">
                    <form id="add-doctor-form" class="doctor-form" onsubmit="Doctors.handleSubmit(event)">
                        <!-- معلومات أساسية -->
                        <div class="form-section">
                            <h3><i class="fas fa-info-circle"></i> المعلومات الأساسية</h3>
                            <div class="form-grid">
                                <div class="form-row">
                                    <label><i class="fas fa-hashtag"></i> رقم السجل</label>
                                    <input type="text" id="doctorId" name="doctorId" readonly value="${this.generateDoctorId()}" class="readonly-input">
                                </div>
                                <div class="form-row">
                                    <label><i class="fas fa-user"></i> اسم الطبيب *</label>
                                    <input type="text" id="doctorName" name="doctorName" required placeholder="الاسم الكامل للطبيب">
                                </div>
                                <div class="form-row">
                                    <label><i class="fas fa-phone"></i> رقم التليفون *</label>
                                    <input type="tel" id="doctorPhone" name="doctorPhone" required placeholder="رقم الهاتف الأساسي">
                                </div>
                                <div class="form-row">
                                    <label><i class="fas fa-phone-alt"></i> رقم هاتف آخر</label>
                                    <input type="tel" id="doctorPhone2" name="doctorPhone2" placeholder="رقم هاتف إضافي">
                                </div>
                            </div>
                        </div>

                        <!-- عنوان العيادة -->
                        <div class="form-section">
                            <h3><i class="fas fa-map-marker-alt"></i> عنوان العيادة</h3>
                            <div class="form-grid">
                                <div class="form-row full-width">
                                    <label><i class="fas fa-building"></i> العنوان الكامل</label>
                                    <textarea id="clinicAddress" name="clinicAddress" rows="3" placeholder="عنوان العيادة بالتفصيل"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="form-section">
                            <h3><i class="fas fa-plus-circle"></i> معلومات إضافية</h3>
                            <div class="form-grid">
                                <div class="form-row">
                                    <label><i class="fas fa-stethoscope"></i> التخصص</label>
                                    <input type="text" id="specialization" name="specialization" placeholder="مثل: طب الأسنان العام">
                                </div>
                                <div class="form-row">
                                    <label><i class="fas fa-envelope"></i> البريد الإلكتروني</label>
                                    <input type="email" id="email" name="email" placeholder="<EMAIL>">
                                </div>
                                <div class="form-row full-width">
                                    <label><i class="fas fa-sticky-note"></i> ملاحظات</label>
                                    <textarea id="notes" name="notes" rows="2" placeholder="أي ملاحظات إضافية"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="form-actions">
                            <button type="submit" class="btn-action btn-save">
                                <i class="fas fa-save"></i> حفظ الطبيب
                            </button>
                            <button type="button" class="btn-action btn-clear" onclick="Doctors.clearForm()">
                                <i class="fas fa-eraser"></i> إخلاء الحقول
                            </button>
                            <button type="button" class="btn-action btn-close" onclick="Doctors.closeModal()">
                                <i class="fas fa-times"></i> إغلاق
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <style>
        /* إطار النافذة المنبثقة */
        .modal-frame {
            width: 95vw;
            max-width: 800px;
            height: 90vh;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            margin: auto;
            position: relative;
            overflow: hidden;
            border: 3px solid #e5e7eb;
            display: flex;
            flex-direction: column;
        }

        /* رأس الإطار */
        .modal-frame-header {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            padding: 1rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #065f46;
            flex-shrink: 0;
        }

        .modal-frame-title {
            font-size: 1.25rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .modal-frame-title i {
            font-size: 1.5rem;
            color: #d1fae5;
        }

        .modal-frame-close {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-frame-close:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: scale(1.05);
        }

        /* محتوى الإطار مع التمرير */
        .modal-frame-content {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 0;
        }

        /* شريط التمرير المخصص */
        .modal-frame-content::-webkit-scrollbar {
            width: 12px;
        }

        .modal-frame-content::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 6px;
        }

        .modal-frame-content::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            border-radius: 6px;
            border: 2px solid #f1f5f9;
        }

        .modal-frame-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #047857 0%, #065f46 100%);
        }

        /* النافذة الرئيسية */
        .add-doctor-modal {
            background: transparent;
            border-radius: 0;
            padding: 2rem;
            box-shadow: none;
            margin: 0;
            position: relative;
            overflow: visible;
        }

        /* أقسام النموذج */
        .form-section {
            background: #ffffff;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid #e5e7eb;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .form-section h3 {
            margin: 0 0 1rem 0;
            color: #059669;
            font-size: 1.1rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-bottom: 2px solid #d1fae5;
            padding-bottom: 0.5rem;
        }

        .form-section h3 i {
            color: #047857;
        }

        /* شبكة النموذج */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }

        .form-row {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-row.full-width {
            grid-column: 1 / -1;
        }

        .form-row label {
            font-weight: 600;
            color: #374151;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-row label i {
            color: #059669;
            width: 16px;
        }

        .form-row input,
        .form-row textarea {
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            background: #ffffff;
        }

        .form-row input:focus,
        .form-row textarea:focus {
            outline: none;
            border-color: #059669;
            box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
        }

        .readonly-input {
            background: #f9fafb !important;
            color: #6b7280 !important;
            cursor: not-allowed;
        }

        /* أزرار التحكم */
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 2px solid #e5e7eb;
        }

        .btn-action {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            min-width: 120px;
            justify-content: center;
        }

        .btn-save {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
        }

        .btn-save:hover {
            background: linear-gradient(135deg, #047857 0%, #065f46 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(5, 150, 105, 0.3);
        }

        .btn-clear {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .btn-clear:hover {
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
        }

        .btn-close {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            color: white;
        }

        .btn-close:hover {
            background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(107, 114, 128, 0.3);
        }

        /* تجاوب */
        @media (max-width: 768px) {
            .modal-frame {
                width: 98vw;
                height: 95vh;
                border-radius: 15px;
            }

            .add-doctor-modal {
                padding: 1rem;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .form-actions {
                flex-direction: column;
            }

            .btn-action {
                width: 100%;
            }
        }

        @media (max-width: 480px) {
            .modal-frame {
                width: 100vw;
                height: 100vh;
                border-radius: 0;
                border: none;
            }

            .modal-frame-header {
                padding: 0.75rem 1rem;
            }

            .modal-frame-title {
                font-size: 1.1rem;
            }

            .add-doctor-modal {
                padding: 0.75rem;
            }

            .form-section {
                padding: 1rem;
                margin-bottom: 1rem;
            }
        }
        </style>
        `;
    }

    // معالجة إرسال النموذج
    static handleSubmit(event) {
        event.preventDefault();

        const formData = new FormData(event.target);
        const doctorData = {
            doctorId: formData.get('doctorId'),
            name: formData.get('doctorName'),
            phone: formData.get('doctorPhone'),
            phone2: formData.get('doctorPhone2'),
            clinicAddress: formData.get('clinicAddress'),
            specialization: formData.get('specialization'),
            email: formData.get('email'),
            notes: formData.get('notes'),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // التحقق من البيانات المطلوبة
        if (!doctorData.name || !doctorData.phone) {
            alert('⚠️ يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        // حفظ الطبيب
        try {
            const doctors = Database.getDoctors() || [];
            doctors.push(doctorData);
            Database.saveDoctors(doctors);

            // إظهار رسالة نجاح
            alert('✅ تم حفظ الطبيب بنجاح');

            // إغلاق النافذة وتحديث الجدول
            this.closeModal();
            this.loadDoctorsData();
            this.updateTable();

        } catch (error) {
            console.error('خطأ في حفظ الطبيب:', error);
            alert('❌ حدث خطأ في حفظ الطبيب');
        }
    }

    // إخلاء الحقول
    static clearForm() {
        const form = document.getElementById('add-doctor-form');
        if (!form) return;

        // إخلاء جميع الحقول عدا رقم السجل
        const inputs = form.querySelectorAll('input:not([readonly]), textarea');
        inputs.forEach(input => {
            input.value = '';
        });

        // التركيز على حقل الاسم
        const nameInput = document.getElementById('doctorName');
        if (nameInput) {
            nameInput.focus();
        }
    }

    // إعداد نموذج الإضافة
    static setupAddForm() {
        const form = document.getElementById('add-doctor-form');
        if (!form) return;

        // التركيز على حقل الاسم
        const nameInput = document.getElementById('doctorName');
        if (nameInput) {
            setTimeout(() => nameInput.focus(), 100);
        }

        // إضافة مستمعات للتحقق من صحة البيانات
        const requiredInputs = form.querySelectorAll('input[required]');
        requiredInputs.forEach(input => {
            input.addEventListener('blur', this.validateField);
            input.addEventListener('input', this.clearFieldError);
        });
    }

    // التحقق من صحة الحقل
    static validateField(event) {
        const field = event.target;
        const value = field.value.trim();

        if (field.hasAttribute('required') && !value) {
            field.style.borderColor = '#ef4444';
            field.style.boxShadow = '0 0 0 3px rgba(239, 68, 68, 0.1)';
        } else {
            field.style.borderColor = '#059669';
            field.style.boxShadow = '0 0 0 3px rgba(5, 150, 105, 0.1)';
        }
    }

    // إزالة خطأ الحقل
    static clearFieldError(event) {
        const field = event.target;
        field.style.borderColor = '#e5e7eb';
        field.style.boxShadow = 'none';
    }

    // حفظ الطبيب
    static async saveDoctor() {
        const form = document.getElementById('add-doctor-form');
        if (!form) return;

        const formData = new FormData(form);

        // التحقق من صحة البيانات
        const name = formData.get('name').trim();
        const phone = formData.get('phone').trim();

        if (!name) {
            Utils.showNotification('يرجى إدخال اسم الطبيب', 'warning');
            return;
        }

        if (!phone) {
            Utils.showNotification('يرجى إدخال رقم الهاتف', 'warning');
            return;
        }

        if (!Utils.isValidPhone(phone)) {
            Utils.showNotification('رقم الهاتف غير صحيح', 'warning');
            return;
        }

        // التحقق من عدم تكرار رقم الهاتف
        const existingDoctor = this.currentDoctors.find(d => d.phone === phone);
        if (existingDoctor) {
            Utils.showNotification('رقم الهاتف مستخدم بالفعل', 'warning');
            return;
        }

        // جمع بيانات الطبيب
        const doctorData = {
            name: name,
            specialization: formData.get('specialization')?.trim() || '',
            phone: phone,
            phone2: formData.get('phone2')?.trim() || '',
            clinicAddress: formData.get('clinicAddress')?.trim() || '',
            email: formData.get('email')?.trim() || '',
            website: formData.get('website')?.trim() || '',
            notes: formData.get('notes')?.trim() || '',
            customPrices: {} // قائمة أسعار مخصصة فارغة
        };

        // التحقق من صحة البريد الإلكتروني
        if (doctorData.email && !Utils.isValidEmail(doctorData.email)) {
            Utils.showNotification('البريد الإلكتروني غير صحيح', 'warning');
            return;
        }

        // حفظ في قاعدة البيانات
        const saved = Database.addDoctor(doctorData);

        if (saved) {
            Utils.showNotification('تم حفظ الطبيب بنجاح', 'success');
            Utils.closeModal();
            this.loadData();
        } else {
            Utils.showNotification('فشل في حفظ الطبيب', 'error');
        }
    }

    // عرض تفاصيل الطبيب
    static viewDetails(doctorId) {
        const doctor = Database.getDoctorById(doctorId);
        if (!doctor) {
            Utils.showNotification('الطبيب غير موجود', 'error');
            return;
        }

        // حساب إحصائيات الطبيب
        const prosthetics = Database.getProsthetics().filter(p => p.doctorId === doctorId);
        const totalProsthetics = prosthetics.length;
        const pendingProsthetics = prosthetics.filter(p => p.status === 'pending').length;
        const completedProsthetics = prosthetics.filter(p => p.status === 'completed').length;
        const totalRevenue = prosthetics.reduce((sum, p) => sum + (p.totalPrice || 0), 0);

        Utils.createModal({
            title: `تفاصيل الطبيب - ${doctor.name}`,
            content: `
                <div class="doctor-details">
                    <div class="details-grid">
                        <div class="detail-item">
                            <label>الاسم:</label>
                            <span>${doctor.name}</span>
                        </div>
                        ${doctor.specialization ? `
                            <div class="detail-item">
                                <label>التخصص:</label>
                                <span>${doctor.specialization}</span>
                            </div>
                        ` : ''}
                        <div class="detail-item">
                            <label>رقم الهاتف الأساسي:</label>
                            <span>
                                <a href="tel:${doctor.phone}" class="phone-link">
                                    <i class="fas fa-phone"></i>
                                    ${doctor.phone}
                                </a>
                            </span>
                        </div>
                        ${doctor.phone2 ? `
                            <div class="detail-item">
                                <label>رقم هاتف إضافي:</label>
                                <span>
                                    <a href="tel:${doctor.phone2}" class="phone-link">
                                        <i class="fas fa-phone"></i>
                                        ${doctor.phone2}
                                    </a>
                                </span>
                            </div>
                        ` : ''}
                        ${doctor.email ? `
                            <div class="detail-item">
                                <label>البريد الإلكتروني:</label>
                                <span>
                                    <a href="mailto:${doctor.email}" class="email-link">
                                        <i class="fas fa-envelope"></i>
                                        ${doctor.email}
                                    </a>
                                </span>
                            </div>
                        ` : ''}
                        ${doctor.website ? `
                            <div class="detail-item">
                                <label>الموقع الإلكتروني:</label>
                                <span>
                                    <a href="${doctor.website}" target="_blank" class="website-link">
                                        <i class="fas fa-globe"></i>
                                        ${doctor.website}
                                    </a>
                                </span>
                            </div>
                        ` : ''}
                        ${doctor.clinicAddress ? `
                            <div class="detail-item full-width">
                                <label>عنوان العيادة:</label>
                                <span>${doctor.clinicAddress}</span>
                            </div>
                        ` : ''}
                        <div class="detail-item">
                            <label>تاريخ الإضافة:</label>
                            <span>${Utils.formatDate(doctor.createdAt, 'DD/MM/YYYY HH:mm')}</span>
                        </div>
                    </div>

                    <!-- إحصائيات الطبيب -->
                    <div class="doctor-stats">
                        <h4>الإحصائيات</h4>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value">${totalProsthetics}</div>
                                <div class="stat-label">إجمالي التركيبات</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${pendingProsthetics}</div>
                                <div class="stat-label">في الانتظار</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${completedProsthetics}</div>
                                <div class="stat-label">مكتملة</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${Utils.formatCurrency(totalRevenue)}</div>
                                <div class="stat-label">إجمالي الإيرادات</div>
                            </div>
                        </div>
                    </div>

                    ${doctor.notes ? `
                        <div class="detail-section">
                            <label>الملاحظات:</label>
                            <div class="notes-content">${doctor.notes}</div>
                        </div>
                    ` : ''}

                    <div class="modal-actions">
                        <button class="btn btn-primary" onclick="Doctors.editDoctor('${doctor.id}')">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </button>
                        <button class="btn btn-warning" onclick="Doctors.managePrices('${doctor.id}')">
                            <i class="fas fa-dollar-sign"></i>
                            إدارة الأسعار
                        </button>
                        <button class="btn btn-info" onclick="Doctors.viewProsthetics('${doctor.id}')">
                            <i class="fas fa-tooth"></i>
                            عرض التركيبات
                        </button>
                        <button class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            `,
            size: 'large'
        });
    }

    // تعديل الطبيب
    static editDoctor(doctorId) {
        const doctor = Database.getDoctorById(doctorId);
        if (!doctor) {
            Utils.showNotification('الطبيب غير موجود', 'error');
            return;
        }

        Utils.closeModal(); // إغلاق النافذة الحالية

        Utils.createModal({
            title: `تعديل الطبيب - ${doctor.name}`,
            content: `
                <form id="edit-doctor-form" class="doctor-form">
                    <input type="hidden" name="doctorId" value="${doctor.id}">

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="edit-doctor-name">الاسم الكامل *</label>
                            <input type="text" id="edit-doctor-name" name="name" value="${doctor.name}" required>
                        </div>

                        <div class="form-group">
                            <label for="edit-doctor-specialization">التخصص</label>
                            <input type="text" id="edit-doctor-specialization" name="specialization"
                                   value="${doctor.specialization || ''}" placeholder="مثل: طب الأسنان العام">
                        </div>

                        <div class="form-group">
                            <label for="edit-doctor-phone">رقم الهاتف الأساسي *</label>
                            <input type="tel" id="edit-doctor-phone" name="phone" value="${doctor.phone}" required>
                        </div>

                        <div class="form-group">
                            <label for="edit-doctor-phone2">رقم هاتف إضافي</label>
                            <input type="tel" id="edit-doctor-phone2" name="phone2" value="${doctor.phone2 || ''}">
                        </div>

                        <div class="form-group full-width">
                            <label for="edit-doctor-clinic-address">عنوان العيادة</label>
                            <textarea id="edit-doctor-clinic-address" name="clinicAddress" rows="2">${doctor.clinicAddress || ''}</textarea>
                        </div>

                        <div class="form-group">
                            <label for="edit-doctor-email">البريد الإلكتروني</label>
                            <input type="email" id="edit-doctor-email" name="email" value="${doctor.email || ''}">
                        </div>

                        <div class="form-group">
                            <label for="edit-doctor-website">الموقع الإلكتروني</label>
                            <input type="url" id="edit-doctor-website" name="website" value="${doctor.website || ''}">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit-doctor-notes">ملاحظات</label>
                        <textarea id="edit-doctor-notes" name="notes" rows="3">${doctor.notes || ''}</textarea>
                    </div>

                    <div class="modal-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ التغييرات
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            `,
            size: 'large'
        });

        // إعداد نموذج التعديل
        const form = document.getElementById('edit-doctor-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.updateDoctor();
            });
        }
    }

    // تحديث الطبيب
    static async updateDoctor() {
        const form = document.getElementById('edit-doctor-form');
        if (!form) return;

        const formData = new FormData(form);
        const doctorId = formData.get('doctorId');

        // التحقق من صحة البيانات
        const name = formData.get('name').trim();
        const phone = formData.get('phone').trim();

        if (!name) {
            Utils.showNotification('يرجى إدخال اسم الطبيب', 'warning');
            return;
        }

        if (!phone) {
            Utils.showNotification('يرجى إدخال رقم الهاتف', 'warning');
            return;
        }

        if (!Utils.isValidPhone(phone)) {
            Utils.showNotification('رقم الهاتف غير صحيح', 'warning');
            return;
        }

        // التحقق من عدم تكرار رقم الهاتف (باستثناء الطبيب الحالي)
        const existingDoctor = this.currentDoctors.find(d => d.phone === phone && d.id !== doctorId);
        if (existingDoctor) {
            Utils.showNotification('رقم الهاتف مستخدم بالفعل', 'warning');
            return;
        }

        const updateData = {
            name: name,
            specialization: formData.get('specialization')?.trim() || '',
            phone: phone,
            phone2: formData.get('phone2')?.trim() || '',
            clinicAddress: formData.get('clinicAddress')?.trim() || '',
            email: formData.get('email')?.trim() || '',
            website: formData.get('website')?.trim() || '',
            notes: formData.get('notes')?.trim() || ''
        };

        // التحقق من صحة البريد الإلكتروني
        if (updateData.email && !Utils.isValidEmail(updateData.email)) {
            Utils.showNotification('البريد الإلكتروني غير صحيح', 'warning');
            return;
        }

        // تحديث في قاعدة البيانات
        const updated = Database.updateDoctor(doctorId, updateData);

        if (updated) {
            Utils.showNotification('تم تحديث الطبيب بنجاح', 'success');
            Utils.closeModal();
            this.loadData();
        } else {
            Utils.showNotification('فشل في تحديث الطبيب', 'error');
        }
    }

    // إدارة أسعار الطبيب
    static managePrices(doctorId) {
        const doctor = Database.getDoctorById(doctorId);
        if (!doctor) return;

        const prostheticTypes = Database.getProstheticTypes();

        Utils.closeModal(); // إغلاق النافذة الحالية

        Utils.createModal({
            title: `إدارة أسعار الطبيب - ${doctor.name}`,
            content: `
                <div class="price-management">
                    <div class="price-info">
                        <p>يمكنك تخصيص أسعار خاصة لهذا الطبيب. إذا لم يتم تحديد سعر خاص، سيتم استخدام السعر الافتراضي.</p>
                    </div>

                    <form id="doctor-prices-form">
                        <input type="hidden" name="doctorId" value="${doctor.id}">

                        ${Object.keys(prostheticTypes).map(category => `
                            <div class="price-category">
                                <h4>${this.getCategoryDisplayName(category)}</h4>
                                <div class="price-items">
                                    ${Object.keys(prostheticTypes[category]).map(item => {
                                        const defaultPrice = prostheticTypes[category][item];
                                        const customPrice = doctor.customPrices?.[category]?.[item] || '';
                                        return `
                                            <div class="price-item">
                                                <label>${item}</label>
                                                <div class="price-inputs">
                                                    <span class="default-price">افتراضي: ${Utils.formatCurrency(defaultPrice)}</span>
                                                    <input type="number"
                                                           name="price_${category}_${item}"
                                                           value="${customPrice}"
                                                           placeholder="سعر مخصص"
                                                           step="0.01">
                                                </div>
                                            </div>
                                        `;
                                    }).join('')}
                                </div>
                            </div>
                        `).join('')}

                        <div class="modal-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                حفظ الأسعار
                            </button>
                            <button type="button" class="btn btn-warning" onclick="Doctors.resetPrices('${doctor.id}')">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين للافتراضي
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="Utils.closeModal()">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            `,
            size: 'large'
        });

        // إعداد نموذج الأسعار
        const form = document.getElementById('doctor-prices-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveDoctorPrices();
            });
        }
    }

    // الحصول على اسم عرض الفئة
    static getCategoryDisplayName(category) {
        const categoryNames = {
            porcelain: 'تركيبات البورسلين',
            zircon: 'تركيبات الزيركون',
            metal: 'التركيبات المعدنية',
            removable: 'الأطقم المتحركة',
            orthodontic: 'أجهزة التقويم',
            additional: 'أعمال إضافية'
        };
        return categoryNames[category] || category;
    }

    // حفظ أسعار الطبيب
    static saveDoctorPrices() {
        const form = document.getElementById('doctor-prices-form');
        if (!form) return;

        const formData = new FormData(form);
        const doctorId = formData.get('doctorId');
        const prostheticTypes = Database.getProstheticTypes();

        const customPrices = {};

        // جمع الأسعار المخصصة
        Object.keys(prostheticTypes).forEach(category => {
            Object.keys(prostheticTypes[category]).forEach(item => {
                const priceValue = formData.get(`price_${category}_${item}`);
                if (priceValue && parseFloat(priceValue) > 0) {
                    if (!customPrices[category]) {
                        customPrices[category] = {};
                    }
                    customPrices[category][item] = parseFloat(priceValue);
                }
            });
        });

        // تحديث في قاعدة البيانات
        const updated = Database.updateDoctor(doctorId, { customPrices });

        if (updated) {
            Utils.showNotification('تم حفظ الأسعار بنجاح', 'success');
            Utils.closeModal();
            this.loadData();
        } else {
            Utils.showNotification('فشل في حفظ الأسعار', 'error');
        }
    }

    // إعادة تعيين أسعار الطبيب للافتراضي
    static async resetPrices(doctorId) {
        const confirmed = await Utils.confirm(
            'هل أنت متأكد من إعادة تعيين جميع الأسعار للقيم الافتراضية؟',
            'تأكيد إعادة التعيين'
        );

        if (confirmed) {
            const updated = Database.updateDoctor(doctorId, { customPrices: {} });

            if (updated) {
                Utils.showNotification('تم إعادة تعيين الأسعار بنجاح', 'success');
                Utils.closeModal();
                this.loadData();
            } else {
                Utils.showNotification('فشل في إعادة تعيين الأسعار', 'error');
            }
        }
    }

    // عرض تركيبات الطبيب
    static viewProsthetics(doctorId) {
        const doctor = Database.getDoctorById(doctorId);
        if (!doctor) return;

        const prosthetics = Database.getProsthetics().filter(p => p.doctorId === doctorId);

        Utils.closeModal(); // إغلاق النافذة الحالية

        Utils.createModal({
            title: `تركيبات الطبيب - ${doctor.name}`,
            content: `
                <div class="doctor-prosthetics">
                    <div class="prosthetics-summary">
                        <div class="summary-stats">
                            <div class="stat-item">
                                <span class="stat-value">${prosthetics.length}</span>
                                <span class="stat-label">إجمالي التركيبات</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">${prosthetics.filter(p => p.status === 'pending').length}</span>
                                <span class="stat-label">في الانتظار</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">${prosthetics.filter(p => p.status === 'completed').length}</span>
                                <span class="stat-label">مكتملة</span>
                            </div>
                        </div>
                    </div>

                    <div class="prosthetics-table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم الحالة</th>
                                    <th>اسم المريض</th>
                                    <th>نوع التركيبة</th>
                                    <th>السعر</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${prosthetics.length > 0 ? prosthetics.map(p => `
                                    <tr onclick="Navigation.loadPage('prosthetics'); Prosthetics.viewDetails('${p.id}')"
                                        class="table-row-clickable">
                                        <td><strong>${p.caseNumber}</strong></td>
                                        <td>${p.patientName}</td>
                                        <td>${Prosthetics.getTypeDisplayName(p.type)}</td>
                                        <td>${Utils.formatCurrency(p.totalPrice)}</td>
                                        <td>
                                            <span class="status-badge status-${p.status}">
                                                ${Prosthetics.getStatusDisplayName(p.status)}
                                            </span>
                                        </td>
                                        <td>${Utils.formatDate(p.createdAt, 'DD/MM/YYYY')}</td>
                                    </tr>
                                `).join('') : `
                                    <tr>
                                        <td colspan="6" class="no-data">
                                            <div class="no-data-message">
                                                <i class="fas fa-tooth"></i>
                                                <p>لا توجد تركيبات لهذا الطبيب</p>
                                            </div>
                                        </td>
                                    </tr>
                                `}
                            </tbody>
                        </table>
                    </div>

                    <div class="modal-actions">
                        <button class="btn btn-primary" onclick="Navigation.loadPage('prosthetics')">
                            <i class="fas fa-tooth"></i>
                            إدارة التركيبات
                        </button>
                        <button class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            `,
            size: 'large'
        });
    }

    // حذف الطبيب
    static async deleteDoctor(doctorId) {
        const doctor = Database.getDoctorById(doctorId);
        if (!doctor) return;

        const confirmed = await Utils.confirm(
            `هل أنت متأكد من حذف الطبيب "${doctor.name}"؟`,
            'تأكيد الحذف'
        );

        if (confirmed) {
            const result = Database.deleteDoctor(doctorId);

            if (result.success) {
                Utils.showNotification('تم حذف الطبيب بنجاح', 'success');
                this.loadData();
            } else {
                Utils.showNotification(result.message || 'فشل في حذف الطبيب', 'error');
            }
        }
    }

    // تصدير البيانات
    static exportData() {
        const data = this.filteredDoctors.map(doctor => ({
            'الاسم': doctor.name,
            'التخصص': doctor.specialization || '',
            'رقم الهاتف الأساسي': doctor.phone,
            'رقم هاتف إضافي': doctor.phone2 || '',
            'عنوان العيادة': doctor.clinicAddress || '',
            'البريد الإلكتروني': doctor.email || '',
            'الموقع الإلكتروني': doctor.website || '',
            'تاريخ الإضافة': Utils.formatDate(doctor.createdAt, 'DD/MM/YYYY'),
            'الملاحظات': doctor.notes || ''
        }));

        const filename = `doctors_${Utils.formatDate(new Date(), 'YYYY-MM-DD')}.csv`;
        Utils.exportToCSV(data, filename);
    }
}
