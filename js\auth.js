console.log('Loaded: auth.js');
// نظام المصادقة والمستخدمين
class Auth {
    static currentUser = null;
    static sessionKey = 'dentalLab_session';

    // تهيئة نظام المصادقة
    static init() {
        this.checkSession();
        this.setupEventListeners();
    }

    // إعداد مستمعي الأحداث
    static setupEventListeners() {
        const loginForm = document.getElementById('loginForm');
        const logoutBtn = document.getElementById('logoutBtn');
        const resetDbBtn = document.getElementById('resetDbBtn');

        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }

        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.logout();
            });
        }

        if (resetDbBtn) {
            resetDbBtn.addEventListener('click', () => {
                this.resetDatabase();
            });
        }
    }

    // التحقق من الجلسة الحالية
    static checkSession() {
        const session = localStorage.getItem(this.sessionKey);
        if (session) {
            try {
                const sessionData = JSON.parse(session);
                const user = Database.getUserById(sessionData.userId);
                
                if (user && user.isActive && this.isSessionValid(sessionData)) {
                    this.currentUser = user;
                    this.showMainApp();
                    return true;
                }
            } catch (error) {
                console.error('خطأ في التحقق من الجلسة:', error);
            }
        }
        
        this.showLoginScreen();
        return false;
    }

    // التحقق من صحة الجلسة
    static isSessionValid(sessionData) {
        const now = new Date().getTime();
        const sessionTime = new Date(sessionData.timestamp).getTime();
        const maxAge = 24 * 60 * 60 * 1000; // 24 ساعة
        
        return (now - sessionTime) < maxAge;
    }

    // معالجة تسجيل الدخول
    static async handleLogin() {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked;

        console.log('محاولة تسجيل دخول:', { username, password });

        if (!username || !password) {
            Utils.showNotification('يرجى إدخال اسم المستخدم وكلمة المرور', 'warning');
            return;
        }

        // البحث عن المستخدم
        const user = Database.getUserByUsername(username);
        console.log('المستخدم الموجود:', user);

        if (!user) {
            console.log('المستخدم غير موجود');
            Utils.showNotification('اسم المستخدم غير صحيح', 'error');
            return;
        }

        if (!user.isActive) {
            Utils.showNotification('هذا الحساب غير مفعل', 'error');
            return;
        }

        // التحقق من كلمة المرور
        const passwordValid = Database.verifyPassword(password, user.password);
        console.log('التحقق من كلمة المرور:', { password, hashedPassword: user.password, valid: passwordValid });

        if (!passwordValid) {
            console.log('كلمة المرور غير صحيحة');
            Utils.showNotification('كلمة المرور غير صحيحة', 'error');
            return;
        }

        // إنشاء الجلسة
        const sessionData = {
            userId: user.id,
            timestamp: new Date().toISOString(),
            rememberMe: rememberMe
        };

        localStorage.setItem(this.sessionKey, JSON.stringify(sessionData));
        
        // حفظ بيانات الدخول إذا طُلب ذلك
        if (rememberMe) {
            localStorage.setItem('dentalLab_rememberedUser', username);
        } else {
            localStorage.removeItem('dentalLab_rememberedUser');
        }

        // تحديث صلاحيات المستخدم إذا لم تكن محدثة
        this.updateUserPermissions(user);

        this.currentUser = user;

        // طباعة معلومات الصلاحيات للتأكد
        console.log('🔑 معلومات المستخدم والصلاحيات:', {
            name: user.name,
            role: user.role,
            permissions: user.permissions,
            hasSettingsPermission: this.hasPermission('manage_settings')
        });

        // تسجيل النشاط
        Database.addActivity({
            type: 'user_login',
            text: `تم تسجيل دخول المستخدم: ${user.name}`,
            icon: 'sign-in-alt',
            userId: user.id
        });

        Utils.showNotification(`مرحباً ${user.name}`, 'success');
        this.showMainApp();
    }

    // تسجيل الخروج
    static logout() {
        if (this.currentUser) {
            Database.addActivity({
                type: 'user_logout',
                text: `تم تسجيل خروج المستخدم: ${this.currentUser.name}`,
                icon: 'sign-out-alt',
                userId: this.currentUser.id
            });
        }

        localStorage.removeItem(this.sessionKey);
        this.currentUser = null;
        this.showLoginScreen();
        Utils.showNotification('تم تسجيل الخروج بنجاح', 'info');
    }

    // عرض شاشة تسجيل الدخول
    static showLoginScreen() {
        document.getElementById('loginScreen').style.display = 'flex';
        document.getElementById('mainApp').style.display = 'none';
        
        // استرجاع اسم المستخدم المحفوظ
        const rememberedUser = localStorage.getItem('dentalLab_rememberedUser');
        if (rememberedUser) {
            document.getElementById('username').value = rememberedUser;
            document.getElementById('rememberMe').checked = true;
        }
    }

    // عرض التطبيق الرئيسي
    static showMainApp() {
        document.getElementById('loginScreen').style.display = 'none';
        document.getElementById('mainApp').style.display = 'block';

        // تحديث معلومات المستخدم
        const currentUserElement = document.getElementById('currentUser');
        if (currentUserElement && this.currentUser) {
            currentUserElement.textContent = `مرحباً، ${this.currentUser.name}`;
        }

        // تحميل وتطبيق إعدادات المعمل المحفوظة
        this.loadAndApplyLabSettings();

        // تحميل إضافي للإعدادات الأساسية
        this.loadBasicLabSettings();

        // التأكد من تهيئة جميع المكونات
        setTimeout(() => {
            // إعادة تهيئة الشريط الجانبي للتأكد من ظهوره
            if (typeof SidebarManager !== 'undefined') {
                SidebarManager.init();
            }

            // إعادة تهيئة نظام التنقل
            if (typeof Navigation !== 'undefined') {
                Navigation.init();
                Navigation.loadPage('dashboard');
            }

            // إعادة تهيئة الشريط السفلي
            if (typeof BottomBar !== 'undefined') {
                BottomBar.init();
            }
        }, 200);

        // تحميل لوحة التحكم
        if (typeof Navigation !== 'undefined') {
            Navigation.loadPage('dashboard');
        }
    }

    // تحميل وتطبيق إعدادات المعمل
    static loadAndApplyLabSettings() {
        try {
            console.log('🔄 تحميل إعدادات المعمل عند تسجيل الدخول...');

            // تحميل الإعدادات من localStorage
            const savedSettings = localStorage.getItem('labSettings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                console.log('📋 الإعدادات المحملة:', settings);

                // تطبيق الإعدادات على الواجهة الرئيسية
                this.applyLabSettingsToMainInterface(settings);

                console.log('✅ تم تطبيق إعدادات المعمل على الواجهة الرئيسية');
            } else {
                console.log('⚠️ لا توجد إعدادات محفوظة');
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل إعدادات المعمل:', error);
        }
    }

    // تطبيق إعدادات المعمل على الواجهة الرئيسية
    static applyLabSettingsToMainInterface(settings) {
        // تحديث اسم المعمل في الشريط العلوي
        if (settings.labName) {
            const labNameElements = document.querySelectorAll('#labName, .lab-name');
            labNameElements.forEach(element => {
                if (element) {
                    element.textContent = settings.labName;
                    console.log('✅ تم تحديث اسم المعمل في الشريط العلوي:', settings.labName);
                }
            });
        }

        // تحديث العنوان الفرعي في الشريط العلوي
        if (settings.labSubtitle) {
            const labSubtitleElements = document.querySelectorAll('#labSubtitle, .lab-subtitle');
            labSubtitleElements.forEach(element => {
                if (element) {
                    element.textContent = settings.labSubtitle;
                    console.log('✅ تم تحديث العنوان الفرعي في الشريط العلوي:', settings.labSubtitle);
                }
            });
        }

        // تحديث الشعار في الشريط العلوي
        if (settings.labLogo) {
            const labLogoElements = document.querySelectorAll('#labLogo, .lab-logo');
            labLogoElements.forEach(element => {
                if (element) {
                    element.innerHTML = `<img src="${settings.labLogo}" alt="شعار المعمل" style="width: 100%; height: 100%; object-fit: cover; border-radius: inherit;">`;
                    console.log('✅ تم تحديث الشعار في الشريط العلوي');
                }
            });
        }

        // تطبيق إعدادات أخرى إذا لزم الأمر
        if (settings.fontSize) {
            document.documentElement.style.fontSize = settings.fontSize + 'px';
        }

        // إرسال حدث تحديث بيانات المعمل
        const event = new CustomEvent('labInfoUpdated', {
            detail: { settings }
        });
        document.dispatchEvent(event);
        console.log('📡 تم إرسال حدث تحديث بيانات المعمل');

        // إعداد مراقبة تغييرات localStorage للتحديث التلقائي
        this.setupLabSettingsWatcher();
    }

    // إعداد مراقبة تغييرات إعدادات المعمل
    static setupLabSettingsWatcher() {
        // مراقبة تغييرات localStorage
        window.addEventListener('storage', (e) => {
            if (e.key === 'labSettings' && e.newValue) {
                try {
                    const newSettings = JSON.parse(e.newValue);
                    console.log('🔄 تم اكتشاف تغيير في إعدادات المعمل، تطبيق التحديثات...');
                    this.applyLabSettingsToMainInterface(newSettings);
                } catch (error) {
                    console.error('❌ خطأ في تطبيق إعدادات المعمل المحدثة:', error);
                }
            }
        });

        // مراقبة الأحداث المخصصة
        document.addEventListener('labInfoUpdated', (e) => {
            if (e.detail && e.detail.settings) {
                console.log('🔄 تم استقبال حدث تحديث بيانات المعمل، تطبيق التحديثات...');
                this.applyLabSettingsToMainInterface(e.detail.settings);
            }
        });

        console.log('👁️ تم إعداد مراقبة تغييرات إعدادات المعمل');
    }

    // تحميل الإعدادات الأساسية
    static loadBasicLabSettings() {
        try {
            console.log('📦 تحميل الإعدادات الأساسية من localStorage...');

            const savedData = localStorage.getItem('labSettings');
            if (savedData) {
                const settings = JSON.parse(savedData);
                console.log('✅ تم العثور على إعدادات محفوظة:', settings);

                // تطبيق اسم المعمل
                if (settings.labName) {
                    const labNameElements = document.querySelectorAll('#labName, .lab-name');
                    labNameElements.forEach(element => {
                        if (element) {
                            element.textContent = settings.labName;
                            console.log('✅ تم تطبيق اسم المعمل:', settings.labName);
                        }
                    });
                }

                // تطبيق العنوان الفرعي
                if (settings.labSubtitle) {
                    const labSubtitleElements = document.querySelectorAll('#labSubtitle, .lab-subtitle');
                    labSubtitleElements.forEach(element => {
                        if (element) {
                            element.textContent = settings.labSubtitle;
                            console.log('✅ تم تطبيق العنوان الفرعي:', settings.labSubtitle);
                        }
                    });
                }

                // تطبيق الشعار
                if (settings.labLogo) {
                    const labLogoElements = document.querySelectorAll('#labLogo, .lab-logo');
                    labLogoElements.forEach(element => {
                        if (element) {
                            element.innerHTML = `<img src="${settings.labLogo}" alt="شعار المعمل" style="width: 100%; height: 100%; object-fit: cover; border-radius: inherit;">`;
                            console.log('✅ تم تطبيق الشعار');
                        }
                    });
                }

            } else {
                console.log('⚠️ لا توجد إعدادات محفوظة');
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل الإعدادات الأساسية:', error);
        }
    }

    // إعادة تعيين قاعدة البيانات
    static async resetDatabase() {
        const confirmed = await Utils.confirm(
            'هل أنت متأكد من إعادة تعيين قاعدة البيانات؟ سيتم حذف جميع البيانات!',
            'تأكيد إعادة التعيين'
        );

        if (confirmed) {
            Database.reset();
            this.logout();
            Utils.showNotification('تم إعادة تعيين قاعدة البيانات بنجاح', 'success');
            
            // إعادة تحميل الصفحة
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }
    }

    // التحقق من الصلاحيات
    static hasPermission(permission) {
        if (!this.currentUser) return false;

        const rolePermissions = {
            admin: ['*'], // جميع الصلاحيات
            manager: [
                'view_dashboard',
                'manage_prosthetics',
                'manage_doctors',
                'manage_employees',
                'view_financial',
                'view_reports',
                'manage_settings'
            ],
            accountant: [
                'view_dashboard',
                'view_prosthetics',
                'manage_financial',
                'view_reports'
            ],
            employee: [
                'view_dashboard',
                'view_prosthetics',
                'add_prosthetic',
                'edit_prosthetic'
            ]
        };

        const userPermissions = rolePermissions[this.currentUser.role] || [];
        
        // إذا كان لدى المستخدم صلاحية شاملة
        if (userPermissions.includes('*')) return true;
        
        // التحقق من الصلاحية المحددة
        return userPermissions.includes(permission);
    }

    // الحصول على المستخدم الحالي
    static getCurrentUser() {
        return this.currentUser;
    }

    // التحقق من تسجيل الدخول
    static isLoggedIn() {
        return this.currentUser !== null;
    }

    // الحصول على دور المستخدم
    static getUserRole() {
        return this.currentUser ? this.currentUser.role : null;
    }

    // التحقق من دور المستخدم
    static hasRole(role) {
        return this.getUserRole() === role;
    }

    // التحقق من كونه مدير
    static isAdmin() {
        return this.hasRole('admin');
    }

    // التحقق من كونه مدير أو مدير عام
    static isManagerOrAdmin() {
        return this.hasRole('admin') || this.hasRole('manager');
    }

    // تحديث معلومات المستخدم الحالي
    static updateCurrentUser(userData) {
        if (this.currentUser) {
            const updated = Database.updateUser(this.currentUser.id, userData);
            if (updated) {
                this.currentUser = { ...this.currentUser, ...userData };
                return true;
            }
        }
        return false;
    }

    // تغيير كلمة المرور
    static async changePassword(currentPassword, newPassword) {
        if (!this.currentUser) return false;

        // التحقق من كلمة المرور الحالية
        if (!Database.verifyPassword(currentPassword, this.currentUser.password)) {
            Utils.showNotification('كلمة المرور الحالية غير صحيحة', 'error');
            return false;
        }

        // تحديث كلمة المرور
        const hashedPassword = Database.hashPassword(newPassword);
        const updated = this.updateCurrentUser({ password: hashedPassword });
        
        if (updated) {
            Utils.showNotification('تم تغيير كلمة المرور بنجاح', 'success');
            
            Database.addActivity({
                type: 'password_changed',
                text: 'تم تغيير كلمة المرور',
                icon: 'key',
                userId: this.currentUser.id
            });
            
            return true;
        }

        Utils.showNotification('فشل في تغيير كلمة المرور', 'error');
        return false;
    }

    // إنشاء مستخدم جديد (للمديرين فقط)
    static async createUser(userData) {
        if (!this.hasPermission('manage_users')) {
            Utils.showNotification('ليس لديك صلاحية لإنشاء مستخدمين', 'error');
            return false;
        }

        // التحقق من عدم وجود اسم المستخدم
        const existingUser = Database.getUserByUsername(userData.username);
        if (existingUser) {
            Utils.showNotification('اسم المستخدم موجود بالفعل', 'error');
            return false;
        }

        // تشفير كلمة المرور
        userData.password = Database.hashPassword(userData.password);
        userData.isActive = true;

        const created = Database.addUser(userData);
        if (created) {
            Database.addActivity({
                type: 'user_created',
                text: `تم إنشاء مستخدم جديد: ${userData.name}`,
                icon: 'user-plus',
                userId: this.currentUser.id
            });
            
            Utils.showNotification('تم إنشاء المستخدم بنجاح', 'success');
            return true;
        }

        Utils.showNotification('فشل في إنشاء المستخدم', 'error');
        return false;
    }

    // تحديث حالة المستخدم (تفعيل/إلغاء تفعيل)
    static toggleUserStatus(userId) {
        if (!this.hasPermission('manage_users')) {
            Utils.showNotification('ليس لديك صلاحية لتعديل المستخدمين', 'error');
            return false;
        }

        const user = Database.getUserById(userId);
        if (!user) return false;

        const newStatus = !user.isActive;
        const updated = Database.updateUser(userId, { isActive: newStatus });
        
        if (updated) {
            const statusText = newStatus ? 'تفعيل' : 'إلغاء تفعيل';
            Utils.showNotification(`تم ${statusText} المستخدم بنجاح`, 'success');
            
            Database.addActivity({
                type: 'user_status_changed',
                text: `تم ${statusText} المستخدم: ${user.name}`,
                icon: newStatus ? 'user-check' : 'user-times',
                userId: this.currentUser.id
            });
            
            return true;
        }

        return false;
    }

    // الحصول على قائمة المستخدمين (للمديرين فقط)
    static getUsers() {
        if (!this.hasPermission('manage_users')) {
            return [];
        }
        return Database.getUsers();
    }

    // تحديث صلاحيات المستخدم
    static updateUserPermissions(user) {
        try {
            // إذا كان المستخدم admin وليس لديه صلاحيات محددة، أعطه صلاحيات كاملة
            if (user.role === 'admin' && (!user.permissions || user.permissions.length === 0)) {
                user.permissions = ['*'];
                console.log('🔧 تم تحديث صلاحيات المدير إلى صلاحيات كاملة');

                // تحديث المستخدم في قاعدة البيانات
                Database.updateUser(user.id, { permissions: user.permissions });
            }
        } catch (error) {
            console.error('❌ خطأ في تحديث صلاحيات المستخدم:', error);
        }
    }

    // الحصول على صلاحيات المستخدم الحالي
    static getUserPermissions() {
        if (!this.currentUser) return [];

        const rolePermissions = {
            admin: ['*'], // جميع الصلاحيات
            manager: [
                'view_dashboard',
                'manage_prosthetics',
                'manage_doctors',
                'manage_employees',
                'view_financial',
                'view_reports',
                'manage_settings',
                'manage_users',
                'manage_backup'
            ],
            accountant: [
                'view_dashboard',
                'view_prosthetics',
                'manage_financial',
                'view_reports'
            ],
            employee: [
                'view_dashboard',
                'view_prosthetics',
                'add_prosthetic',
                'edit_prosthetic'
            ]
        };

        // إذا كان للمستخدم صلاحيات مخصصة، استخدمها
        if (this.currentUser.permissions && this.currentUser.permissions.length > 0) {
            return this.currentUser.permissions;
        }

        // وإلا استخدم صلاحيات الدور
        return rolePermissions[this.currentUser.role] || [];
    }

    // التحقق من صلاحية محددة مع تفاصيل أكثر
    static checkPermissionDetailed(permission) {
        const hasPermission = this.hasPermission(permission);
        const userPermissions = this.getUserPermissions();

        console.log(`🔍 فحص الصلاحية "${permission}":`, {
            hasPermission,
            userRole: this.currentUser?.role,
            userPermissions,
            requestedPermission: permission
        });

        return hasPermission;
    }

    // إعادة تعيين صلاحيات جميع المستخدمين (للطوارئ)
    static resetAllUserPermissions() {
        try {
            const users = Database.getUsers();
            let updatedCount = 0;

            users.forEach(user => {
                if (user.role === 'admin') {
                    user.permissions = ['*'];
                    Database.updateUser(user.id, { permissions: user.permissions });
                    updatedCount++;
                }
            });

            console.log(`✅ تم تحديث صلاحيات ${updatedCount} مدير`);
            Utils.showNotification(`تم تحديث صلاحيات ${updatedCount} مدير`, 'success');

        } catch (error) {
            console.error('❌ خطأ في إعادة تعيين الصلاحيات:', error);
            Utils.showNotification('حدث خطأ في إعادة تعيين الصلاحيات', 'error');
        }
    }
}
