/* أنماط إدارة التركيبات - تصميم حديث */

.prosthetics-container {
    padding: 1.5rem;
    max-width: 1600px;
    margin: 0 auto;
    background: #f8fafc;
    min-height: 100vh;
}

/* أنماط خاصة بإدارة التركيبات فقط */



/* شريط الأدوات */
.toolbar {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.toolbar-section h2 {
    margin: 0 0 1.5rem 0;
    color: #1f2937;
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #1f2937 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.toolbar-buttons {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.tool-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem;
    border-radius: 16px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 140px;
    position: relative;
    overflow: hidden;
    font-weight: 600;
}

.tool-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.tool-btn:hover::before {
    left: 100%;
}

.tool-btn i {
    font-size: 2rem;
    transition: transform 0.3s ease;
}

.tool-btn:hover i {
    transform: scale(1.2) rotate(5deg);
}

.tool-btn span {
    font-size: 0.9rem;
    text-align: center;
    line-height: 1.2;
}

.tool-btn:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

/* ألوان الأزرار */
.tool-btn.primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    color: white;
    border-color: #2563eb;
}

.tool-btn.primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    box-shadow: 0 15px 35px rgba(59, 130, 246, 0.4);
}

.tool-btn.secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
    border-color: #5b6470;
}

.tool-btn.secondary:hover {
    background: linear-gradient(135deg, #5b6470 0%, #374151 100%);
    box-shadow: 0 15px 35px rgba(107, 114, 128, 0.4);
}

.tool-btn.info {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    color: white;
    border-color: #0284c7;
}

.tool-btn.info:hover {
    background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
    box-shadow: 0 15px 35px rgba(6, 182, 212, 0.4);
}

.tool-btn.warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border-color: #e08e0b;
}

.tool-btn.warning:hover {
    background: linear-gradient(135deg, #e08e0b 0%, #c2710c 100%);
    box-shadow: 0 15px 35px rgba(245, 158, 11, 0.4);
}

.tool-btn.success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border-color: #0d9668;
}

.tool-btn.success:hover {
    background: linear-gradient(135deg, #0d9668 0%, #047857 100%);
    box-shadow: 0 15px 35px rgba(16, 185, 129, 0.4);
}



/* حاوية قائمة التركيبات - تصميم جديد */
.prosthetics-list-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 24px;
    padding: 2.5rem;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.prosthetics-list-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2.5rem;
    flex-wrap: wrap;
    gap: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 2px solid rgba(59, 130, 246, 0.1);
}

.list-header h2 {
    margin: 0;
    color: #1f2937;
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(135deg, #1f2937 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.list-filters {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.list-filters select,
.list-filters input {
    padding: 1rem 1.5rem;
    border: 2px solid #e5e7eb;
    border-radius: 15px;
    font-size: 1rem;
    font-weight: 500;
    background: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.list-filters select:focus,
.list-filters input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.list-filters input::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

/* جدول التركيبات - تصميم جديد */
.prosthetics-table {
    overflow-x: auto;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
}

.prosthetics-table table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: white;
    border-radius: 16px;
    overflow: hidden;
}

.prosthetics-table th,
.prosthetics-table td {
    padding: 1.5rem;
    text-align: right;
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
}

.prosthetics-table th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    font-weight: 700;
    color: #374151;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
}

.prosthetics-table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #06b6d4);
}

.prosthetics-table tr {
    transition: all 0.3s ease;
}

.prosthetics-table tbody tr:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
    transform: scale(1.01);
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
}

/* شارات الحالة - تصميم جديد */
.status-badge {
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.status-badge:hover::before {
    left: 100%;
}

.status-pending {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
    border: 2px solid #f59e0b;
}

.status-progress {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1e40af;
    border: 2px solid #3b82f6;
}

.status-completed {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    color: #065f46;
    border: 2px solid #10b981;
}

/* أزرار الإجراءات - تصميم جديد */
.action-buttons {
    display: flex;
    gap: 0.75rem;
}

.btn-icon {
    width: 42px;
    height: 42px;
    border-radius: 12px;
    border: 2px solid transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    font-size: 1rem;
}

.btn-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.2), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.btn-icon:hover::before {
    opacity: 1;
}

.btn-edit {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    color: #059669;
    border-color: #10b981;
}

.btn-edit:hover {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

.btn-delete {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    color: #dc2626;
    border-color: #ef4444;
}

.btn-delete:hover {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);
}

.btn-invoice {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
    border-color: #f59e0b;
}

.btn-invoice:hover {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
}

/* حالة عدم وجود بيانات - تصميم جديد */
.no-data {
    text-align: center;
    padding: 4rem;
    color: #6b7280;
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    border-radius: 16px;
    margin: 2rem 0;
}

.no-data i {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    opacity: 0.7;
}

.no-data p {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #4b5563;
}

/* التجاوب - محسن */
@media (max-width: 1024px) {
    .header-main-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .header-stats-section {
        flex-wrap: wrap;
        gap: 1rem;
        justify-content: center;
    }

    .stat-box {
        min-width: 120px;
        padding: 1.25rem;
    }

    .stat-value {
        font-size: 1.8rem;
    }

    .department-title {
        font-size: 2rem;
    }

    .toolbar-buttons {
        gap: 1rem;
    }

    .tool-btn {
        min-width: 120px;
        padding: 1.25rem;
    }

    .tool-btn i {
        font-size: 1.8rem;
    }
}

@media (max-width: 768px) {
    .prosthetics-container {
        padding: 1rem;
        background: #f8fafc;
    }

    .page-header {
        padding: 2rem;
        border-radius: 16px;
    }

    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .header-title h1 {
        font-size: 1.8rem;
    }

    .department-title {
        font-size: 1.8rem;
    }

    .department-description {
        font-size: 1rem;
    }

    .header-stats-section {
        flex-direction: column;
        gap: 1rem;
        margin-top: 1.5rem;
        padding-top: 1.5rem;
    }

    .stat-box {
        min-width: auto;
        flex: none;
        padding: 1rem;
        width: 100%;
        max-width: 200px;
        margin: 0 auto;
    }

    .stat-value {
        font-size: 1.6rem;
    }

    .stat-name {
        font-size: 0.75rem;
    }

    .download-btn {
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }

    .toolbar {
        padding: 1.5rem;
    }

    .toolbar-buttons {
        gap: 1rem;
        justify-content: center;
    }

    .tool-btn {
        min-width: 100px;
        padding: 1rem;
    }

    .tool-btn i {
        font-size: 1.5rem;
    }

    .tool-btn span {
        font-size: 0.8rem;
    }

    .list-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1.5rem;
    }

    .list-filters {
        justify-content: center;
        gap: 1rem;
    }

    .list-filters select,
    .list-filters input {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .prosthetics-table th,
    .prosthetics-table td {
        padding: 1rem 0.75rem;
        font-size: 0.9rem;
    }

    .action-buttons {
        gap: 0.5rem;
    }

    .btn-icon {
        width: 38px;
        height: 38px;
    }
}

@media (max-width: 480px) {
    .prosthetics-container {
        padding: 0.75rem;
    }

    .page-header {
        padding: 1.5rem;
    }



    .prosthetics-list-container {
        padding: 1.5rem;
    }

    .prosthetics-table {
        font-size: 0.8rem;
    }

    .prosthetics-table th,
    .prosthetics-table td {
        padding: 0.75rem 0.5rem;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        font-size: 0.7rem;
    }
}
