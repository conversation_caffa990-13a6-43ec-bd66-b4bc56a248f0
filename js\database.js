console.log('Loaded: database.js');
// نظام قاعدة البيانات المحلية
class Database {
    static init() {
        console.log('تهيئة قاعدة البيانات...');
        // إنشاء البيانات الافتراضية إذا لم تكن موجودة
        const isInitialized = localStorage.getItem('dentalLab_initialized');
        console.log('حالة التهيئة:', isInitialized);

        if (!isInitialized) {
            console.log('إنشاء البيانات الافتراضية...');
            this.createDefaultData();
            localStorage.setItem('dentalLab_initialized', 'true');
            console.log('تم إنشاء البيانات الافتراضية');
        } else {
            console.log('البيانات موجودة بالفعل');
        }

        // التحقق من المستخدمين وتحديث صلاحياتهم
        const users = this.getUsers();
        console.log('المستخدمون الموجودون:', users);

        // التحقق من صلاحيات المستخدم الجديد
        this.ensureUserPermissions();
    }

    // إنشاء البيانات الافتراضية
    static createDefaultData() {
        // المستخدمون الافتراضيون
        const defaultUsers = [
            {
                id: 'dentallab_admin',
                username: 'dentallab',
                password: this.hashPassword('dental2024'),
                name: 'مدير معمل الأسنان',
                role: 'admin',
                email: '<EMAIL>',
                phone: '***********',
                createdAt: new Date().toISOString(),
                isActive: true,
                permissions: ['*'], // صلاحيات كاملة
                lastLogin: null,
                loginCount: 0
            }
        ];

        // إعدادات المعمل الافتراضية
        const defaultSettings = {
            labName: 'معمل الأسنان المتخصص',
            address: 'العنوان الرئيسي للمعمل',
            phones: ['***********', '03-5750974', '***********'],
            email: '<EMAIL>',
            website: 'www.dentallab.com',
            logo: '',
            currency: 'جنيه',
            taxRate: 0,
            language: 'ar',
            theme: 'default',
            autoBackup: true,
            backupInterval: 7, // أيام
            notifications: {
                email: true,
                sms: false,
                push: true
            }
        };

        // حفظ البيانات
        this.setItem('users', defaultUsers);
        this.setItem('settings', defaultSettings);
        this.setItem('prosthetics', []);
        this.setItem('doctors', []);
        this.setItem('employees', []);
        this.setItem('externalLabs', []);
        this.setItem('accounts', []);
        this.setItem('expenses', []);
        this.setItem('payments', []);
        this.setItem('activities', []);
    }

    // تشفير كلمة المرور (تشفير بسيط)
    static hashPassword(password) {
        return btoa(password + 'dentallab_salt');
    }

    // التحقق من كلمة المرور
    static verifyPassword(password, hash) {
        return this.hashPassword(password) === hash;
    }

    // حفظ عنصر في localStorage
    static setItem(key, value) {
        try {
            localStorage.setItem(`dentalLab_${key}`, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            return false;
        }
    }

    // استرجاع عنصر من localStorage
    static getItem(key) {
        try {
            const item = localStorage.getItem(`dentalLab_${key}`);
            return item ? JSON.parse(item) : null;
        } catch (error) {
            console.error('خطأ في استرجاع البيانات:', error);
            return null;
        }
    }

    // حذف عنصر من localStorage
    static removeItem(key) {
        localStorage.removeItem(`dentalLab_${key}`);
    }

    // إعادة تعيين قاعدة البيانات
    static reset() {
        const keys = Object.keys(localStorage).filter(key => key.startsWith('dentalLab_'));
        keys.forEach(key => localStorage.removeItem(key));
        this.createDefaultData();
        localStorage.setItem('dentalLab_initialized', 'true');
    }

    // تصدير جميع البيانات
    static exportAll() {
        const data = {};
        const keys = Object.keys(localStorage).filter(key => key.startsWith('dentalLab_'));
        
        keys.forEach(key => {
            const cleanKey = key.replace('dentalLab_', '');
            data[cleanKey] = this.getItem(cleanKey);
        });

        return data;
    }

    // استيراد البيانات
    static importAll(data) {
        try {
            Object.keys(data).forEach(key => {
                this.setItem(key, data[key]);
            });
            return true;
        } catch (error) {
            console.error('خطأ في استيراد البيانات:', error);
            return false;
        }
    }

    // === وظائف المستخدمين ===
    static getUsers() {
        return this.getItem('users') || [];
    }

    static getUserById(id) {
        const users = this.getUsers();
        return users.find(user => user.id === id);
    }

    static getUserByUsername(username) {
        const users = this.getUsers();
        console.log('البحث عن المستخدم:', username, 'في قائمة المستخدمين:', users);
        return users.find(user => user.username === username);
    }

    static addUser(user) {
        const users = this.getUsers();
        user.id = user.id || Utils.generateId();
        user.createdAt = new Date().toISOString();
        users.push(user);
        return this.setItem('users', users);
    }

    static updateUser(id, userData) {
        const users = this.getUsers();
        const index = users.findIndex(user => user.id === id);
        if (index !== -1) {
            users[index] = { ...users[index], ...userData, updatedAt: new Date().toISOString() };
            return this.setItem('users', users);
        }
        return false;
    }

    static deleteUser(id) {
        const users = this.getUsers();
        const filteredUsers = users.filter(user => user.id !== id);
        return this.setItem('users', filteredUsers);
    }



    // === وظائف التركيبات ===
    static getProsthetics() {
        return this.getItem('prosthetics') || [];
    }

    static getProstheticById(id) {
        const prosthetics = this.getProsthetics();
        return prosthetics.find(p => p.id === id);
    }

    static addProsthetic(prosthetic) {
        const prosthetics = this.getProsthetics();
        prosthetic.id = prosthetic.id || Utils.generateId();
        prosthetic.caseNumber = prosthetic.caseNumber || Utils.generateCaseNumber();
        prosthetic.createdAt = new Date().toISOString();
        prosthetic.status = prosthetic.status || 'pending';
        prosthetics.push(prosthetic);

        // إضافة نشاط
        this.addActivity({
            type: 'prosthetic_added',
            text: `تم إضافة تركيبة جديدة - رقم الحالة: ${prosthetic.caseNumber}`,
            icon: 'plus',
            userId: prosthetic.createdBy
        });

        return this.setItem('prosthetics', prosthetics);
    }

    static updateProsthetic(id, prostheticData) {
        const prosthetics = this.getProsthetics();
        const index = prosthetics.findIndex(p => p.id === id);
        if (index !== -1) {
            const oldStatus = prosthetics[index].status;
            prosthetics[index] = { ...prosthetics[index], ...prostheticData, updatedAt: new Date().toISOString() };

            // إضافة نشاط إذا تغيرت الحالة
            if (oldStatus !== prostheticData.status) {
                this.addActivity({
                    type: 'prosthetic_status_changed',
                    text: `تم تحديث حالة التركيبة ${prosthetics[index].caseNumber} إلى ${this.getStatusText(prostheticData.status)}`,
                    icon: 'edit',
                    userId: prostheticData.updatedBy
                });
            }

            return this.setItem('prosthetics', prosthetics);
        }
        return false;
    }

    static deleteProsthetic(id) {
        const prosthetics = this.getProsthetics();
        const prosthetic = prosthetics.find(p => p.id === id);
        if (prosthetic) {
            this.addActivity({
                type: 'prosthetic_deleted',
                text: `تم حذف التركيبة ${prosthetic.caseNumber}`,
                icon: 'trash',
                userId: 'system'
            });
        }
        const filteredProsthetics = prosthetics.filter(p => p.id !== id);
        return this.setItem('prosthetics', filteredProsthetics);
    }

    // === وظائف الأطباء ===
    static getDoctors() {
        return this.getItem('doctors') || [];
    }

    static getDoctorById(id) {
        const doctors = this.getDoctors();
        return doctors.find(d => d.id === id);
    }

    static addDoctor(doctor) {
        const doctors = this.getDoctors();
        doctor.id = doctor.id || Utils.generateId();
        doctor.createdAt = new Date().toISOString();
        doctors.push(doctor);
        
        this.addActivity({
            type: 'doctor_added',
            text: `تم إضافة طبيب جديد: ${doctor.name}`,
            icon: 'user-md',
            userId: 'system'
        });
        
        return this.setItem('doctors', doctors);
    }

    static updateDoctor(id, doctorData) {
        const doctors = this.getDoctors();
        const index = doctors.findIndex(d => d.id === id);
        if (index !== -1) {
            doctors[index] = { ...doctors[index], ...doctorData, updatedAt: new Date().toISOString() };
            return this.setItem('doctors', doctors);
        }
        return false;
    }

    static deleteDoctor(id) {
        const doctors = this.getDoctors();
        const doctor = doctors.find(d => d.id === id);
        

        
        if (doctor) {
            this.addActivity({
                type: 'doctor_deleted',
                text: `تم حذف الطبيب: ${doctor.name}`,
                icon: 'trash',
                userId: 'system'
            });
        }
        
        const filteredDoctors = doctors.filter(d => d.id !== id);
        this.setItem('doctors', filteredDoctors);
        return { success: true };
    }

    // === وظائف الموظفين ===
    static getEmployees() {
        return this.getItem('employees') || [];
    }

    static getEmployeeById(id) {
        const employees = this.getEmployees();
        return employees.find(e => e.id === id);
    }

    static addEmployee(employee) {
        const employees = this.getEmployees();
        employee.id = employee.id || Utils.generateId();
        employee.createdAt = new Date().toISOString();
        employees.push(employee);
        
        this.addActivity({
            type: 'employee_added',
            text: `تم إضافة موظف جديد: ${employee.name}`,
            icon: 'users',
            userId: 'system'
        });
        
        return this.setItem('employees', employees);
    }

    static updateEmployee(id, employeeData) {
        const employees = this.getEmployees();
        const index = employees.findIndex(e => e.id === id);
        if (index !== -1) {
            employees[index] = { ...employees[index], ...employeeData, updatedAt: new Date().toISOString() };
            return this.setItem('employees', employees);
        }
        return false;
    }

    static deleteEmployee(id) {
        const employees = this.getEmployees();
        const employee = employees.find(e => e.id === id);
        
        if (employee) {
            this.addActivity({
                type: 'employee_deleted',
                text: `تم حذف الموظف: ${employee.name}`,
                icon: 'trash',
                userId: 'system'
            });
        }
        
        const filteredEmployees = employees.filter(e => e.id !== id);
        return this.setItem('employees', filteredEmployees);
    }

    // === وظائف الأنشطة ===
    static getActivities() {
        return this.getItem('activities') || [];
    }

    static addActivity(activity) {
        const activities = this.getActivities();

        // التحقق من وجود Utils
        if (typeof Utils === 'undefined' || typeof Utils.generateId !== 'function') {
            console.error('❌ Utils.generateId غير متاح');
            activity.id = activity.id || 'id_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        } else {
            activity.id = activity.id || Utils.generateId();
        }

        activity.time = new Date().toISOString();
        activities.unshift(activity); // إضافة في المقدمة
        
        // الاحتفاظ بآخر 100 نشاط فقط
        if (activities.length > 100) {
            activities.splice(100);
        }
        
        return this.setItem('activities', activities);
    }

    // === وظائف الإعدادات ===
    static getSettings() {
        return this.getItem('settings') || {};
    }

    static updateSettings(settings) {
        const currentSettings = this.getSettings();
        const newSettings = { ...currentSettings, ...settings };
        return this.setItem('settings', newSettings);
    }



    // === وظائف مساعدة ===
    static getStatusText(status) {
        const statusTexts = {
            pending: 'قيد الانتظار',
            progress: 'قيد التنفيذ',
            completed: 'مكتملة'
        };
        return statusTexts[status] || status;
    }
    static getStatusText(status) {
        const statusTexts = {
            pending: 'في الانتظار',
            progress: 'قيد التنفيذ',
            completed: 'مكتملة',
            delivered: 'تم التسليم'
        };
        return statusTexts[status] || status;
    }

    // إحصائيات سريعة
    static getQuickStats() {
        const prosthetics = this.getProsthetics();
        const doctors = this.getDoctors();
        const employees = this.getEmployees();

        return {
            totalProsthetics: prosthetics.length,
            pendingProsthetics: prosthetics.filter(p => p.status === 'pending' || p.status === 'progress').length,
            completedProsthetics: prosthetics.filter(p => p.status === 'completed').length,
            totalDoctors: doctors.length,
            totalEmployees: employees.length,
            nextCaseNumber: Utils.generateCaseNumber()
        };
    }

    // التأكد من صلاحيات المستخدمين
    static ensureUserPermissions() {
        try {
            const users = this.getUsers();
            let updated = false;

            users.forEach(user => {
                // إذا كان المستخدم admin وليس لديه صلاحيات، أعطه صلاحيات كاملة
                if (user.role === 'admin' && (!user.permissions || user.permissions.length === 0)) {
                    user.permissions = ['*'];
                    this.updateUser(user.id, { permissions: user.permissions });
                    updated = true;
                    console.log(`✅ تم تحديث صلاحيات المستخدم: ${user.name}`);
                }

                // التأكد من وجود المستخدم الجديد dentallab
                if (user.username === 'dentallab') {
                    if (!user.permissions || !user.permissions.includes('*')) {
                        user.permissions = ['*'];
                        this.updateUser(user.id, { permissions: user.permissions });
                        updated = true;
                        console.log(`✅ تم تحديث صلاحيات المستخدم dentallab`);
                    }
                }
            });

            if (updated) {
                console.log('🔑 تم تحديث صلاحيات المستخدمين بنجاح');
            }

        } catch (error) {
            console.error('❌ خطأ في تحديث صلاحيات المستخدمين:', error);
        }
    }
}
