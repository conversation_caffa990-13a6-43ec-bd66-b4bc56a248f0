// الشريط السفلي - الوقت والتاريخ واسم القسم
class BottomBar {
    static currentSection = 'لوحة التحكم';
    static timeInterval = null;
    static isInitialized = false;

    // تهيئة الشريط السفلي
    static init() {
        if (this.isInitialized) return;
        
        this.updateDateTime();
        this.startTimeUpdates();
        this.setupEventListeners();
        this.isInitialized = true;
    }

    // بدء تحديث الوقت
    static startTimeUpdates() {
        // تحديث فوري
        this.updateDateTime();
        
        // تحديث كل ثانية
        this.timeInterval = setInterval(() => {
            this.updateDateTime();
        }, 1000);
    }

    // إيقاف تحديث الوقت
    static stopTimeUpdates() {
        if (this.timeInterval) {
            clearInterval(this.timeInterval);
            this.timeInterval = null;
        }
    }

    // تحديث الوقت والتاريخ
    static updateDateTime() {
        const now = new Date();
        
        // تحديث الوقت
        this.updateTime(now);
        
        // تحديث التاريخ الميلادي
        this.updateGregorianDate(now);
        
        // تحديث التاريخ الهجري
        this.updateHijriDate(now);
    }

    // تحديث الوقت
    static updateTime(date) {
        const timeElement = document.getElementById('currentTime');
        if (!timeElement) return;

        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        const timeString = `${hours}:${minutes}:${seconds}`;
        
        // تأثير النبضة عند تحديث الثواني
        if (timeElement.textContent !== timeString) {
            timeElement.parentElement.classList.add('updating');
            setTimeout(() => {
                timeElement.parentElement.classList.remove('updating');
            }, 300);
        }
        
        timeElement.textContent = timeString;
    }

    // تحديث التاريخ الميلادي
    static updateGregorianDate(date) {
        const dateElement = document.getElementById('gregorianDate');
        if (!dateElement) return;

        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        
        const dateString = `${day}/${month}/${year}`;
        dateElement.textContent = dateString;
    }

    // تحديث التاريخ الهجري
    static updateHijriDate(date) {
        const dateElement = document.getElementById('hijriDate');
        if (!dateElement) return;

        try {
            const hijriDate = this.convertToHijri(date);
            dateElement.textContent = hijriDate;
        } catch (error) {
            console.warn('خطأ في تحويل التاريخ الهجري:', error);
            dateElement.textContent = 'التاريخ الهجري';
        }
    }

    // تحويل التاريخ الميلادي إلى هجري (تقريبي)
    static convertToHijri(gregorianDate) {
        // خوارزمية تقريبية لتحويل التاريخ الهجري
        // هذه خوارزمية مبسطة وقد تحتاج لمكتبة متخصصة للدقة الكاملة
        
        const gYear = gregorianDate.getFullYear();
        const gMonth = gregorianDate.getMonth() + 1;
        const gDay = gregorianDate.getDate();
        
        // تحويل تقريبي (يمكن تحسينه باستخدام مكتبة متخصصة)
        const hijriYear = Math.floor((gYear - 622) * 1.030684);
        const hijriMonth = Math.floor((gMonth + (gDay / 30)) * 1.030684) % 12 + 1;
        const hijriDay = Math.floor(gDay * 1.030684) % 30 + 1;
        
        // أسماء الشهور الهجرية
        const hijriMonths = [
            'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني',
            'جمادى الأولى', 'جمادى الثانية', 'رجب', 'شعبان',
            'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
        ];
        
        const monthName = hijriMonths[hijriMonth - 1] || hijriMonths[0];
        
        return `${String(hijriDay).padStart(2, '0')} ${monthName} ${hijriYear + 1400}هـ`;
    }

    // تحديث اسم القسم الحالي
    static updateCurrentSection(sectionName) {
        this.currentSection = sectionName;
        const sectionElement = document.getElementById('currentSectionName');
        if (sectionElement) {
            // تأثير انتقال سلس
            sectionElement.style.opacity = '0';
            setTimeout(() => {
                sectionElement.textContent = sectionName;
                sectionElement.style.opacity = '1';
            }, 150);
        }
    }

    // إعداد مستمعي الأحداث
    static setupEventListeners() {
        // مراقبة تغيير الصفحات
        document.addEventListener('pageChanged', (event) => {
            if (event.detail && event.detail.pageName) {
                this.updateCurrentSection(event.detail.pageName);
            }
        });

        // مراقبة النقر على روابط التنقل
        document.addEventListener('click', (event) => {
            const navLink = event.target.closest('.nav-link');
            if (navLink) {
                const pageName = this.getPageDisplayName(navLink.getAttribute('data-page'));
                this.updateCurrentSection(pageName);
            }
        });

        // إيقاف التحديث عند إخفاء الصفحة
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopTimeUpdates();
            } else {
                this.startTimeUpdates();
            }
        });

        // إعادة تشغيل التحديث عند العودة للصفحة
        window.addEventListener('focus', () => {
            this.startTimeUpdates();
        });

        // إيقاف التحديث عند مغادرة الصفحة
        window.addEventListener('blur', () => {
            this.stopTimeUpdates();
        });
    }

    // الحصول على اسم الصفحة للعرض
    static getPageDisplayName(pageName) {
        const pageNames = {
            'dashboard': 'لوحة التحكم',
            'prosthetics': 'التركيبات السنية',
            'doctors': 'إدارة الأطباء',
            'employees': 'إدارة الموظفين',
            'external-labs': 'المعامل الخارجية',
            'financial': 'النظام المالي',
            'reports': 'التقارير',
            'backup': 'النسخ الاحتياطي',
            'settings': 'الإعدادات'
        };
        
        return pageNames[pageName] || pageName || 'لوحة التحكم';
    }

    // تنظيف الموارد
    static cleanup() {
        this.stopTimeUpdates();
        this.isInitialized = false;
    }

    // إعادة تهيئة الشريط
    static reinitialize() {
        this.cleanup();
        setTimeout(() => {
            this.init();
        }, 100);
    }

    // تحديث يدوي للوقت (للاختبار)
    static forceUpdate() {
        this.updateDateTime();
    }

    // الحصول على معلومات الوقت الحالي
    static getCurrentTimeInfo() {
        const now = new Date();
        return {
            time: now.toLocaleTimeString('ar-SA'),
            gregorianDate: now.toLocaleDateString('ar-SA'),
            hijriDate: this.convertToHijri(now),
            currentSection: this.currentSection
        };
    }

    // تبديل تنسيق الوقت (12/24 ساعة)
    static toggleTimeFormat() {
        // يمكن إضافة هذه الميزة لاحقاً
        console.log('تبديل تنسيق الوقت - ميزة قادمة');
    }

    // إظهار/إخفاء الشريط السفلي
    static toggleVisibility() {
        const bottomBar = document.querySelector('.bottom-bar');
        if (bottomBar) {
            bottomBar.style.display = bottomBar.style.display === 'none' ? 'block' : 'none';
        }
    }
}

// تهيئة تلقائية عند تحميل الصفحة (تم نقلها إلى App.init)
// document.addEventListener('DOMContentLoaded', () => {
//     BottomBar.init();
// });

// تنظيف عند إغلاق الصفحة
window.addEventListener('beforeunload', () => {
    BottomBar.cleanup();
});
