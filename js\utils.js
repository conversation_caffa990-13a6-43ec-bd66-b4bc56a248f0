console.log('Loaded: utils.js');

class Utils {
    static formatDate(date, format = 'YYYY-MM-DD') {
        if (!date) return '';
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        switch (format) {
            case 'YYYY-MM-DD':
                return `${year}-${month}-${day}`;
            case 'DD/MM/YYYY':
                return `${day}/${month}/${year}`;
            case 'DD/MM/YYYY HH:mm':
                return `${day}/${month}/${year} ${hours}:${minutes}`;
            case 'YYYY-MM-DD HH:mm':
                return `${year}-${month}-${day} ${hours}:${minutes}`;
            default:
                return d.toLocaleDateString('ar-EG');
        }
    }

    // تنسيق التاريخ والوقت
    static formatDateTime(date, format = 'DD/MM/YYYY HH:mm') {
        if (!date) return '';
        return this.formatDate(date, format);
    }

    static formatNumber(number, decimals = 0) {
        if (isNaN(number)) return '0';
        return Number(number).toLocaleString('ar-EG', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    }

    static formatCurrency(amount, currency = 'جنيه') {
        if (isNaN(amount)) return `0 ${currency}`;
        return `${Number(amount).toLocaleString('ar-EG', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} ${currency}`;
    }

    static showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notifications');
        if (!container) return;
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        container.appendChild(notification);
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, duration);
    }

    static getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    static generateCaseNumber() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        return `${year}${month}-001`;
    }

    static updateRecentActivities(activities) {
        const container = document.getElementById('recentActivities');
        if (!container) return;
        const activitiesHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="fas fa-${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-text">${activity.text}</div>
                    <div class="activity-time">${this.formatDate(activity.time, 'DD/MM/YYYY HH:mm')}</div>
                </div>
            </div>
        `).join('');
        container.innerHTML = activitiesHTML || '<div class="no-activities">لا توجد نشاطات حديثة</div>';
    }

    static createModal({ title = '', content = '', size = 'medium' }) {
        console.log('=== Utils.createModal CALLED ===', { title, content, size });
        let overlay = document.getElementById('modalOverlay');
        let modalContent = document.getElementById('modalContent');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'modalOverlay';
            overlay.className = 'modal-overlay';
            document.body.appendChild(overlay);
        }
        if (!modalContent) {
            modalContent = document.createElement('div');
            modalContent.id = 'modalContent';
            modalContent.className = 'modal-content';
            overlay.appendChild(modalContent);
        }
        // إضافة زر الإغلاق
        const closeButton = `
            <button onclick="Utils.closeModal()" style="
                position: absolute;
                top: 10px;
                right: 10px;
                background: #ff4757;
                color: white;
                border: none;
                border-radius: 50%;
                width: 30px;
                height: 30px;
                cursor: pointer;
                font-size: 16px;
                z-index: 100001;
            ">×</button>
        `;

        modalContent.innerHTML = closeButton + content;

        // تطبيق CSS مباشرة للتأكد من الظهور
        overlay.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background: rgba(0,0,0,0.5) !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            z-index: 99999 !important;
            padding: 20px !important;
        `;

        modalContent.style.cssText = `
            background: white !important;
            border-radius: 10px !important;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25) !important;
            max-width: 95vw !important;
            max-height: 95vh !important;
            overflow: auto !important;
            z-index: 100000 !important;
            padding: 20px !important;
        `;

        console.log('=== Modal overlay display set to flex ===');
        overlay.onclick = (e) => {
            if (e.target === overlay) Utils.closeModal();
        };
    }

    static closeModal() {
        const overlay = document.getElementById('modalOverlay');
        if (overlay) overlay.style.display = 'none';
    }

    /**
     * إنشاء معرف فريد
     */
    static generateId() {
        return 'id_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * عرض نافذة تأكيد
     */
    static confirm(message, title = 'تأكيد') {
        return new Promise((resolve) => {
            const confirmed = window.confirm(`${title}\n\n${message}`);
            resolve(confirmed);
        });
    }

    /**
     * عرض نافذة تنبيه
     */
    static alert(message, title = 'تنبيه') {
        return new Promise((resolve) => {
            window.alert(`${title}\n\n${message}`);
            resolve();
        });
    }

    /**
     * عرض نافذة إدخال
     */
    static prompt(message, defaultValue = '', title = 'إدخال') {
        return new Promise((resolve) => {
            const result = window.prompt(`${title}\n\n${message}`, defaultValue);
            resolve(result);
        });
    }

    /**
     * تحويل النص إلى عنوان URL صالح
     */
    static slugify(text) {
        return text
            .toString()
            .toLowerCase()
            .trim()
            .replace(/\s+/g, '-')
            .replace(/[^\w\-]+/g, '')
            .replace(/\-\-+/g, '-')
            .replace(/^-+/, '')
            .replace(/-+$/, '');
    }

    /**
     * نسخ النص إلى الحافظة
     */
    static async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showNotification('تم نسخ النص إلى الحافظة', 'success');
            return true;
        } catch (err) {
            console.error('فشل في نسخ النص:', err);
            this.showNotification('فشل في نسخ النص', 'error');
            return false;
        }
    }

    /**
     * تحقق من صحة البريد الإلكتروني
     */
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * تحقق من صحة رقم الهاتف
     */
    static isValidPhone(phone) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
    }

    /**
     * تنظيف النص من HTML
     */
    static stripHtml(html) {
        const tmp = document.createElement('div');
        tmp.innerHTML = html;
        return tmp.textContent || tmp.innerText || '';
    }

    /**
     * تحويل الحجم بالبايت إلى نص قابل للقراءة
     */
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * تأخير التنفيذ
     */
    static delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * تحقق من كون القيمة فارغة
     */
    static isEmpty(value) {
        return value === null || value === undefined || value === '' ||
               (Array.isArray(value) && value.length === 0) ||
               (typeof value === 'object' && Object.keys(value).length === 0);
    }
}

window.Utils = Utils;

// التحقق من أن الدوال المطلوبة متاحة
console.log('✅ Utils.generateId متاح:', typeof Utils.generateId === 'function');
console.log('✅ Utils.confirm متاح:', typeof Utils.confirm === 'function');
