console.log('Loaded: utils.js');

class Utils {
    static formatDate(date, format = 'YYYY-MM-DD') {
        if (!date) return '';
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        switch (format) {
            case 'YYYY-MM-DD':
                return `${year}-${month}-${day}`;
            case 'DD/MM/YYYY':
                return `${day}/${month}/${year}`;
            case 'DD/MM/YYYY HH:mm':
                return `${day}/${month}/${year} ${hours}:${minutes}`;
            case 'YYYY-MM-DD HH:mm':
                return `${year}-${month}-${day} ${hours}:${minutes}`;
            default:
                return d.toLocaleDateString('ar-EG');
        }
    }

    // تنسيق التاريخ والوقت
    static formatDateTime(date, format = 'DD/MM/YYYY HH:mm') {
        if (!date) return '';
        return this.formatDate(date, format);
    }

    static formatNumber(number, decimals = 0) {
        if (isNaN(number)) return '0';
        return Number(number).toLocaleString('ar-EG', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    }

    static formatCurrency(amount, currency = 'جنيه') {
        if (isNaN(amount)) return `0 ${currency}`;
        return `${Number(amount).toLocaleString('ar-EG', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} ${currency}`;
    }

    static showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notifications');
        if (!container) return;
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        container.appendChild(notification);
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, duration);
    }

    static getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    static generateCaseNumber() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        return `${year}${month}-001`;
    }

    static updateRecentActivities(activities) {
        const container = document.getElementById('recentActivities');
        if (!container) return;
        const activitiesHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="fas fa-${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-text">${activity.text}</div>
                    <div class="activity-time">${this.formatDate(activity.time, 'DD/MM/YYYY HH:mm')}</div>
                </div>
            </div>
        `).join('');
        container.innerHTML = activitiesHTML || '<div class="no-activities">لا توجد نشاطات حديثة</div>';
    }

    static createModal({ title = '', content = '', size = 'medium' }) {
        console.log('=== Utils.createModal CALLED ===', { title, content, size });
        let overlay = document.getElementById('modalOverlay');
        let modalContent = document.getElementById('modalContent');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'modalOverlay';
            overlay.className = 'modal-overlay';
            document.body.appendChild(overlay);
        }
        if (!modalContent) {
            modalContent = document.createElement('div');
            modalContent.id = 'modalContent';
            modalContent.className = 'modal-content';
            overlay.appendChild(modalContent);
        }
        modalContent.innerHTML = content;
        overlay.style.display = 'flex';
        overlay.style.zIndex = '99999';
        modalContent.style.zIndex = '100000';
        console.log('=== Modal overlay display set to flex ===');
        overlay.onclick = (e) => {
            if (e.target === overlay) Utils.closeModal();
        };
    }

    static closeModal() {
        const overlay = document.getElementById('modalOverlay');
        if (overlay) overlay.style.display = 'none';
    }
}

window.Utils = Utils;
