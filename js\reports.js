// إدارة التقارير المتقدمة
console.log('🔄 بدء تحميل ملف reports.js...');

class Reports {
    static currentReports = [];
    static reportTypes = {
        prosthetics: 'تقارير التركيبات',
        financial: 'التقارير المالية',
        doctors: 'تقارير الأطباء',
        employees: 'تقارير الموظفين',
        inventory: 'تقارير المخزون',
        performance: 'تقارير الأداء'
    };

    // عرض صفحة التقارير
    static render() {
        try {
            const pageContent = document.getElementById('pageContent');
            if (!pageContent) {
                console.error('pageContent element not found');
                return;
            }

            pageContent.innerHTML = `
                <div class="reports-container">
                    <!-- عنوان الإدارة المطور -->
                    <div class="department-header reports">
                        <div class="department-header-content">
                            <div class="department-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="department-info">
                                <h1 class="department-name" data-text="إدارة التقارير">إدارة التقارير</h1>
                                <p class="department-description">تقارير شاملة ومفصلة لجميع جوانب العمل في المعمل</p>
                                <div class="department-stats">
                                    <div class="department-stat">
                                        <span class="department-stat-value" id="total-reports-count">0</span>
                                        <span class="department-stat-label">إجمالي التقارير</span>
                                    </div>
                                    <div class="department-stat">
                                        <span class="department-stat-value" id="monthly-reports-count">0</span>
                                        <span class="department-stat-label">تقارير الشهر</span>
                                    </div>
                                    <div class="department-stat">
                                        <span class="department-stat-value" id="report-categories-count">6</span>
                                        <span class="department-stat-label">فئات التقارير</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار التقارير السريعة -->
                    <div class="page-actions-container">
                        <div class="page-actions">
                            <button class="btn btn-primary" onclick="Reports.generateQuickReport()">
                                <i class="fas fa-bolt"></i>
                                تقرير سريع
                            </button>
                            <button class="btn btn-success" onclick="Reports.showCustomReportModal()">
                                <i class="fas fa-cogs"></i>
                                تقرير مخصص
                            </button>
                            <button class="btn btn-info" onclick="Reports.showReportHistory()">
                                <i class="fas fa-history"></i>
                                سجل التقارير
                            </button>
                            <button class="btn btn-warning" onclick="Reports.exportAllReports()">
                                <i class="fas fa-download"></i>
                                تصدير جميع التقارير
                            </button>
                        </div>
                    </div>

                    <!-- فئات التقارير -->
                    <div class="reports-categories">
                        <div class="categories-grid">
                            <div class="report-category" onclick="Reports.showProstheticsReports()">
                                <div class="category-icon">
                                    <i class="fas fa-tooth"></i>
                                </div>
                                <div class="category-info">
                                    <h3>تقارير التركيبات</h3>
                                    <p>تقارير مفصلة عن جميع أعمال التركيبات</p>
                                    <div class="category-stats">
                                        <span id="prosthetics-reports-count">0 تقرير</span>
                                    </div>
                                </div>
                                <div class="category-arrow">
                                    <i class="fas fa-chevron-left"></i>
                                </div>
                            </div>

                            <div class="report-category" onclick="Reports.showFinancialReports()">
                                <div class="category-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="category-info">
                                    <h3>التقارير المالية</h3>
                                    <p>تقارير الإيرادات والمصروفات والأرباح</p>
                                    <div class="category-stats">
                                        <span id="financial-reports-count">0 تقرير</span>
                                    </div>
                                </div>
                                <div class="category-arrow">
                                    <i class="fas fa-chevron-left"></i>
                                </div>
                            </div>

                            <div class="report-category" onclick="Reports.showDoctorsReports()">
                                <div class="category-icon">
                                    <i class="fas fa-user-md"></i>
                                </div>
                                <div class="category-info">
                                    <h3>تقارير الأطباء</h3>
                                    <p>تقارير أداء وإحصائيات الأطباء</p>
                                    <div class="category-stats">
                                        <span id="doctors-reports-count">0 تقرير</span>
                                    </div>
                                </div>
                                <div class="category-arrow">
                                    <i class="fas fa-chevron-left"></i>
                                </div>
                            </div>

                            <div class="report-category" onclick="Reports.showEmployeesReports()">
                                <div class="category-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="category-info">
                                    <h3>تقارير الموظفين</h3>
                                    <p>تقارير الرواتب والأداء والحضور</p>
                                    <div class="category-stats">
                                        <span id="employees-reports-count">0 تقرير</span>
                                    </div>
                                </div>
                                <div class="category-arrow">
                                    <i class="fas fa-chevron-left"></i>
                                </div>
                            </div>

                            <div class="report-category" onclick="Reports.showInventoryReports()">
                                <div class="category-icon">
                                    <i class="fas fa-boxes"></i>
                                </div>
                                <div class="category-info">
                                    <h3>تقارير المخزون</h3>
                                    <p>تقارير المواد والأدوات والمستويات</p>
                                    <div class="category-stats">
                                        <span id="inventory-reports-count">0 تقرير</span>
                                    </div>
                                </div>
                                <div class="category-arrow">
                                    <i class="fas fa-chevron-left"></i>
                                </div>
                            </div>

                            <div class="report-category" onclick="Reports.showPerformanceReports()">
                                <div class="category-icon">
                                    <i class="fas fa-chart-pie"></i>
                                </div>
                                <div class="category-info">
                                    <h3>تقارير الأداء</h3>
                                    <p>تقارير الأداء العام والإنتاجية</p>
                                    <div class="category-stats">
                                        <span id="performance-reports-count">0 تقرير</span>
                                    </div>
                                </div>
                                <div class="category-arrow">
                                    <i class="fas fa-chevron-left"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- منطقة عرض التقارير -->
                    <div class="reports-display-area" id="reports-display">
                        <div class="welcome-message">
                            <div class="welcome-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <h3>مرحباً بك في إدارة التقارير</h3>
                            <p>اختر نوع التقرير الذي تريد إنشاؤه من الفئات أعلاه</p>
                            <div class="quick-actions">
                                <button class="btn btn-primary" onclick="Reports.generateQuickReport()">
                                    <i class="fas fa-bolt"></i>
                                    إنشاء تقرير سريع
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            this.loadData();
            this.updateStats();

        } catch (error) {
            console.error('Error in Reports.render:', error);
            Utils.showNotification('حدث خطأ في تحميل صفحة التقارير', 'error');
        }
    }

    // تحميل البيانات
    static loadData() {
        try {
            // تحميل التقارير المحفوظة
            this.currentReports = Database.getItem('reports') || [];
            console.log('تم تحميل', this.currentReports.length, 'تقرير');
        } catch (error) {
            console.error('خطأ في تحميل بيانات التقارير:', error);
            this.currentReports = [];
        }
    }

    // تحديث الإحصائيات
    static updateStats() {
        try {
            const totalReports = this.currentReports.length;
            const currentMonth = new Date().getMonth();
            const currentYear = new Date().getFullYear();
            
            const monthlyReports = this.currentReports.filter(report => {
                const reportDate = new Date(report.createdAt);
                return reportDate.getMonth() === currentMonth && reportDate.getFullYear() === currentYear;
            }).length;

            // تحديث إحصائيات العنوان
            const totalReportsCount = document.getElementById('total-reports-count');
            const monthlyReportsCount = document.getElementById('monthly-reports-count');
            
            if (totalReportsCount) totalReportsCount.textContent = totalReports;
            if (monthlyReportsCount) monthlyReportsCount.textContent = monthlyReports;

            // تحديث إحصائيات الفئات
            this.updateCategoryStats();

        } catch (error) {
            console.error('خطأ في تحديث إحصائيات التقارير:', error);
        }
    }

    // تحديث إحصائيات الفئات
    static updateCategoryStats() {
        const categories = ['prosthetics', 'financial', 'doctors', 'employees', 'inventory', 'performance'];

        categories.forEach(category => {
            const categoryReports = this.currentReports.filter(r => r.type === category).length;
            const element = document.getElementById(`${category}-reports-count`);
            if (element) {
                element.textContent = `${categoryReports} تقرير`;
            }
        });
    }

    // إنشاء تقرير سريع
    static generateQuickReport() {
        try {
            const reportData = this.generateQuickReportData();

            Utils.createModal({
                title: 'التقرير السريع',
                content: `
                    <div class="quick-report">
                        <div class="report-header">
                            <h3><i class="fas fa-bolt"></i> تقرير سريع - ${Utils.formatDate(new Date(), 'DD/MM/YYYY')}</h3>
                            <p>نظرة عامة سريعة على حالة المعمل</p>
                        </div>

                        <div class="report-sections">
                            <!-- قسم التركيبات -->
                            <div class="report-section">
                                <h4><i class="fas fa-tooth"></i> التركيبات</h4>
                                <div class="report-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">إجمالي التركيبات:</span>
                                        <span class="stat-value">${reportData.prosthetics.total}</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">في الانتظار:</span>
                                        <span class="stat-value pending">${reportData.prosthetics.pending}</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">مكتملة:</span>
                                        <span class="stat-value completed">${reportData.prosthetics.completed}</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">الإيرادات:</span>
                                        <span class="stat-value">${Utils.formatCurrency(reportData.prosthetics.revenue)}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- قسم الأطباء -->
                            <div class="report-section">
                                <h4><i class="fas fa-user-md"></i> الأطباء</h4>
                                <div class="report-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">إجمالي الأطباء:</span>
                                        <span class="stat-value">${reportData.doctors.total}</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">الأطباء النشطين:</span>
                                        <span class="stat-value">${reportData.doctors.active}</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">أعمال هذا الشهر:</span>
                                        <span class="stat-value">${reportData.doctors.monthlyWorks}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- قسم الموظفين -->
                            <div class="report-section">
                                <h4><i class="fas fa-users"></i> الموظفين</h4>
                                <div class="report-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">إجمالي الموظفين:</span>
                                        <span class="stat-value">${reportData.employees.total}</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">إجمالي الرواتب:</span>
                                        <span class="stat-value">${Utils.formatCurrency(reportData.employees.totalSalaries)}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- قسم المالية -->
                            <div class="report-section">
                                <h4><i class="fas fa-chart-line"></i> المالية</h4>
                                <div class="report-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">إجمالي الإيرادات:</span>
                                        <span class="stat-value income">${Utils.formatCurrency(reportData.financial.totalIncome)}</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">إجمالي المصروفات:</span>
                                        <span class="stat-value expense">${Utils.formatCurrency(reportData.financial.totalExpenses)}</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">صافي الربح:</span>
                                        <span class="stat-value ${reportData.financial.netProfit >= 0 ? 'profit' : 'loss'}">
                                            ${Utils.formatCurrency(reportData.financial.netProfit)}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="report-actions">
                            <button class="btn btn-primary" onclick="Reports.saveReport('quick', '${JSON.stringify(reportData).replace(/"/g, '&quot;')}')">
                                <i class="fas fa-save"></i>
                                حفظ التقرير
                            </button>
                            <button class="btn btn-success" onclick="Reports.exportReport('quick', '${JSON.stringify(reportData).replace(/"/g, '&quot;')}')">
                                <i class="fas fa-download"></i>
                                تصدير PDF
                            </button>
                            <button class="btn btn-info" onclick="Reports.printReport()">
                                <i class="fas fa-print"></i>
                                طباعة
                            </button>
                            <button class="btn btn-secondary" onclick="Utils.closeModal()">
                                <i class="fas fa-times"></i>
                                إغلاق
                            </button>
                        </div>
                    </div>
                `,
                size: 'large'
            });

        } catch (error) {
            console.error('خطأ في إنشاء التقرير السريع:', error);
            Utils.showNotification('حدث خطأ في إنشاء التقرير السريع', 'error');
        }
    }

    // جمع بيانات التقرير السريع
    static generateQuickReportData() {
        const prosthetics = Database.getProsthetics() || [];
        const doctors = Database.getDoctors() || [];
        const employees = Database.getEmployees() || [];
        const expenses = Database.getItem('expenses') || [];

        // إحصائيات التركيبات
        const prostheticsStats = {
            total: prosthetics.length,
            pending: prosthetics.filter(p => p.status === 'pending').length,
            completed: prosthetics.filter(p => p.status === 'completed' || p.status === 'delivered').length,
            revenue: prosthetics.filter(p => p.status === 'completed' || p.status === 'delivered')
                .reduce((sum, p) => sum + (p.totalPrice || 0), 0)
        };

        // إحصائيات الأطباء
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();
        const monthlyWorks = prosthetics.filter(p => {
            const workDate = new Date(p.createdAt);
            return workDate.getMonth() === currentMonth && workDate.getFullYear() === currentYear;
        }).length;

        const doctorsStats = {
            total: doctors.length,
            active: doctors.filter(d => {
                return prosthetics.some(p => p.doctorId === d.id);
            }).length,
            monthlyWorks: monthlyWorks
        };

        // إحصائيات الموظفين
        const employeesStats = {
            total: employees.length,
            totalSalaries: employees.reduce((sum, e) => sum + (e.baseSalary || 0), 0)
        };

        // إحصائيات المالية
        const totalIncome = prostheticsStats.revenue;
        const totalExpenses = expenses.reduce((sum, e) => sum + (e.amount || 0), 0);
        const netProfit = totalIncome - totalExpenses;

        const financialStats = {
            totalIncome: totalIncome,
            totalExpenses: totalExpenses,
            netProfit: netProfit
        };

        return {
            prosthetics: prostheticsStats,
            doctors: doctorsStats,
            employees: employeesStats,
            financial: financialStats,
            generatedAt: new Date().toISOString()
        };
    }

    // عرض نافذة التقرير المخصص
    static showCustomReportModal() {
        Utils.createModal({
            title: 'إنشاء تقرير مخصص',
            content: `
                <form id="custom-report-form" class="custom-report-form">
                    <div class="form-section">
                        <h4><i class="fas fa-cogs"></i> إعدادات التقرير</h4>

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="report-title">عنوان التقرير *</label>
                                <input type="text" id="report-title" required placeholder="أدخل عنوان التقرير">
                            </div>

                            <div class="form-group">
                                <label for="report-type">نوع التقرير *</label>
                                <select id="report-type" required>
                                    <option value="">اختر نوع التقرير</option>
                                    <option value="prosthetics">تقرير التركيبات</option>
                                    <option value="financial">تقرير مالي</option>
                                    <option value="doctors">تقرير الأطباء</option>
                                    <option value="employees">تقرير الموظفين</option>
                                    <option value="inventory">تقرير المخزون</option>
                                    <option value="performance">تقرير الأداء</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="date-from">من تاريخ</label>
                                <input type="date" id="date-from">
                            </div>

                            <div class="form-group">
                                <label for="date-to">إلى تاريخ</label>
                                <input type="date" id="date-to" value="">
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h4><i class="fas fa-filter"></i> فلاتر التقرير</h4>

                        <div class="filters-grid">
                            <div class="filter-group">
                                <label>
                                    <input type="checkbox" id="include-pending" checked>
                                    تضمين الأعمال المعلقة
                                </label>
                            </div>
                            <div class="filter-group">
                                <label>
                                    <input type="checkbox" id="include-completed" checked>
                                    تضمين الأعمال المكتملة
                                </label>
                            </div>
                            <div class="filter-group">
                                <label>
                                    <input type="checkbox" id="include-charts" checked>
                                    تضمين الرسوم البيانية
                                </label>
                            </div>
                            <div class="filter-group">
                                <label>
                                    <input type="checkbox" id="include-details" checked>
                                    تضمين التفاصيل
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="modal-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-chart-bar"></i>
                            إنشاء التقرير
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            `,
            size: 'large'
        });

        // إعداد النموذج
        const form = document.getElementById('custom-report-form');
        if (form) {
            // تعيين التاريخ الافتراضي
            const dateToInput = document.getElementById('date-to');
            if (dateToInput) {
                const today = new Date();
                const dateString = today.getFullYear() + '-' +
                                  String(today.getMonth() + 1).padStart(2, '0') + '-' +
                                  String(today.getDate()).padStart(2, '0');
                dateToInput.value = dateString;
            }

            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.generateCustomReport();
            });
        }
    }

    // إنشاء تقرير مخصص
    static generateCustomReport() {
        try {
            const formData = new FormData(document.getElementById('custom-report-form'));

            const reportConfig = {
                title: formData.get('report-title'),
                type: formData.get('report-type'),
                dateFrom: formData.get('date-from'),
                dateTo: formData.get('date-to'),
                includePending: document.getElementById('include-pending').checked,
                includeCompleted: document.getElementById('include-completed').checked,
                includeCharts: document.getElementById('include-charts').checked,
                includeDetails: document.getElementById('include-details').checked
            };

            if (!reportConfig.title || !reportConfig.type) {
                Utils.showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            Utils.closeModal();

            // إنشاء التقرير حسب النوع
            switch (reportConfig.type) {
                case 'prosthetics':
                    this.generateProstheticsReport(reportConfig);
                    break;
                case 'financial':
                    this.generateFinancialReport(reportConfig);
                    break;
                case 'doctors':
                    this.generateDoctorsReport(reportConfig);
                    break;
                case 'employees':
                    this.generateEmployeesReport(reportConfig);
                    break;
                case 'inventory':
                    this.generateInventoryReport(reportConfig);
                    break;
                case 'performance':
                    this.generatePerformanceReport(reportConfig);
                    break;
                default:
                    Utils.showNotification('نوع التقرير غير مدعوم', 'error');
            }

        } catch (error) {
            console.error('خطأ في إنشاء التقرير المخصص:', error);
            Utils.showNotification('حدث خطأ في إنشاء التقرير', 'error');
        }
    }

    // تقرير التركيبات
    static generateProstheticsReport(config) {
        const prosthetics = Database.getProsthetics() || [];
        let filteredData = prosthetics;

        // تطبيق فلاتر التاريخ
        if (config.dateFrom) {
            filteredData = filteredData.filter(p => new Date(p.createdAt) >= new Date(config.dateFrom));
        }
        if (config.dateTo) {
            filteredData = filteredData.filter(p => new Date(p.createdAt) <= new Date(config.dateTo));
        }

        // تطبيق فلاتر الحالة
        if (!config.includePending) {
            filteredData = filteredData.filter(p => p.status !== 'pending');
        }
        if (!config.includeCompleted) {
            filteredData = filteredData.filter(p => p.status !== 'completed' && p.status !== 'delivered');
        }

        const reportData = {
            config: config,
            data: filteredData,
            summary: {
                total: filteredData.length,
                totalRevenue: filteredData.reduce((sum, p) => sum + (p.totalPrice || 0), 0),
                averagePrice: filteredData.length > 0 ?
                    filteredData.reduce((sum, p) => sum + (p.totalPrice || 0), 0) / filteredData.length : 0,
                byStatus: this.groupByStatus(filteredData),
                byType: this.groupByType(filteredData),
                byDoctor: this.groupByDoctor(filteredData)
            }
        };

        this.displayReport('prosthetics', reportData);
    }

    // دوال مساعدة للتجميع
    static groupByStatus(data) {
        const groups = {};
        data.forEach(item => {
            const status = item.status || 'unknown';
            if (!groups[status]) groups[status] = 0;
            groups[status]++;
        });
        return groups;
    }

    static groupByType(data) {
        const groups = {};
        data.forEach(item => {
            const type = item.type || 'unknown';
            if (!groups[type]) groups[type] = 0;
            groups[type]++;
        });
        return groups;
    }

    static groupByDoctor(data) {
        const groups = {};
        data.forEach(item => {
            const doctor = item.doctorName || 'غير محدد';
            if (!groups[doctor]) groups[doctor] = 0;
            groups[doctor]++;
        });
        return groups;
    }

    // عرض التقرير
    static displayReport(type, reportData) {
        const displayArea = document.getElementById('reports-display');
        if (!displayArea) return;

        let reportHTML = '';

        switch (type) {
            case 'prosthetics':
                reportHTML = this.generateProstheticsReportHTML(reportData);
                break;
            default:
                reportHTML = '<p>نوع التقرير غير مدعوم</p>';
        }

        displayArea.innerHTML = reportHTML;
        displayArea.scrollIntoView({ behavior: 'smooth' });
    }

    // إنشاء HTML لتقرير التركيبات
    static generateProstheticsReportHTML(reportData) {
        const { config, data, summary } = reportData;

        return `
            <div class="report-display">
                <div class="report-header">
                    <h2><i class="fas fa-tooth"></i> ${config.title}</h2>
                    <div class="report-meta">
                        <span><i class="fas fa-calendar"></i> ${Utils.formatDate(new Date(), 'DD/MM/YYYY HH:mm')}</span>
                        <span><i class="fas fa-filter"></i> ${config.dateFrom ? 'من ' + Utils.formatDate(config.dateFrom, 'DD/MM/YYYY') : 'جميع التواريخ'}</span>
                        ${config.dateTo ? '<span>إلى ' + Utils.formatDate(config.dateTo, 'DD/MM/YYYY') + '</span>' : ''}
                    </div>
                </div>

                <!-- ملخص التقرير -->
                <div class="report-summary">
                    <h3><i class="fas fa-chart-pie"></i> ملخص التقرير</h3>
                    <div class="summary-cards">
                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-list"></i>
                            </div>
                            <div class="card-content">
                                <h4>إجمالي التركيبات</h4>
                                <span class="card-value">${summary.total}</span>
                            </div>
                        </div>
                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-money-bill"></i>
                            </div>
                            <div class="card-content">
                                <h4>إجمالي الإيرادات</h4>
                                <span class="card-value">${Utils.formatCurrency(summary.totalRevenue)}</span>
                            </div>
                        </div>
                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-calculator"></i>
                            </div>
                            <div class="card-content">
                                <h4>متوسط السعر</h4>
                                <span class="card-value">${Utils.formatCurrency(summary.averagePrice)}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات حسب الحالة -->
                <div class="report-section">
                    <h3><i class="fas fa-chart-bar"></i> التوزيع حسب الحالة</h3>
                    <div class="stats-grid">
                        ${Object.entries(summary.byStatus).map(([status, count]) => `
                            <div class="stat-item">
                                <span class="stat-label">${this.getStatusDisplayName(status)}:</span>
                                <span class="stat-value">${count}</span>
                                <span class="stat-percentage">(${((count / summary.total) * 100).toFixed(1)}%)</span>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <!-- إحصائيات حسب النوع -->
                <div class="report-section">
                    <h3><i class="fas fa-tooth"></i> التوزيع حسب النوع</h3>
                    <div class="stats-grid">
                        ${Object.entries(summary.byType).map(([type, count]) => `
                            <div class="stat-item">
                                <span class="stat-label">${this.getTypeDisplayName(type)}:</span>
                                <span class="stat-value">${count}</span>
                                <span class="stat-percentage">(${((count / summary.total) * 100).toFixed(1)}%)</span>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <!-- إحصائيات حسب الطبيب -->
                <div class="report-section">
                    <h3><i class="fas fa-user-md"></i> التوزيع حسب الطبيب</h3>
                    <div class="stats-grid">
                        ${Object.entries(summary.byDoctor).map(([doctor, count]) => `
                            <div class="stat-item">
                                <span class="stat-label">${doctor}:</span>
                                <span class="stat-value">${count}</span>
                                <span class="stat-percentage">(${((count / summary.total) * 100).toFixed(1)}%)</span>
                            </div>
                        `).join('')}
                    </div>
                </div>

                ${config.includeDetails ? `
                    <!-- تفاصيل التركيبات -->
                    <div class="report-section">
                        <h3><i class="fas fa-list-ul"></i> تفاصيل التركيبات</h3>
                        <div class="table-wrapper">
                            <table class="report-table">
                                <thead>
                                    <tr>
                                        <th>رقم الحالة</th>
                                        <th>اسم المريض</th>
                                        <th>الطبيب</th>
                                        <th>النوع</th>
                                        <th>السعر</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${data.map(item => `
                                        <tr>
                                            <td>${item.caseNumber || item.workNumber}</td>
                                            <td>${item.patientName}</td>
                                            <td>${item.doctorName}</td>
                                            <td>${this.getTypeDisplayName(item.type)}</td>
                                            <td>${Utils.formatCurrency(item.totalPrice || 0)}</td>
                                            <td><span class="status-badge status-${item.status}">${this.getStatusDisplayName(item.status)}</span></td>
                                            <td>${Utils.formatDate(item.createdAt, 'DD/MM/YYYY')}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                ` : ''}

                <!-- أزرار التقرير -->
                <div class="report-actions">
                    <button class="btn btn-primary" onclick="Reports.saveReport('prosthetics', '${JSON.stringify(reportData).replace(/"/g, '&quot;')}')">
                        <i class="fas fa-save"></i>
                        حفظ التقرير
                    </button>
                    <button class="btn btn-success" onclick="Reports.exportReportPDF('prosthetics', '${JSON.stringify(reportData).replace(/"/g, '&quot;')}')">
                        <i class="fas fa-file-pdf"></i>
                        تصدير PDF
                    </button>
                    <button class="btn btn-info" onclick="Reports.exportReportExcel('prosthetics', '${JSON.stringify(reportData).replace(/"/g, '&quot;')}')">
                        <i class="fas fa-file-excel"></i>
                        تصدير Excel
                    </button>
                    <button class="btn btn-warning" onclick="window.print()">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                </div>
            </div>
        `;
    }

    // الحصول على اسم عرض الحالة
    static getStatusDisplayName(status) {
        const statusNames = {
            pending: 'في الانتظار',
            progress: 'قيد التنفيذ',
            completed: 'مكتملة',
            delivered: 'تم التسليم',
            unknown: 'غير محدد'
        };
        return statusNames[status] || status;
    }

    // الحصول على اسم عرض النوع
    static getTypeDisplayName(type) {
        const typeNames = {
            'crown-porcelain': 'تاج بورسلين',
            'crown-zircon': 'تاج زيركون',
            'crown-metal': 'تاج معدني',
            'bridge-porcelain': 'جسر بورسلين',
            'bridge-zircon': 'جسر زيركون',
            'veneer-porcelain': 'فينير بورسلين',
            'veneer-composite': 'فينير كومبوزيت',
            'denture-partial': 'طقم جزئي',
            'denture-complete': 'طقم كامل',
            'implant-crown': 'تاج زراعة',
            unknown: 'غير محدد'
        };
        return typeNames[type] || type;
    }

    // حفظ التقرير
    static saveReport(type, reportDataStr) {
        try {
            const reportData = JSON.parse(reportDataStr.replace(/&quot;/g, '"'));

            const report = {
                id: Utils.generateId(),
                type: type,
                title: reportData.config ? reportData.config.title : 'تقرير سريع',
                data: reportData,
                createdAt: new Date().toISOString(),
                createdBy: 'المستخدم الحالي'
            };

            this.currentReports.push(report);
            Database.setItem('reports', this.currentReports);

            Utils.showNotification('تم حفظ التقرير بنجاح', 'success');
            this.updateStats();

        } catch (error) {
            console.error('خطأ في حفظ التقرير:', error);
            Utils.showNotification('حدث خطأ في حفظ التقرير', 'error');
        }
    }

    // تصدير التقرير كـ PDF
    static exportReportPDF(type, reportDataStr) {
        try {
            Utils.showNotification('جاري تحضير ملف PDF...', 'info');

            // محاكاة تصدير PDF
            setTimeout(() => {
                const reportData = JSON.parse(reportDataStr.replace(/&quot;/g, '"'));
                const title = reportData.config ? reportData.config.title : 'تقرير سريع';
                const filename = `${title}_${Utils.formatDate(new Date(), 'YYYY-MM-DD')}.pdf`;

                Utils.showNotification(`تم تصدير التقرير: ${filename}`, 'success');
            }, 2000);

        } catch (error) {
            console.error('خطأ في تصدير PDF:', error);
            Utils.showNotification('حدث خطأ في تصدير PDF', 'error');
        }
    }

    // تصدير التقرير كـ Excel
    static exportReportExcel(type, reportDataStr) {
        try {
            const reportData = JSON.parse(reportDataStr.replace(/&quot;/g, '"'));
            const title = reportData.config ? reportData.config.title : 'تقرير سريع';

            let csvContent = '';

            if (type === 'prosthetics' && reportData.data) {
                // إنشاء CSV للتركيبات
                csvContent = 'رقم الحالة,اسم المريض,الطبيب,النوع,السعر,الحالة,التاريخ\n';
                reportData.data.forEach(item => {
                    csvContent += `"${item.caseNumber || item.workNumber}","${item.patientName}","${item.doctorName}","${this.getTypeDisplayName(item.type)}","${item.totalPrice || 0}","${this.getStatusDisplayName(item.status)}","${Utils.formatDate(item.createdAt, 'DD/MM/YYYY')}"\n`;
                });
            }

            // تحميل الملف
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${title}_${Utils.formatDate(new Date(), 'YYYY-MM-DD')}.csv`;
            link.click();

            Utils.showNotification('تم تصدير التقرير بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في تصدير Excel:', error);
            Utils.showNotification('حدث خطأ في تصدير Excel', 'error');
        }
    }

    // عرض سجل التقارير
    static showReportHistory() {
        Utils.createModal({
            title: 'سجل التقارير',
            content: `
                <div class="report-history">
                    <div class="history-header">
                        <h3><i class="fas fa-history"></i> التقارير المحفوظة</h3>
                        <p>جميع التقارير التي تم إنشاؤها وحفظها</p>
                    </div>

                    <div class="history-list">
                        ${this.currentReports.length > 0 ? this.currentReports.map(report => `
                            <div class="history-item">
                                <div class="item-icon">
                                    <i class="fas fa-${this.getReportIcon(report.type)}"></i>
                                </div>
                                <div class="item-content">
                                    <h4>${report.title}</h4>
                                    <p>النوع: ${this.reportTypes[report.type] || report.type}</p>
                                    <small>تم الإنشاء: ${Utils.formatDate(report.createdAt, 'DD/MM/YYYY HH:mm')}</small>
                                </div>
                                <div class="item-actions">
                                    <button class="btn-small btn-info" onclick="Reports.viewSavedReport('${report.id}')" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn-small btn-success" onclick="Reports.exportSavedReport('${report.id}')" title="تصدير">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn-small btn-danger" onclick="Reports.deleteSavedReport('${report.id}')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        `).join('') : `
                            <div class="no-reports">
                                <i class="fas fa-chart-bar"></i>
                                <p>لا توجد تقارير محفوظة</p>
                                <small>ابدأ بإنشاء تقرير جديد</small>
                            </div>
                        `}
                    </div>

                    <div class="modal-actions">
                        <button class="btn btn-primary" onclick="Reports.generateQuickReport(); Utils.closeModal();">
                            <i class="fas fa-plus"></i>
                            إنشاء تقرير جديد
                        </button>
                        <button class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            `,
            size: 'large'
        });
    }

    // الحصول على أيقونة التقرير
    static getReportIcon(type) {
        const icons = {
            prosthetics: 'tooth',
            financial: 'chart-line',
            doctors: 'user-md',
            employees: 'users',
            inventory: 'boxes',
            performance: 'chart-pie',
            quick: 'bolt'
        };
        return icons[type] || 'chart-bar';
    }

    // عرض تقرير محفوظ
    static viewSavedReport(reportId) {
        const report = this.currentReports.find(r => r.id === reportId);
        if (!report) {
            Utils.showNotification('التقرير غير موجود', 'error');
            return;
        }

        Utils.closeModal();
        this.displayReport(report.type, report.data);
    }

    // حذف تقرير محفوظ
    static deleteSavedReport(reportId) {
        if (!confirm('هل أنت متأكد من حذف هذا التقرير؟')) {
            return;
        }

        this.currentReports = this.currentReports.filter(r => r.id !== reportId);
        Database.setItem('reports', this.currentReports);

        Utils.showNotification('تم حذف التقرير بنجاح', 'success');
        this.updateStats();
        this.showReportHistory(); // إعادة تحميل النافذة
    }

    // تصدير جميع التقارير
    static exportAllReports() {
        try {
            const exportData = {
                reports: this.currentReports,
                exportDate: new Date().toISOString(),
                totalReports: this.currentReports.length
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `all-reports-${Utils.formatDate(new Date(), 'YYYY-MM-DD')}.json`;
            link.click();

            Utils.showNotification('تم تصدير جميع التقارير بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في تصدير التقارير:', error);
            Utils.showNotification('حدث خطأ في تصدير التقارير', 'error');
        }
    }

    // عرض تقارير التركيبات
    static showProstheticsReports() {
        const today = new Date();
        const dateString = today.getFullYear() + '-' +
                          String(today.getMonth() + 1).padStart(2, '0') + '-' +
                          String(today.getDate()).padStart(2, '0');

        const config = {
            title: 'تقرير التركيبات الشامل',
            type: 'prosthetics',
            dateFrom: null,
            dateTo: dateString,
            includePending: true,
            includeCompleted: true,
            includeCharts: true,
            includeDetails: true
        };

        this.generateProstheticsReport(config);
    }

    // عرض التقارير المالية
    static showFinancialReports() {
        const prosthetics = Database.getProsthetics() || [];
        const expenses = Database.getItem('expenses') || [];

        const reportData = {
            config: {
                title: 'التقرير المالي الشامل',
                type: 'financial'
            },
            summary: {
                totalIncome: prosthetics.filter(p => p.status === 'completed' || p.status === 'delivered')
                    .reduce((sum, p) => sum + (p.totalPrice || 0), 0),
                totalExpenses: expenses.reduce((sum, e) => sum + (e.amount || 0), 0),
                monthlyIncome: this.getMonthlyIncome(prosthetics),
                monthlyExpenses: this.getMonthlyExpenses(expenses)
            }
        };

        reportData.summary.netProfit = reportData.summary.totalIncome - reportData.summary.totalExpenses;

        this.displayFinancialReport(reportData);
    }

    // عرض تقارير الأطباء
    static showDoctorsReports() {
        const doctors = Database.getDoctors() || [];
        const prosthetics = Database.getProsthetics() || [];

        const doctorsStats = doctors.map(doctor => {
            const doctorWorks = prosthetics.filter(p => p.doctorId === doctor.id);
            return {
                doctor: doctor,
                totalWorks: doctorWorks.length,
                completedWorks: doctorWorks.filter(p => p.status === 'completed' || p.status === 'delivered').length,
                totalRevenue: doctorWorks.filter(p => p.status === 'completed' || p.status === 'delivered')
                    .reduce((sum, p) => sum + (p.totalPrice || 0), 0),
                averagePrice: doctorWorks.length > 0 ?
                    doctorWorks.reduce((sum, p) => sum + (p.totalPrice || 0), 0) / doctorWorks.length : 0
            };
        });

        const reportData = {
            config: {
                title: 'تقرير الأطباء الشامل',
                type: 'doctors'
            },
            data: doctorsStats,
            summary: {
                totalDoctors: doctors.length,
                activeDoctors: doctorsStats.filter(d => d.totalWorks > 0).length,
                totalWorks: doctorsStats.reduce((sum, d) => sum + d.totalWorks, 0),
                totalRevenue: doctorsStats.reduce((sum, d) => sum + d.totalRevenue, 0)
            }
        };

        this.displayDoctorsReport(reportData);
    }

    // عرض تقارير الموظفين
    static showEmployeesReports() {
        const employees = Database.getEmployees() || [];

        const reportData = {
            config: {
                title: 'تقرير الموظفين الشامل',
                type: 'employees'
            },
            data: employees,
            summary: {
                totalEmployees: employees.length,
                totalSalaries: employees.reduce((sum, e) => sum + (e.baseSalary || 0), 0),
                averageSalary: employees.length > 0 ?
                    employees.reduce((sum, e) => sum + (e.baseSalary || 0), 0) / employees.length : 0,
                byDepartment: this.groupEmployeesByDepartment(employees)
            }
        };

        this.displayEmployeesReport(reportData);
    }

    // عرض تقارير المخزون
    static showInventoryReports() {
        const inventory = Database.getItem('inventory') || [];

        const reportData = {
            config: {
                title: 'تقرير المخزون الشامل',
                type: 'inventory'
            },
            data: inventory,
            summary: {
                totalItems: inventory.length,
                totalValue: inventory.reduce((sum, item) => sum + ((item.quantity || 0) * (item.unitPrice || 0)), 0),
                lowStockItems: inventory.filter(item => (item.quantity || 0) <= (item.minStock || 0)).length,
                byCategory: this.groupInventoryByCategory(inventory)
            }
        };

        this.displayInventoryReport(reportData);
    }

    // عرض تقارير الأداء
    static showPerformanceReports() {
        const prosthetics = Database.getProsthetics() || [];
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();

        const monthlyWorks = prosthetics.filter(p => {
            const workDate = new Date(p.createdAt);
            return workDate.getMonth() === currentMonth && workDate.getFullYear() === currentYear;
        });

        const reportData = {
            config: {
                title: 'تقرير الأداء الشامل',
                type: 'performance'
            },
            summary: {
                monthlyWorks: monthlyWorks.length,
                monthlyRevenue: monthlyWorks.filter(p => p.status === 'completed' || p.status === 'delivered')
                    .reduce((sum, p) => sum + (p.totalPrice || 0), 0),
                completionRate: prosthetics.length > 0 ?
                    (prosthetics.filter(p => p.status === 'completed' || p.status === 'delivered').length / prosthetics.length) * 100 : 0,
                averageCompletionTime: this.calculateAverageCompletionTime(prosthetics)
            }
        };

        this.displayPerformanceReport(reportData);
    }

    // دوال مساعدة
    static getMonthlyIncome(prosthetics) {
        const monthlyData = {};
        prosthetics.filter(p => p.status === 'completed' || p.status === 'delivered').forEach(p => {
            const date = new Date(p.createdAt);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            if (!monthlyData[monthKey]) monthlyData[monthKey] = 0;
            monthlyData[monthKey] += p.totalPrice || 0;
        });
        return monthlyData;
    }

    static getMonthlyExpenses(expenses) {
        const monthlyData = {};
        expenses.forEach(e => {
            const date = new Date(e.date || e.createdAt);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            if (!monthlyData[monthKey]) monthlyData[monthKey] = 0;
            monthlyData[monthKey] += e.amount || 0;
        });
        return monthlyData;
    }

    static groupEmployeesByDepartment(employees) {
        const groups = {};
        employees.forEach(emp => {
            const dept = emp.department || 'غير محدد';
            if (!groups[dept]) groups[dept] = 0;
            groups[dept]++;
        });
        return groups;
    }

    static groupInventoryByCategory(inventory) {
        const groups = {};
        inventory.forEach(item => {
            const category = item.category || 'غير محدد';
            if (!groups[category]) groups[category] = 0;
            groups[category]++;
        });
        return groups;
    }

    static calculateAverageCompletionTime(prosthetics) {
        const completedWorks = prosthetics.filter(p => p.status === 'completed' || p.status === 'delivered');
        if (completedWorks.length === 0) return 0;

        const totalDays = completedWorks.reduce((sum, p) => {
            const startDate = new Date(p.createdAt);
            const endDate = new Date(p.updatedAt || p.createdAt);
            const diffTime = Math.abs(endDate - startDate);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return sum + diffDays;
        }, 0);

        return totalDays / completedWorks.length;
    }

    // عرض التقرير المالي
    static displayFinancialReport(reportData) {
        const displayArea = document.getElementById('reports-display');
        if (!displayArea) return;

        displayArea.innerHTML = `
            <div class="report-display">
                <div class="report-header">
                    <h2><i class="fas fa-chart-line"></i> ${reportData.config.title}</h2>
                    <div class="report-meta">
                        <span><i class="fas fa-calendar"></i> ${Utils.formatDate(new Date(), 'DD/MM/YYYY HH:mm')}</span>
                    </div>
                </div>

                <div class="report-summary">
                    <h3><i class="fas fa-money-bill-wave"></i> الملخص المالي</h3>
                    <div class="summary-cards">
                        <div class="summary-card income">
                            <div class="card-icon">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                            <div class="card-content">
                                <h4>إجمالي الإيرادات</h4>
                                <span class="card-value">${Utils.formatCurrency(reportData.summary.totalIncome)}</span>
                            </div>
                        </div>
                        <div class="summary-card expense">
                            <div class="card-icon">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <div class="card-content">
                                <h4>إجمالي المصروفات</h4>
                                <span class="card-value">${Utils.formatCurrency(reportData.summary.totalExpenses)}</span>
                            </div>
                        </div>
                        <div class="summary-card ${reportData.summary.netProfit >= 0 ? 'profit' : 'loss'}">
                            <div class="card-icon">
                                <i class="fas fa-${reportData.summary.netProfit >= 0 ? 'plus' : 'minus'}"></i>
                            </div>
                            <div class="card-content">
                                <h4>صافي الربح</h4>
                                <span class="card-value">${Utils.formatCurrency(reportData.summary.netProfit)}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="report-actions">
                    <button class="btn btn-primary" onclick="Reports.saveReport('financial', '${JSON.stringify(reportData).replace(/"/g, '&quot;')}')">
                        <i class="fas fa-save"></i>
                        حفظ التقرير
                    </button>
                    <button class="btn btn-success" onclick="Reports.exportReportPDF('financial', '${JSON.stringify(reportData).replace(/"/g, '&quot;')}')">
                        <i class="fas fa-file-pdf"></i>
                        تصدير PDF
                    </button>
                </div>
            </div>
        `;

        displayArea.scrollIntoView({ behavior: 'smooth' });
    }

    // عرض تقرير الأطباء
    static displayDoctorsReport(reportData) {
        const displayArea = document.getElementById('reports-display');
        if (!displayArea) return;

        displayArea.innerHTML = `
            <div class="report-display">
                <div class="report-header">
                    <h2><i class="fas fa-user-md"></i> ${reportData.config.title}</h2>
                    <div class="report-meta">
                        <span><i class="fas fa-calendar"></i> ${Utils.formatDate(new Date(), 'DD/MM/YYYY HH:mm')}</span>
                    </div>
                </div>

                <div class="report-summary">
                    <h3><i class="fas fa-chart-pie"></i> ملخص الأطباء</h3>
                    <div class="summary-cards">
                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-user-md"></i>
                            </div>
                            <div class="card-content">
                                <h4>إجمالي الأطباء</h4>
                                <span class="card-value">${reportData.summary.totalDoctors}</span>
                            </div>
                        </div>
                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="card-content">
                                <h4>الأطباء النشطين</h4>
                                <span class="card-value">${reportData.summary.activeDoctors}</span>
                            </div>
                        </div>
                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-briefcase"></i>
                            </div>
                            <div class="card-content">
                                <h4>إجمالي الأعمال</h4>
                                <span class="card-value">${reportData.summary.totalWorks}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="report-section">
                    <h3><i class="fas fa-list-ul"></i> تفاصيل الأطباء</h3>
                    <div class="table-wrapper">
                        <table class="report-table">
                            <thead>
                                <tr>
                                    <th>اسم الطبيب</th>
                                    <th>التخصص</th>
                                    <th>إجمالي الأعمال</th>
                                    <th>الأعمال المكتملة</th>
                                    <th>إجمالي الإيرادات</th>
                                    <th>متوسط السعر</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${reportData.data.map(item => `
                                    <tr>
                                        <td><strong>${item.doctor.name}</strong></td>
                                        <td>${item.doctor.specialization || 'غير محدد'}</td>
                                        <td>${item.totalWorks}</td>
                                        <td>${item.completedWorks}</td>
                                        <td>${Utils.formatCurrency(item.totalRevenue)}</td>
                                        <td>${Utils.formatCurrency(item.averagePrice)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="report-actions">
                    <button class="btn btn-primary" onclick="Reports.saveReport('doctors', '${JSON.stringify(reportData).replace(/"/g, '&quot;')}')">
                        <i class="fas fa-save"></i>
                        حفظ التقرير
                    </button>
                    <button class="btn btn-success" onclick="Reports.exportReportPDF('doctors', '${JSON.stringify(reportData).replace(/"/g, '&quot;')}')">
                        <i class="fas fa-file-pdf"></i>
                        تصدير PDF
                    </button>
                </div>
            </div>
        `;

        displayArea.scrollIntoView({ behavior: 'smooth' });
    }

    // عرض تقرير الموظفين
    static displayEmployeesReport(reportData) {
        const displayArea = document.getElementById('reports-display');
        if (!displayArea) return;

        displayArea.innerHTML = `
            <div class="report-display">
                <div class="report-header">
                    <h2><i class="fas fa-users"></i> ${reportData.config.title}</h2>
                    <div class="report-meta">
                        <span><i class="fas fa-calendar"></i> ${Utils.formatDate(new Date(), 'DD/MM/YYYY HH:mm')}</span>
                    </div>
                </div>

                <div class="report-summary">
                    <h3><i class="fas fa-chart-pie"></i> ملخص الموظفين</h3>
                    <div class="summary-cards">
                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-content">
                                <h4>إجمالي الموظفين</h4>
                                <span class="card-value">${reportData.summary.totalEmployees}</span>
                            </div>
                        </div>
                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-money-bill"></i>
                            </div>
                            <div class="card-content">
                                <h4>إجمالي الرواتب</h4>
                                <span class="card-value">${Utils.formatCurrency(reportData.summary.totalSalaries)}</span>
                            </div>
                        </div>
                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-calculator"></i>
                            </div>
                            <div class="card-content">
                                <h4>متوسط الراتب</h4>
                                <span class="card-value">${Utils.formatCurrency(reportData.summary.averageSalary)}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="report-section">
                    <h3><i class="fas fa-list-ul"></i> تفاصيل الموظفين</h3>
                    <div class="table-wrapper">
                        <table class="report-table">
                            <thead>
                                <tr>
                                    <th>اسم الموظف</th>
                                    <th>المنصب</th>
                                    <th>القسم</th>
                                    <th>الراتب الأساسي</th>
                                    <th>رقم الهاتف</th>
                                    <th>تاريخ التوظيف</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${reportData.data.map(employee => `
                                    <tr>
                                        <td><strong>${employee.name}</strong></td>
                                        <td>${employee.position || 'غير محدد'}</td>
                                        <td>${employee.department || 'غير محدد'}</td>
                                        <td>${Utils.formatCurrency(employee.baseSalary || 0)}</td>
                                        <td>${employee.phone || 'غير محدد'}</td>
                                        <td>${Utils.formatDate(employee.createdAt, 'DD/MM/YYYY')}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="report-actions">
                    <button class="btn btn-primary" onclick="Reports.saveReport('employees', '${JSON.stringify(reportData).replace(/"/g, '&quot;')}')">
                        <i class="fas fa-save"></i>
                        حفظ التقرير
                    </button>
                    <button class="btn btn-success" onclick="Reports.exportReportPDF('employees', '${JSON.stringify(reportData).replace(/"/g, '&quot;')}')">
                        <i class="fas fa-file-pdf"></i>
                        تصدير PDF
                    </button>
                </div>
            </div>
        `;

        displayArea.scrollIntoView({ behavior: 'smooth' });
    }

    // عرض تقرير المخزون
    static displayInventoryReport(reportData) {
        const displayArea = document.getElementById('reports-display');
        if (!displayArea) return;

        displayArea.innerHTML = `
            <div class="report-display">
                <div class="report-header">
                    <h2><i class="fas fa-boxes"></i> ${reportData.config.title}</h2>
                    <div class="report-meta">
                        <span><i class="fas fa-calendar"></i> ${Utils.formatDate(new Date(), 'DD/MM/YYYY HH:mm')}</span>
                    </div>
                </div>

                <div class="report-summary">
                    <h3><i class="fas fa-chart-pie"></i> ملخص المخزون</h3>
                    <div class="summary-cards">
                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <div class="card-content">
                                <h4>إجمالي العناصر</h4>
                                <span class="card-value">${reportData.summary.totalItems}</span>
                            </div>
                        </div>
                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="card-content">
                                <h4>القيمة الإجمالية</h4>
                                <span class="card-value">${Utils.formatCurrency(reportData.summary.totalValue)}</span>
                            </div>
                        </div>
                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="card-content">
                                <h4>مخزون منخفض</h4>
                                <span class="card-value">${reportData.summary.lowStockItems}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="report-actions">
                    <button class="btn btn-primary" onclick="Reports.saveReport('inventory', '${JSON.stringify(reportData).replace(/"/g, '&quot;')}')">
                        <i class="fas fa-save"></i>
                        حفظ التقرير
                    </button>
                    <button class="btn btn-success" onclick="Reports.exportReportPDF('inventory', '${JSON.stringify(reportData).replace(/"/g, '&quot;')}')">
                        <i class="fas fa-file-pdf"></i>
                        تصدير PDF
                    </button>
                </div>
            </div>
        `;

        displayArea.scrollIntoView({ behavior: 'smooth' });
    }

    // عرض تقرير الأداء
    static displayPerformanceReport(reportData) {
        const displayArea = document.getElementById('reports-display');
        if (!displayArea) return;

        displayArea.innerHTML = `
            <div class="report-display">
                <div class="report-header">
                    <h2><i class="fas fa-chart-pie"></i> ${reportData.config.title}</h2>
                    <div class="report-meta">
                        <span><i class="fas fa-calendar"></i> ${Utils.formatDate(new Date(), 'DD/MM/YYYY HH:mm')}</span>
                    </div>
                </div>

                <div class="report-summary">
                    <h3><i class="fas fa-chart-line"></i> مؤشرات الأداء</h3>
                    <div class="summary-cards">
                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="card-content">
                                <h4>أعمال هذا الشهر</h4>
                                <span class="card-value">${reportData.summary.monthlyWorks}</span>
                            </div>
                        </div>
                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-money-bill"></i>
                            </div>
                            <div class="card-content">
                                <h4>إيرادات الشهر</h4>
                                <span class="card-value">${Utils.formatCurrency(reportData.summary.monthlyRevenue)}</span>
                            </div>
                        </div>
                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="card-content">
                                <h4>معدل الإنجاز</h4>
                                <span class="card-value">${reportData.summary.completionRate.toFixed(1)}%</span>
                            </div>
                        </div>
                        <div class="summary-card">
                            <div class="card-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="card-content">
                                <h4>متوسط وقت الإنجاز</h4>
                                <span class="card-value">${reportData.summary.averageCompletionTime.toFixed(1)} يوم</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="report-actions">
                    <button class="btn btn-primary" onclick="Reports.saveReport('performance', '${JSON.stringify(reportData).replace(/"/g, '&quot;')}')">
                        <i class="fas fa-save"></i>
                        حفظ التقرير
                    </button>
                    <button class="btn btn-success" onclick="Reports.exportReportPDF('performance', '${JSON.stringify(reportData).replace(/"/g, '&quot;')}')">
                        <i class="fas fa-file-pdf"></i>
                        تصدير PDF
                    </button>
                </div>
            </div>
        `;

        displayArea.scrollIntoView({ behavior: 'smooth' });
    }

    // تقرير مالي
    static generateFinancialReport(config) {
        Utils.showNotification('تقرير مالي - قيد التطوير', 'info');
    }

    // تقرير الأطباء
    static generateDoctorsReport(config) {
        Utils.showNotification('تقرير الأطباء - قيد التطوير', 'info');
    }

    // تقرير الموظفين
    static generateEmployeesReport(config) {
        Utils.showNotification('تقرير الموظفين - قيد التطوير', 'info');
    }

    // تقرير المخزون
    static generateInventoryReport(config) {
        Utils.showNotification('تقرير المخزون - قيد التطوير', 'info');
    }

    // تقرير الأداء
    static generatePerformanceReport(config) {
        Utils.showNotification('تقرير الأداء - قيد التطوير', 'info');
    }
}

// تسجيل اكتمال تحميل الملف
console.log('✅ تم تحميل ملف reports.js بالكامل');

// التأكد من أن الكلاس متاح عالمياً
window.Reports = Reports;
