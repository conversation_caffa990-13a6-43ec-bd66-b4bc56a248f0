// إدارة الوضع النهاري والليلي
class ThemeManager {
    static currentTheme = 'light';
    static isInitialized = false;

    // تهيئة مدير الوضع
    static init() {
        if (this.isInitialized) return;
        
        this.loadSavedTheme();
        this.setupEventListeners();
        this.updateThemeIcon();
        this.isInitialized = true;
    }

    // تحميل الوضع المحفوظ
    static loadSavedTheme() {
        const savedTheme = localStorage.getItem('dentalLab_theme') || 'light';
        this.setTheme(savedTheme, false);
    }

    // حفظ الوضع
    static saveTheme(theme) {
        localStorage.setItem('dentalLab_theme', theme);
    }

    // تعيين الوضع
    static setTheme(theme, save = true) {
        this.currentTheme = theme;
        
        // تطبيق الوضع على الصفحة
        document.documentElement.setAttribute('data-theme', theme);
        
        // تحديث أيقونة الزر
        this.updateThemeIcon();
        
        // حفظ الوضع
        if (save) {
            this.saveTheme(theme);
        }
        
        // إضافة تأثير الانتقال
        this.addTransitionEffect();
        
        // إرسال حدث تغيير الوضع
        this.dispatchThemeChangeEvent(theme);
    }

    // تبديل الوضع
    static toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
        
        // إضافة تأثير بصري للزر
        this.animateThemeButton();
        
        // إظهار إشعار
        this.showThemeNotification(newTheme);
    }

    // تحديث أيقونة الوضع
    static updateThemeIcon() {
        const themeButton = document.getElementById('themeToggle');
        const themeIcon = themeButton?.querySelector('.theme-icon');
        
        if (!themeIcon) return;

        if (this.currentTheme === 'dark') {
            themeIcon.className = 'fas fa-moon theme-icon';
            themeButton.classList.add('dark-mode');
            themeButton.title = 'تغيير للوضع النهاري';
        } else {
            themeIcon.className = 'fas fa-sun theme-icon';
            themeButton.classList.remove('dark-mode');
            themeButton.title = 'تغيير للوضع الليلي';
        }
    }

    // إضافة تأثير الانتقال
    static addTransitionEffect() {
        // إضافة فئة انتقال مؤقتة
        document.body.classList.add('theme-transitioning');
        
        // إزالة الفئة بعد انتهاء الانتقال
        setTimeout(() => {
            document.body.classList.remove('theme-transitioning');
        }, 500);
    }

    // تحريك زر الوضع
    static animateThemeButton() {
        const themeButton = document.getElementById('themeToggle');
        if (!themeButton) return;

        // إضافة فئة الحركة
        themeButton.classList.add('theme-switching');
        
        // إزالة الفئة بعد انتهاء الحركة
        setTimeout(() => {
            themeButton.classList.remove('theme-switching');
        }, 600);
    }

    // إظهار إشعار تغيير الوضع
    static showThemeNotification(theme) {
        const message = theme === 'dark' ? 'تم التبديل للوضع الليلي' : 'تم التبديل للوضع النهاري';
        const icon = theme === 'dark' ? 'moon' : 'sun';
        
        if (typeof Utils !== 'undefined' && Utils.showNotification) {
            Utils.showNotification(message, 'info');
        }
    }

    // إرسال حدث تغيير الوضع
    static dispatchThemeChangeEvent(theme) {
        const event = new CustomEvent('themeChanged', {
            detail: { theme: theme }
        });
        document.dispatchEvent(event);
    }

    // إعداد مستمعي الأحداث
    static setupEventListeners() {
        // زر تغيير الوضع
        const themeButton = document.getElementById('themeToggle');
        if (themeButton) {
            themeButton.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // اختصار لوحة المفاتيح (Ctrl + Shift + T)
        document.addEventListener('keydown', (event) => {
            if (event.ctrlKey && event.shiftKey && event.key === 'T') {
                event.preventDefault();
                this.toggleTheme();
            }
        });

        // مراقبة تفضيلات النظام
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addEventListener('change', (e) => {
                // تطبيق تفضيلات النظام فقط إذا لم يكن هناك إعداد محفوظ
                if (!localStorage.getItem('dentalLab_theme')) {
                    this.setTheme(e.matches ? 'dark' : 'light');
                }
            });
        }

        // مراقبة تحديث بيانات المعمل
        document.addEventListener('labInfoUpdated', (event) => {
            console.log('تم استقبال حدث تحديث بيانات المعمل');
            this.updateLabInfo();
        });
    }

    // الحصول على الوضع الحالي
    static getCurrentTheme() {
        return this.currentTheme;
    }

    // التحقق من الوضع المظلم
    static isDarkMode() {
        return this.currentTheme === 'dark';
    }

    // إعادة تعيين للوضع الافتراضي
    static resetToDefault() {
        this.setTheme('light');
        localStorage.removeItem('dentalLab_theme');
    }

    // تطبيق وضع النظام
    static applySystemTheme() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            this.setTheme('dark');
        } else {
            this.setTheme('light');
        }
    }

    // تحديث معلومات المعمل
    static updateLabInfo() {
        const settings = Database?.getSettings() || {};

        // تحديث اسم المعمل في جميع الأماكن
        const labNameElements = document.querySelectorAll('#labName, .lab-name');
        labNameElements.forEach(element => {
            if (element) {
                element.textContent = settings.labName || 'معمل الأسنان المتخصص';
            }
        });

        // تحديث العنوان الفرعي في جميع الأماكن
        const labSubtitleElements = document.querySelectorAll('#labSubtitle, .lab-subtitle');
        labSubtitleElements.forEach(element => {
            if (element) {
                element.textContent = settings.labSubtitle || 'نظام الإدارة المتطور';
            }
        });

        // ملاحظة: تم إزالة تحديث اسم المعمل من الشريط الجانبي
        // لأن الشريط الجانبي الآن يعرض "القائمة الرئيسية" فقط

        // تحديث الشعار إذا كان موجوداً
        if (settings.labLogo) {
            const logoElements = document.querySelectorAll('.lab-logo');
            logoElements.forEach(logo => {
                if (logo) {
                    logo.innerHTML = `<img src="${settings.labLogo}" alt="شعار المعمل" style="width: 100%; height: 100%; object-fit: cover; border-radius: inherit;">`;
                }
            });
        }

        console.log('تم تحديث معلومات المعمل عبر ThemeManager');
    }

    // تنظيف الموارد
    static cleanup() {
        this.isInitialized = false;
    }

    // إعادة تهيئة
    static reinitialize() {
        this.cleanup();
        setTimeout(() => {
            this.init();
        }, 100);
    }
}

// إضافة أنماط CSS للانتقالات
const themeStyles = document.createElement('style');
themeStyles.textContent = `
    /* تأثيرات انتقال الوضع */
    .theme-transitioning * {
        transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease !important;
    }
    
    /* تحريك زر الوضع */
    .theme-switching {
        animation: themeSwitch 0.6s ease-in-out;
    }
    
    @keyframes themeSwitch {
        0% { transform: scale(1) rotate(0deg); }
        25% { transform: scale(1.2) rotate(90deg); }
        50% { transform: scale(1.1) rotate(180deg); }
        75% { transform: scale(1.2) rotate(270deg); }
        100% { transform: scale(1) rotate(360deg); }
    }
    
    /* تأثيرات إضافية للوضع المظلم */
    [data-theme="dark"] .lab-logo {
        box-shadow: 0 0 20px rgba(124, 58, 237, 0.3);
    }
    
    [data-theme="dark"] .theme-toggle:hover {
        box-shadow: 0 0 15px rgba(236, 72, 153, 0.4);
    }
    
    /* تحسينات الوضع المظلم للشريط السفلي */
    [data-theme="dark"] .bottom-bar {
        background: linear-gradient(135deg, var(--bg-secondary), var(--bg-dark));
        border-top-color: var(--border-color);
    }
    
    /* تحسينات الوضع المظلم للشريط الجانبي */
    [data-theme="dark"] #sidebar {
        background: linear-gradient(180deg, var(--bg-secondary), var(--bg-dark));
    }
    
    [data-theme="dark"] .sidebar-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    }
`;

document.head.appendChild(themeStyles);

// تهيئة تلقائية عند تحميل الصفحة (تم نقلها إلى App.init)
// document.addEventListener('DOMContentLoaded', () => {
//     ThemeManager.init();
//
//     // تحديث معلومات المعمل بعد تحميل قاعدة البيانات
//     setTimeout(() => {
//         ThemeManager.updateLabInfo();
//     }, 500);
// });

// تنظيف عند إغلاق الصفحة
window.addEventListener('beforeunload', () => {
    ThemeManager.cleanup();
});
