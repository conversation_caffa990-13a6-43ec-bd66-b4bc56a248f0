/* تنسيق صفحة الإعدادات */

/* تنسيق الجداول */
.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* تنسيق إدارة الأدوار */
.roles-management {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.role-card {
    background: white;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.role-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.role-header {
    padding: 20px;
    background: #f8fafc;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.role-info h4 {
    margin: 0 0 5px 0;
    color: #2d3748;
    font-size: 18px;
    font-weight: 600;
}

.role-info p {
    margin: 0;
    color: #718096;
    font-size: 14px;
}

.role-actions .btn {
    padding: 6px 12px;
    font-size: 12px;
}

.role-permissions {
    padding: 20px;
}

.role-permissions h5 {
    margin: 0 0 15px 0;
    color: #4a5568;
    font-size: 14px;
    font-weight: 600;
}

.permissions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.permission-badge {
    background: #edf2f7;
    color: #4a5568;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid #e2e8f0;
}

.permission-badge:first-child {
    background: #fed7d7;
    color: #742a2a;
    border-color: #feb2b2;
}

/* إحصائيات المستخدمين */
.users-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    background: #4299e1;
}

.stat-icon.active {
    background: #48bb78;
}

.stat-icon.admin {
    background: #ed8936;
}

.stat-icon.online {
    background: #38b2ac;
}

.stat-info {
    flex: 1;
}

.stat-value {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    color: #718096;
    font-weight: 500;
}

/* فلاتر المستخدمين */
.users-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    font-size: 14px;
    font-weight: 500;
    color: #4a5568;
}

.filter-group select,
.filter-group input {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    transition: all 0.3s ease;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

/* جدول المستخدمين */
.users-table-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #e2e8f0;
}

.data-table th {
    background: #f8fafc;
    padding: 15px 12px;
    text-align: right;
    font-weight: 600;
    color: #4a5568;
    border-bottom: 1px solid #e2e8f0;
    font-size: 14px;
}

.data-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #f1f5f9;
    color: #2d3748;
    font-size: 14px;
}

.data-table tr:hover {
    background: #f8fafc;
}

.data-table tr:last-child td {
    border-bottom: none;
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.action-buttons .btn {
    padding: 6px 10px;
    font-size: 12px;
    min-width: auto;
}

/* صورة المستخدم */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e2e8f0;
}

.user-avatar-placeholder {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #edf2f7;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #718096;
    font-weight: 600;
    font-size: 16px;
    border: 2px solid #e2e8f0;
}

/* حالة المستخدم */
.user-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.user-status.active {
    background: #c6f6d5;
    color: #22543d;
}

.user-status.inactive {
    background: #fed7d7;
    color: #742a2a;
}

/* دور المستخدم */
.user-role {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    background: #edf2f7;
    color: #4a5568;
}

.user-role.admin {
    background: #fed7d7;
    color: #742a2a;
}

.user-role.manager {
    background: #dbeafe;
    color: #1e40af;
}

.user-role.technician {
    background: #d1fae5;
    color: #065f46;
}

/* صلاحيات المستخدم */
.user-permissions {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    max-width: 150px;
}

.user-permissions .permission-badge {
    font-size: 10px;
    padding: 2px 6px;
}

/* تنسيق متجاوب */
@media (max-width: 768px) {
    .users-stats {
        grid-template-columns: 1fr;
    }
    
    .users-filters {
        grid-template-columns: 1fr;
    }
    
    .roles-management {
        grid-template-columns: 1fr;
    }
    
    .data-table {
        font-size: 12px;
    }
    
    .data-table th,
    .data-table td {
        padding: 10px 8px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 4px;
    }
    
    .action-buttons .btn {
        width: 100%;
        font-size: 11px;
    }
}
