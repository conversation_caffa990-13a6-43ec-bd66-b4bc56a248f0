// إدارة شاشة تسجيل الدخول
console.log('🔄 بدء تحميل ملف login-manager.js...');

class LoginManager {
    // وظيفة إظهار/إخفاء كلمة المرور
    static togglePassword() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.querySelector('.toggle-password');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
            toggleIcon.classList.add('active');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
            toggleIcon.classList.remove('active');
        }
    }

    // تحديث شاشة تسجيل الدخول بناءً على الإعدادات
    static updateLoginScreen() {
        try {
            console.log('🔄 بدء تحديث شاشة تسجيل الدخول...');

            // الحصول على الإعدادات من localStorage أولاً
            let settings = {};
            const labSettingsStr = localStorage.getItem('labSettings');
            console.log('📦 البيانات الخام من localStorage:', labSettingsStr);

            if (labSettingsStr) {
                settings = JSON.parse(labSettingsStr);
                console.log('⚙️ تم تحميل الإعدادات من localStorage:', settings);
            } else {
                // إذا لم توجد في localStorage، محاولة التحميل من قاعدة البيانات
                if (typeof Database !== 'undefined' && Database.getSettings) {
                    settings = Database.getSettings();
                    console.log('⚙️ تم تحميل الإعدادات من قاعدة البيانات:', settings);
                    // حفظ في localStorage للمرة القادمة
                    localStorage.setItem('labSettings', JSON.stringify(settings));
                } else {
                    console.log('⚠️ لا توجد إعدادات محفوظة، استخدام القيم الافتراضية');
                }
            }

            // التحقق من وجود شاشة تسجيل الدخول
            const loginScreen = document.getElementById('loginScreen');
            if (!loginScreen) {
                console.log('❌ شاشة تسجيل الدخول غير موجودة');
                return;
            }

            const isLoginVisible = loginScreen.style.display !== 'none' &&
                                 !loginScreen.classList.contains('hidden');
            console.log('👁️ شاشة تسجيل الدخول مرئية:', isLoginVisible);

            // تحديث عنوان النظام
            const systemTitle = document.getElementById('loginSystemTitle');
            console.log('🏷️ عنصر العنوان موجود:', !!systemTitle);

            if (systemTitle) {
                const labName = settings.labName || 'معمل الأسنان المتخصص';
                const newTitle = `نظام إدارة ${labName}`;
                systemTitle.textContent = newTitle;
                console.log('✅ تم تحديث العنوان إلى:', newTitle);
            }

            // تحديث الشعار
            const loginLogo = document.getElementById('loginLogo');
            console.log('🖼️ عنصر الشعار موجود:', !!loginLogo);

            if (loginLogo) {
                if (settings.labLogo) {
                    loginLogo.innerHTML = `<img src="${settings.labLogo}" alt="شعار المعمل" style="width: 100%; height: 100%; object-fit: cover; border-radius: inherit;">`;
                    console.log('✅ تم تحديث الشعار بصورة مخصصة');
                } else {
                    loginLogo.innerHTML = '<i class="fas fa-tooth"></i>';
                    console.log('✅ تم تعيين الشعار الافتراضي');
                }
            }

            console.log('🎉 تم تحديث شاشة تسجيل الدخول بنجاح');

        } catch (error) {
            console.error('❌ خطأ في تحديث شاشة تسجيل الدخول:', error);
        }
    }

    // إعداد مستمعي الأحداث
    static setupEventListeners() {
        // تحديث شاشة تسجيل الدخول عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تحديث فوري
            LoginManager.updateLoginScreen();

            // تحديث دوري كل 5 ثوان للتأكد
            setInterval(function() {
                const loginScreen = document.getElementById('loginScreen');
                if (loginScreen && loginScreen.style.display !== 'none') {
                    LoginManager.updateLoginScreen();
                }
            }, 5000);

            // مراقبة تغييرات الإعدادات
            window.addEventListener('storage', function(e) {
                if (e.key === 'labSettings') {
                    LoginManager.updateLoginScreen();
                }
            });

            // مراقبة أحداث تحديث بيانات المعمل
            document.addEventListener('labInfoUpdated', function(e) {
                console.log('تم استقبال حدث تحديث بيانات المعمل:', e.detail);
                LoginManager.updateLoginScreen();
            });

            // مراقبة أحداث تحديث شاشة تسجيل الدخول
            document.addEventListener('loginScreenUpdate', function(e) {
                console.log('تم استقبال حدث تحديث شاشة تسجيل الدخول:', e.detail);
                LoginManager.updateLoginScreen();
            });

            // مراقبة أحداث فرض التحديث
            document.addEventListener('forceLoginUpdate', function(e) {
                console.log('🔥 تم استقبال حدث فرض تحديث شاشة تسجيل الدخول:', e.detail);
                LoginManager.updateLoginScreen();
            });

            // مراقبة تحديث اسم المعمل في شاشة تسجيل الدخول
            document.addEventListener('loginLabNameUpdated', function(e) {
                console.log('🏷️ تم استقبال حدث تحديث اسم المعمل:', e.detail);
                const systemTitle = document.getElementById('loginSystemTitle');
                if (systemTitle && e.detail.labName) {
                    systemTitle.textContent = `نظام إدارة ${e.detail.labName}`;
                    console.log('✅ تم تحديث اسم المعمل في شاشة تسجيل الدخول');
                }
            });

            // مراقبة تحديث الشعار في شاشة تسجيل الدخول
            document.addEventListener('loginLogoUpdated', function(e) {
                console.log('🖼️ تم استقبال حدث تحديث الشعار:', e.detail);
                const loginLogo = document.getElementById('loginLogo');
                if (loginLogo) {
                    if (e.detail.logoData) {
                        loginLogo.innerHTML = `<img src="${e.detail.logoData}" alt="شعار المعمل" style="width: 100%; height: 100%; object-fit: cover; border-radius: inherit;">`;
                        console.log('✅ تم تحديث الشعار في شاشة تسجيل الدخول');
                    } else {
                        loginLogo.innerHTML = '<i class="fas fa-tooth"></i>';
                        console.log('✅ تم إعادة تعيين الشعار الافتراضي في شاشة تسجيل الدخول');
                    }
                }
            });
        });
    }

    // إعداد وظيفة الخروج من البرنامج
    static setupLogoutHandler() {
        document.addEventListener('DOMContentLoaded', function() {
            const logoutFromSidebar = document.getElementById('logoutFromSidebar');

            if (logoutFromSidebar) {
                logoutFromSidebar.addEventListener('click', function(e) {
                    e.preventDefault();

                    // عرض رسالة تأكيد
                    if (confirm('هل أنت متأكد من الخروج من البرنامج؟')) {
                        // تنظيف البيانات المحلية
                        localStorage.removeItem('currentUser');
                        localStorage.removeItem('isLoggedIn');
                        localStorage.removeItem('userRole');

                        // إغلاق البرنامج فوري
                        try {
                            // محاولة إغلاق النافذة
                            window.close();

                            // إذا لم تنجح، محاولة إغلاق التطبيق (للتطبيقات المحلية)
                            if (typeof window.electronAPI !== 'undefined') {
                                window.electronAPI.closeApp();
                            }

                        } catch (error) {
                            console.log('خطأ في إغلاق البرنامج:', error);
                        }
                    }
                });
            }
        });
    }

    // تهيئة إدارة تسجيل الدخول
    static init() {
        this.setupEventListeners();
        this.setupLogoutHandler();
        
        // جعل الوظائف متاحة عالمياً للتوافق مع الكود الموجود
        window.togglePassword = this.togglePassword;
        window.updateLoginScreen = this.updateLoginScreen;
        
        // إعداد نسخ بيانات تسجيل الدخول
        this.setupCredentialsCopy();

        console.log('✅ تم تهيئة إدارة تسجيل الدخول بنجاح');
    }

    // إعداد نسخ بيانات تسجيل الدخول
    static setupCredentialsCopy() {
        // إضافة مستمع أحداث لنسخ بيانات تسجيل الدخول
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('credential-value')) {
                // تحديد نوع البيانات
                const credentialItem = e.target.closest('.credential-item');
                const label = credentialItem.querySelector('.credential-label').textContent;

                if (label.includes('اسم المستخدم')) {
                    this.fillUsername(e.target.textContent);
                } else if (label.includes('كلمة المرور')) {
                    this.fillPassword(e.target.textContent);
                }

                // نسخ النص أيضاً
                this.copyCredential(e.target);
            }
        });
    }

    // ملء اسم المستخدم
    static fillUsername(username) {
        const usernameInput = document.getElementById('username');
        if (usernameInput) {
            usernameInput.value = username;
            usernameInput.focus();

            // إضافة تأثير بصري
            usernameInput.style.borderColor = '#10b981';
            setTimeout(() => {
                usernameInput.style.borderColor = '';
            }, 2000);
        }
    }

    // ملء كلمة المرور
    static fillPassword(password) {
        const passwordInput = document.getElementById('password');
        if (passwordInput) {
            passwordInput.value = password;

            // إضافة تأثير بصري
            passwordInput.style.borderColor = '#10b981';
            setTimeout(() => {
                passwordInput.style.borderColor = '';
            }, 2000);
        }
    }

    // ملء البيانات تلقائياً
    static autoFillCredentials() {
        this.fillUsername('dentallab');
        this.fillPassword('dental2024');

        // إظهار رسالة تأكيد
        const button = document.querySelector('.auto-fill-btn');
        if (button) {
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i> تم الملء';
            button.style.background = 'linear-gradient(135deg, #10b981, #059669)';

            setTimeout(() => {
                button.innerHTML = originalText;
                button.style.background = '';
            }, 2000);
        }

        console.log('✅ تم ملء بيانات تسجيل الدخول تلقائياً');
    }

    // نسخ بيانات تسجيل الدخول
    static async copyCredential(element) {
        try {
            const text = element.textContent;

            // نسخ النص إلى الحافظة
            if (navigator.clipboard && navigator.clipboard.writeText) {
                await navigator.clipboard.writeText(text);
            } else {
                // طريقة بديلة للمتصفحات القديمة
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
            }

            // إظهار تأثير بصري
            const originalText = element.textContent;
            element.textContent = '✓ تم النسخ';
            element.style.background = '#10b981';
            element.style.color = 'white';

            // إعادة النص الأصلي بعد ثانيتين
            setTimeout(() => {
                element.textContent = originalText;
                element.style.background = '';
                element.style.color = '';
            }, 2000);

            console.log('✅ تم نسخ:', text);

        } catch (error) {
            console.error('❌ خطأ في نسخ النص:', error);

            // إظهار رسالة خطأ
            element.style.background = '#ef4444';
            element.style.color = 'white';
            const originalText = element.textContent;
            element.textContent = '✗ فشل النسخ';

            setTimeout(() => {
                element.textContent = originalText;
                element.style.background = '';
                element.style.color = '';
            }, 2000);
        }
    }
}

// تهيئة إدارة تسجيل الدخول
LoginManager.init();

// تسجيل اكتمال تحميل الملف
console.log('✅ تم تحميل ملف login-manager.js بالكامل');

// التأكد من أن الكلاس متاح عالمياً
window.LoginManager = LoginManager;
