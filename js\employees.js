// إدارة الموظفين
class Employees {
    static currentEmployees = [];
    static filteredEmployees = [];
    static currentPage = 1;
    static pageSize = 10;
    static searchQuery = '';

    // عرض صفحة الموظفين
    static render() {
        const pageContent = document.getElementById('pageContent');
        
        pageContent.innerHTML = `
            <div class="employees-container">
                <!-- عنوان الإدارة المطور -->
                <div class="department-header employees">
                    <div class="department-header-content">
                        <div class="department-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="department-info">
                            <h1 class="department-name" data-text="إدارة الموظفين">إدارة الموظفين</h1>
                            <p class="department-description">إدارة شاملة للموظفين وحساب المرتبات والعمولات</p>
                            <div class="department-stats">
                                <div class="department-stat">
                                    <span class="department-stat-value" id="total-employees-count">0</span>
                                    <span class="department-stat-label">إجمالي الموظفين</span>
                                </div>
                                <div class="department-stat">
                                    <span class="department-stat-value" id="active-employees-count">0</span>
                                    <span class="department-stat-label">نشطين</span>
                                </div>
                                <div class="department-stat">
                                    <span class="department-stat-value" id="total-salaries-count">0</span>
                                    <span class="department-stat-label">إجمالي المرتبات</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="page-actions-container">
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="Employees.showAddModal()">
                            <i class="fas fa-plus"></i>
                            إضافة موظف جديد
                        </button>
                        <button class="btn btn-warning" onclick="Employees.calculateSalaries()">
                            <i class="fas fa-calculator"></i>
                            حساب المرتبات
                        </button>
                        <button class="btn btn-success" onclick="Employees.exportData()">
                            <i class="fas fa-download"></i>
                            تصدير البيانات
                        </button>
                    </div>
                </div>

                <!-- أدوات البحث -->
                <div class="filters-container">
                    <div class="search-box">
                        <input type="search" id="employees-search" placeholder="البحث في الموظفين..." 
                               value="${this.searchQuery}">
                        <i class="fas fa-search"></i>
                    </div>
                    
                    <button class="btn btn-secondary" onclick="Employees.resetFilters()">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </button>
                </div>

                <!-- جدول الموظفين -->
                <div class="table-container">
                    <div class="table-header">
                        <h3 class="table-title">قائمة الموظفين</h3>
                        <div class="table-info">
                            <span id="employees-count">0 موظف</span>
                        </div>
                    </div>
                    
                    <div class="table-wrapper">
                        <table class="data-table" id="employees-table">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>رقم الهاتف</th>
                                    <th>المسمى الوظيفي</th>
                                    <th>الراتب الأساسي</th>
                                    <th>نسبة العمولة</th>
                                    <th>تاريخ التوظيف</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="employees-tbody">
                                <!-- سيتم تحميل البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="table-pagination" id="employees-pagination">
                        <!-- سيتم تحميل التصفح هنا -->
                    </div>
                </div>
            </div>
        `;

        this.loadData();
        this.setupEventListeners();
    }

    // تحميل البيانات
    static loadData() {
        this.currentEmployees = Database.getEmployees();
        this.applyFilters();
        this.updateTable();
        this.updateStats();
    }

    // إعداد مستمعي الأحداث
    static setupEventListeners() {
        // البحث
        const searchInput = document.getElementById('employees-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchQuery = e.target.value;
                this.currentPage = 1;
                this.applyFilters();
                this.updateTable();
            });
        }
    }

    // تطبيق الفلاتر
    static applyFilters() {
        let filtered = [...this.currentEmployees];

        // فلتر البحث
        if (this.searchQuery) {
            const query = this.searchQuery.toLowerCase();
            filtered = filtered.filter(employee => 
                employee.name.toLowerCase().includes(query) ||
                employee.phone.toLowerCase().includes(query) ||
                employee.position?.toLowerCase().includes(query) ||
                employee.nationalId?.toLowerCase().includes(query)
            );
        }

        this.filteredEmployees = filtered;
    }

    // تحديث الجدول
    static updateTable() {
        const tbody = document.getElementById('employees-tbody');
        if (!tbody) return;

        // حساب البيانات للصفحة الحالية
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageData = this.filteredEmployees.slice(startIndex, endIndex);

        // إنشاء صفوف الجدول
        tbody.innerHTML = pageData.map(employee => `
            <tr onclick="Employees.viewDetails('${employee.id}')" class="table-row-clickable">
                <td>
                    <div class="employee-info">
                        <strong>${employee.name}</strong>
                        ${employee.nationalId ? `<div class="employee-id">رقم البطاقة: ${employee.nationalId}</div>` : ''}
                    </div>
                </td>
                <td>
                    <a href="tel:${employee.phone}" class="phone-link">
                        <i class="fas fa-phone"></i>
                        ${employee.phone}
                    </a>
                </td>
                <td>${employee.position || '-'}</td>
                <td>
                    <strong>${Utils.formatCurrency(employee.baseSalary || 0)}</strong>
                </td>
                <td>
                    <div class="commission-info">
                        <div>عام: ${employee.generalCommission || 0}%</div>
                        <div>بورسلين: ${employee.porcelainCommission || 0}%</div>
                        <div>زيركون: ${employee.zirconCommission || 0}%</div>
                    </div>
                </td>
                <td>${Utils.formatDate(employee.createdAt, 'DD/MM/YYYY')}</td>
                <td class="actions-cell" onclick="event.stopPropagation()">
                    <div class="action-buttons">
                        <button class="btn-icon btn-primary" onclick="Employees.editEmployee('${employee.id}')" 
                                title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon btn-warning" onclick="Employees.calculateEmployeeSalary('${employee.id}')" 
                                title="حساب المرتب">
                            <i class="fas fa-calculator"></i>
                        </button>
                        <button class="btn-icon btn-info" onclick="Employees.viewSalaryHistory('${employee.id}')" 
                                title="تاريخ المرتبات">
                            <i class="fas fa-history"></i>
                        </button>
                        <button class="btn-icon btn-danger" onclick="Employees.deleteEmployee('${employee.id}')" 
                                title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        // إذا لم توجد بيانات
        if (pageData.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="no-data">
                        <div class="no-data-message">
                            <i class="fas fa-users"></i>
                            <p>لا يوجد موظفون مطابقون للبحث</p>
                        </div>
                    </td>
                </tr>
            `;
        }

        this.updatePagination();
    }

    // تحديث التصفح
    static updatePagination() {
        const totalPages = Math.ceil(this.filteredEmployees.length / this.pageSize);
        const paginationContainer = document.getElementById('employees-pagination');
        
        if (!paginationContainer) return;

        let paginationHTML = `
            <div class="pagination-info">
                عرض ${((this.currentPage - 1) * this.pageSize) + 1} إلى 
                ${Math.min(this.currentPage * this.pageSize, this.filteredEmployees.length)} 
                من ${this.filteredEmployees.length} موظف
            </div>
            <div class="pagination-controls">
        `;

        // زر السابق
        if (this.currentPage > 1) {
            paginationHTML += `
                <button class="btn btn-secondary btn-sm" onclick="Employees.changePage(${this.currentPage - 1})">
                    <i class="fas fa-chevron-right"></i>
                    السابق
                </button>
            `;
        }

        // أرقام الصفحات
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === this.currentPage ? 'btn-primary' : 'btn-secondary';
            paginationHTML += `
                <button class="btn ${activeClass} btn-sm" onclick="Employees.changePage(${i})">
                    ${i}
                </button>
            `;
        }

        // زر التالي
        if (this.currentPage < totalPages) {
            paginationHTML += `
                <button class="btn btn-secondary btn-sm" onclick="Employees.changePage(${this.currentPage + 1})">
                    التالي
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;
        }

        paginationHTML += '</div>';
        paginationContainer.innerHTML = paginationHTML;
    }

    // تغيير الصفحة
    static changePage(page) {
        this.currentPage = page;
        this.updateTable();
    }

    // تحديث الإحصائيات
    static updateStats() {
        const countElement = document.getElementById('employees-count');
        if (countElement) {
            countElement.textContent = `${this.filteredEmployees.length} موظف`;
        }

        // تحديث إحصائيات العنوان
        this.updateDepartmentHeaderStats();
    }

    // تحديث إحصائيات عنوان الإدارة
    static updateDepartmentHeaderStats() {
        const allEmployees = this.currentEmployees;

        // حساب الإحصائيات
        const stats = {
            total: allEmployees.length,
            active: allEmployees.filter(e => e.baseSalary > 0).length,
            totalSalaries: allEmployees.reduce((sum, e) => sum + (e.baseSalary || 0), 0)
        };

        // تحديث العناصر
        const totalEmployeesCount = document.getElementById('total-employees-count');
        const activeEmployeesCount = document.getElementById('active-employees-count');
        const totalSalariesCount = document.getElementById('total-salaries-count');

        if (totalEmployeesCount) totalEmployeesCount.textContent = stats.total;
        if (activeEmployeesCount) activeEmployeesCount.textContent = stats.active;
        if (totalSalariesCount) totalSalariesCount.textContent = Utils.formatCurrency(stats.totalSalaries);
    }

    // إعادة تعيين الفلاتر
    static resetFilters() {
        this.searchQuery = '';
        this.currentPage = 1;

        const searchInput = document.getElementById('employees-search');
        if (searchInput) searchInput.value = '';

        this.applyFilters();
        this.updateTable();
    }

    // عرض نافذة إضافة موظف جديد
    static showAddModal() {
        Utils.createModal({
            title: 'إضافة موظف جديد',
            content: `
                <form id="add-employee-form" class="employee-form">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="employee-name">الاسم الكامل *</label>
                            <input type="text" id="employee-name" name="name" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="employee-phone">رقم الهاتف *</label>
                            <input type="tel" id="employee-phone" name="phone" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="employee-national-id">رقم البطاقة الشخصية</label>
                            <input type="text" id="employee-national-id" name="nationalId">
                        </div>
                        
                        <div class="form-group">
                            <label for="employee-position">المسمى الوظيفي</label>
                            <input type="text" id="employee-position" name="position" 
                                   placeholder="مثل: فني أسنان">
                        </div>
                        
                        <div class="form-group">
                            <label for="employee-base-salary">الراتب الأساسي</label>
                            <input type="number" id="employee-base-salary" name="baseSalary" 
                                   step="0.01" min="0">
                        </div>
                        
                        <div class="form-group">
                            <label for="employee-general-commission">نسبة العمولة العامة (%)</label>
                            <input type="number" id="employee-general-commission" name="generalCommission" 
                                   step="0.01" min="0" max="100">
                        </div>
                        
                        <div class="form-group">
                            <label for="employee-porcelain-commission">نسبة البورسلين (%)</label>
                            <input type="number" id="employee-porcelain-commission" name="porcelainCommission" 
                                   step="0.01" min="0" max="100">
                        </div>
                        
                        <div class="form-group">
                            <label for="employee-zircon-commission">نسبة الزيركون (%)</label>
                            <input type="number" id="employee-zircon-commission" name="zirconCommission" 
                                   step="0.01" min="0" max="100">
                        </div>
                        
                        <div class="form-group">
                            <label for="employee-orthodontic-commission">نسبة التقويم (%)</label>
                            <input type="number" id="employee-orthodontic-commission" name="orthodonticCommission" 
                                   step="0.01" min="0" max="100">
                        </div>
                        
                        <div class="form-group">
                            <label for="employee-removable-commission">نسبة الأطقم المتحركة (%)</label>
                            <input type="number" id="employee-removable-commission" name="removableCommission" 
                                   step="0.01" min="0" max="100">
                        </div>
                        
                        <div class="form-group">
                            <label for="employee-deduction-days">أيام الخصم</label>
                            <input type="number" id="employee-deduction-days" name="deductionDays" 
                                   min="0" max="31">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="employee-notes">ملاحظات</label>
                        <textarea id="employee-notes" name="notes" rows="3"></textarea>
                    </div>

                    <div class="modal-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ الموظف
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            `,
            size: 'large'
        });

        this.setupAddForm();
    }

    // إعداد نموذج الإضافة
    static setupAddForm() {
        const form = document.getElementById('add-employee-form');
        if (!form) return;

        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveEmployee();
        });
    }

    // حفظ الموظف
    static async saveEmployee() {
        const form = document.getElementById('add-employee-form');
        if (!form) return;

        const formData = new FormData(form);

        // التحقق من صحة البيانات
        const name = formData.get('name').trim();
        const phone = formData.get('phone').trim();

        if (!name) {
            Utils.showNotification('يرجى إدخال اسم الموظف', 'warning');
            return;
        }

        if (!phone) {
            Utils.showNotification('يرجى إدخال رقم الهاتف', 'warning');
            return;
        }

        if (!Utils.isValidPhone(phone)) {
            Utils.showNotification('رقم الهاتف غير صحيح', 'warning');
            return;
        }

        // التحقق من عدم تكرار رقم الهاتف
        const existingEmployee = this.currentEmployees.find(e => e.phone === phone);
        if (existingEmployee) {
            Utils.showNotification('رقم الهاتف مستخدم بالفعل', 'warning');
            return;
        }

        // جمع بيانات الموظف
        const employeeData = {
            name: name,
            phone: phone,
            nationalId: formData.get('nationalId')?.trim() || '',
            position: formData.get('position')?.trim() || '',
            baseSalary: parseFloat(formData.get('baseSalary')) || 0,
            generalCommission: parseFloat(formData.get('generalCommission')) || 0,
            porcelainCommission: parseFloat(formData.get('porcelainCommission')) || 0,
            zirconCommission: parseFloat(formData.get('zirconCommission')) || 0,
            orthodonticCommission: parseFloat(formData.get('orthodonticCommission')) || 0,
            removableCommission: parseFloat(formData.get('removableCommission')) || 0,
            deductionDays: parseInt(formData.get('deductionDays')) || 0,
            notes: formData.get('notes')?.trim() || ''
        };

        // حفظ في قاعدة البيانات
        const saved = Database.addEmployee(employeeData);

        if (saved) {
            Utils.showNotification('تم حفظ الموظف بنجاح', 'success');
            Utils.closeModal();
            this.loadData();
        } else {
            Utils.showNotification('فشل في حفظ الموظف', 'error');
        }
    }

    // عرض تفاصيل الموظف
    static viewDetails(employeeId) {
        const employee = Database.getEmployeeById(employeeId);
        if (!employee) {
            Utils.showNotification('الموظف غير موجود', 'error');
            return;
        }

        Utils.createModal({
            title: `تفاصيل الموظف - ${employee.name}`,
            content: `
                <div class="employee-details">
                    <div class="details-grid">
                        <div class="detail-item">
                            <label>الاسم:</label>
                            <span>${employee.name}</span>
                        </div>
                        <div class="detail-item">
                            <label>رقم الهاتف:</label>
                            <span>
                                <a href="tel:${employee.phone}" class="phone-link">
                                    <i class="fas fa-phone"></i>
                                    ${employee.phone}
                                </a>
                            </span>
                        </div>
                        ${employee.nationalId ? `
                            <div class="detail-item">
                                <label>رقم البطاقة الشخصية:</label>
                                <span>${employee.nationalId}</span>
                            </div>
                        ` : ''}
                        ${employee.position ? `
                            <div class="detail-item">
                                <label>المسمى الوظيفي:</label>
                                <span>${employee.position}</span>
                            </div>
                        ` : ''}
                        <div class="detail-item">
                            <label>الراتب الأساسي:</label>
                            <span>${Utils.formatCurrency(employee.baseSalary || 0)}</span>
                        </div>
                        <div class="detail-item">
                            <label>تاريخ التوظيف:</label>
                            <span>${Utils.formatDate(employee.createdAt, 'DD/MM/YYYY')}</span>
                        </div>
                    </div>

                    <!-- نسب العمولات -->
                    <div class="commission-section">
                        <h4>نسب العمولات</h4>
                        <div class="commission-grid">
                            <div class="commission-item">
                                <label>العمولة العامة:</label>
                                <span>${employee.generalCommission || 0}%</span>
                            </div>
                            <div class="commission-item">
                                <label>البورسلين:</label>
                                <span>${employee.porcelainCommission || 0}%</span>
                            </div>
                            <div class="commission-item">
                                <label>الزيركون:</label>
                                <span>${employee.zirconCommission || 0}%</span>
                            </div>
                            <div class="commission-item">
                                <label>التقويم:</label>
                                <span>${employee.orthodonticCommission || 0}%</span>
                            </div>
                            <div class="commission-item">
                                <label>الأطقم المتحركة:</label>
                                <span>${employee.removableCommission || 0}%</span>
                            </div>
                            <div class="commission-item">
                                <label>أيام الخصم:</label>
                                <span>${employee.deductionDays || 0} يوم</span>
                            </div>
                        </div>
                    </div>

                    ${employee.notes ? `
                        <div class="detail-section">
                            <label>الملاحظات:</label>
                            <div class="notes-content">${employee.notes}</div>
                        </div>
                    ` : ''}

                    <div class="modal-actions">
                        <button class="btn btn-primary" onclick="Employees.editEmployee('${employee.id}')">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </button>
                        <button class="btn btn-warning" onclick="Employees.calculateEmployeeSalary('${employee.id}')">
                            <i class="fas fa-calculator"></i>
                            حساب المرتب
                        </button>
                        <button class="btn btn-info" onclick="Employees.viewSalaryHistory('${employee.id}')">
                            <i class="fas fa-history"></i>
                            تاريخ المرتبات
                        </button>
                        <button class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            `,
            size: 'large'
        });
    }

    // تعديل الموظف
    static editEmployee(employeeId) {
        const employee = Database.getEmployeeById(employeeId);
        if (!employee) {
            Utils.showNotification('الموظف غير موجود', 'error');
            return;
        }

        Utils.closeModal(); // إغلاق النافذة الحالية

        Utils.createModal({
            title: `تعديل الموظف - ${employee.name}`,
            content: `
                <form id="edit-employee-form" class="employee-form">
                    <input type="hidden" name="employeeId" value="${employee.id}">

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="edit-employee-name">الاسم الكامل *</label>
                            <input type="text" id="edit-employee-name" name="name" value="${employee.name}" required>
                        </div>

                        <div class="form-group">
                            <label for="edit-employee-phone">رقم الهاتف *</label>
                            <input type="tel" id="edit-employee-phone" name="phone" value="${employee.phone}" required>
                        </div>

                        <div class="form-group">
                            <label for="edit-employee-national-id">رقم البطاقة الشخصية</label>
                            <input type="text" id="edit-employee-national-id" name="nationalId" value="${employee.nationalId || ''}">
                        </div>

                        <div class="form-group">
                            <label for="edit-employee-position">المسمى الوظيفي</label>
                            <input type="text" id="edit-employee-position" name="position" value="${employee.position || ''}">
                        </div>

                        <div class="form-group">
                            <label for="edit-employee-base-salary">الراتب الأساسي</label>
                            <input type="number" id="edit-employee-base-salary" name="baseSalary"
                                   value="${employee.baseSalary || 0}" step="0.01" min="0">
                        </div>

                        <div class="form-group">
                            <label for="edit-employee-general-commission">نسبة العمولة العامة (%)</label>
                            <input type="number" id="edit-employee-general-commission" name="generalCommission"
                                   value="${employee.generalCommission || 0}" step="0.01" min="0" max="100">
                        </div>

                        <div class="form-group">
                            <label for="edit-employee-porcelain-commission">نسبة البورسلين (%)</label>
                            <input type="number" id="edit-employee-porcelain-commission" name="porcelainCommission"
                                   value="${employee.porcelainCommission || 0}" step="0.01" min="0" max="100">
                        </div>

                        <div class="form-group">
                            <label for="edit-employee-zircon-commission">نسبة الزيركون (%)</label>
                            <input type="number" id="edit-employee-zircon-commission" name="zirconCommission"
                                   value="${employee.zirconCommission || 0}" step="0.01" min="0" max="100">
                        </div>

                        <div class="form-group">
                            <label for="edit-employee-orthodontic-commission">نسبة التقويم (%)</label>
                            <input type="number" id="edit-employee-orthodontic-commission" name="orthodonticCommission"
                                   value="${employee.orthodonticCommission || 0}" step="0.01" min="0" max="100">
                        </div>

                        <div class="form-group">
                            <label for="edit-employee-removable-commission">نسبة الأطقم المتحركة (%)</label>
                            <input type="number" id="edit-employee-removable-commission" name="removableCommission"
                                   value="${employee.removableCommission || 0}" step="0.01" min="0" max="100">
                        </div>

                        <div class="form-group">
                            <label for="edit-employee-deduction-days">أيام الخصم</label>
                            <input type="number" id="edit-employee-deduction-days" name="deductionDays"
                                   value="${employee.deductionDays || 0}" min="0" max="31">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit-employee-notes">ملاحظات</label>
                        <textarea id="edit-employee-notes" name="notes" rows="3">${employee.notes || ''}</textarea>
                    </div>

                    <div class="modal-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ التغييرات
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            `,
            size: 'large'
        });

        // إعداد نموذج التعديل
        const form = document.getElementById('edit-employee-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.updateEmployee();
            });
        }
    }

    // تحديث الموظف
    static async updateEmployee() {
        const form = document.getElementById('edit-employee-form');
        if (!form) return;

        const formData = new FormData(form);
        const employeeId = formData.get('employeeId');

        // التحقق من صحة البيانات
        const name = formData.get('name').trim();
        const phone = formData.get('phone').trim();

        if (!name) {
            Utils.showNotification('يرجى إدخال اسم الموظف', 'warning');
            return;
        }

        if (!phone) {
            Utils.showNotification('يرجى إدخال رقم الهاتف', 'warning');
            return;
        }

        if (!Utils.isValidPhone(phone)) {
            Utils.showNotification('رقم الهاتف غير صحيح', 'warning');
            return;
        }

        // التحقق من عدم تكرار رقم الهاتف (باستثناء الموظف الحالي)
        const existingEmployee = this.currentEmployees.find(e => e.phone === phone && e.id !== employeeId);
        if (existingEmployee) {
            Utils.showNotification('رقم الهاتف مستخدم بالفعل', 'warning');
            return;
        }

        const updateData = {
            name: name,
            phone: phone,
            nationalId: formData.get('nationalId')?.trim() || '',
            position: formData.get('position')?.trim() || '',
            baseSalary: parseFloat(formData.get('baseSalary')) || 0,
            generalCommission: parseFloat(formData.get('generalCommission')) || 0,
            porcelainCommission: parseFloat(formData.get('porcelainCommission')) || 0,
            zirconCommission: parseFloat(formData.get('zirconCommission')) || 0,
            orthodonticCommission: parseFloat(formData.get('orthodonticCommission')) || 0,
            removableCommission: parseFloat(formData.get('removableCommission')) || 0,
            deductionDays: parseInt(formData.get('deductionDays')) || 0,
            notes: formData.get('notes')?.trim() || ''
        };

        // تحديث في قاعدة البيانات
        const updated = Database.updateEmployee(employeeId, updateData);

        if (updated) {
            Utils.showNotification('تم تحديث الموظف بنجاح', 'success');
            Utils.closeModal();
            this.loadData();
        } else {
            Utils.showNotification('فشل في تحديث الموظف', 'error');
        }
    }

    // حساب مرتب موظف محدد
    static calculateEmployeeSalary(employeeId) {
        const employee = Database.getEmployeeById(employeeId);
        if (!employee) return;

        // الحصول على التركيبات المكتملة للشهر الحالي
        const now = new Date();
        const currentMonth = now.getMonth();
        const currentYear = now.getFullYear();

        const prosthetics = Database.getProsthetics().filter(p => {
            const completedDate = new Date(p.updatedAt || p.createdAt);
            return p.status === 'completed' &&
                   completedDate.getMonth() === currentMonth &&
                   completedDate.getFullYear() === currentYear;
        });

        // حساب العمولات
        let totalCommission = 0;
        const commissionDetails = [];

        prosthetics.forEach(p => {
            let commissionRate = employee.generalCommission || 0;

            // تطبيق نسب خاصة حسب نوع التركيبة
            switch (p.type) {
                case 'porcelain':
                    commissionRate = employee.porcelainCommission || employee.generalCommission || 0;
                    break;
                case 'zircon':
                    commissionRate = employee.zirconCommission || employee.generalCommission || 0;
                    break;
                case 'orthodontic':
                    commissionRate = employee.orthodonticCommission || employee.generalCommission || 0;
                    break;
                case 'removable':
                    commissionRate = employee.removableCommission || employee.generalCommission || 0;
                    break;
            }

            const commission = (p.totalPrice * commissionRate) / 100;
            totalCommission += commission;

            if (commission > 0) {
                commissionDetails.push({
                    caseNumber: p.caseNumber,
                    type: p.type,
                    price: p.totalPrice,
                    rate: commissionRate,
                    commission: commission
                });
            }
        });

        // حساب خصم الأيام
        const dailySalary = (employee.baseSalary || 0) / 30;
        const deductionAmount = dailySalary * (employee.deductionDays || 0);

        // المرتب النهائي
        const finalSalary = (employee.baseSalary || 0) + totalCommission - deductionAmount;

        Utils.createModal({
            title: `حساب مرتب ${employee.name} - ${now.toLocaleDateString('ar-EG', { month: 'long', year: 'numeric' })}`,
            content: `
                <div class="salary-calculation">
                    <div class="salary-summary">
                        <div class="summary-grid">
                            <div class="summary-item">
                                <label>الراتب الأساسي:</label>
                                <span>${Utils.formatCurrency(employee.baseSalary || 0)}</span>
                            </div>
                            <div class="summary-item">
                                <label>إجمالي العمولات:</label>
                                <span class="positive">${Utils.formatCurrency(totalCommission)}</span>
                            </div>
                            <div class="summary-item">
                                <label>خصم الأيام (${employee.deductionDays || 0} يوم):</label>
                                <span class="negative">${Utils.formatCurrency(deductionAmount)}</span>
                            </div>
                            <div class="summary-item total">
                                <label>المرتب النهائي:</label>
                                <span>${Utils.formatCurrency(finalSalary)}</span>
                            </div>
                        </div>
                    </div>

                    ${commissionDetails.length > 0 ? `
                        <div class="commission-details">
                            <h4>تفاصيل العمولات</h4>
                            <table class="commission-table">
                                <thead>
                                    <tr>
                                        <th>رقم الحالة</th>
                                        <th>النوع</th>
                                        <th>السعر</th>
                                        <th>النسبة</th>
                                        <th>العمولة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${commissionDetails.map(detail => `
                                        <tr>
                                            <td>${detail.caseNumber}</td>
                                            <td>${Prosthetics.getTypeDisplayName(detail.type)}</td>
                                            <td>${Utils.formatCurrency(detail.price)}</td>
                                            <td>${detail.rate}%</td>
                                            <td>${Utils.formatCurrency(detail.commission)}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    ` : `
                        <div class="no-commission">
                            <p>لا توجد عمولات لهذا الشهر</p>
                        </div>
                    `}

                    <div class="modal-actions">
                        <button class="btn btn-primary" onclick="Employees.saveSalaryRecord('${employee.id}', ${finalSalary}, ${totalCommission}, ${deductionAmount})">
                            <i class="fas fa-save"></i>
                            حفظ سجل المرتب
                        </button>
                        <button class="btn btn-warning" onclick="Employees.printSalary('${employee.id}', ${finalSalary}, ${totalCommission}, ${deductionAmount})">
                            <i class="fas fa-print"></i>
                            طباعة
                        </button>
                        <button class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            `,
            size: 'large'
        });
    }

    // حفظ سجل المرتب
    static saveSalaryRecord(employeeId, finalSalary, totalCommission, deductionAmount) {
        const employee = Database.getEmployeeById(employeeId);
        if (!employee) return;

        const now = new Date();
        const salaryRecord = {
            id: Utils.generateId(),
            employeeId: employeeId,
            employeeName: employee.name,
            month: now.getMonth() + 1,
            year: now.getFullYear(),
            baseSalary: employee.baseSalary || 0,
            totalCommission: totalCommission,
            deductionAmount: deductionAmount,
            finalSalary: finalSalary,
            deductionDays: employee.deductionDays || 0,
            createdAt: now.toISOString(),
            createdBy: Auth.getCurrentUser()?.id
        };

        // حفظ في قاعدة البيانات (يمكن إضافة جدول منفصل للمرتبات)
        let salaryRecords = Database.getItem('salaryRecords') || [];

        // التحقق من عدم وجود سجل للشهر نفسه
        const existingRecord = salaryRecords.find(r =>
            r.employeeId === employeeId &&
            r.month === salaryRecord.month &&
            r.year === salaryRecord.year
        );

        if (existingRecord) {
            Utils.showNotification('يوجد سجل مرتب لهذا الشهر بالفعل', 'warning');
            return;
        }

        salaryRecords.push(salaryRecord);
        Database.setItem('salaryRecords', salaryRecords);

        Utils.showNotification('تم حفظ سجل المرتب بنجاح', 'success');
        Utils.closeModal();
    }

    // طباعة المرتب
    static printSalary(employeeId, finalSalary, totalCommission, deductionAmount) {
        const employee = Database.getEmployeeById(employeeId);
        if (!employee) return;

        const now = new Date();
        const monthName = now.toLocaleDateString('ar-EG', { month: 'long', year: 'numeric' });

        // إنشاء نافذة طباعة
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>مرتب ${employee.name} - ${monthName}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .salary-details { margin-bottom: 20px; }
                    .detail-row { margin-bottom: 10px; display: flex; justify-content: space-between; }
                    .label { font-weight: bold; }
                    .total { border-top: 2px solid #000; padding-top: 10px; font-size: 1.2em; font-weight: bold; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>معمل الأسنان المتخصص</h1>
                    <h2>كشف مرتب</h2>
                    <h3>${employee.name} - ${monthName}</h3>
                </div>
                <div class="salary-details">
                    <div class="detail-row">
                        <span class="label">الاسم:</span>
                        <span>${employee.name}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">المسمى الوظيفي:</span>
                        <span>${employee.position || '-'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">الراتب الأساسي:</span>
                        <span>${Utils.formatCurrency(employee.baseSalary || 0)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">إجمالي العمولات:</span>
                        <span>${Utils.formatCurrency(totalCommission)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">خصم الأيام (${employee.deductionDays || 0} يوم):</span>
                        <span>${Utils.formatCurrency(deductionAmount)}</span>
                    </div>
                    <div class="detail-row total">
                        <span class="label">المرتب النهائي:</span>
                        <span>${Utils.formatCurrency(finalSalary)}</span>
                    </div>
                </div>
                <div class="footer">
                    <p>تاريخ الطباعة: ${Utils.formatDate(new Date(), 'DD/MM/YYYY HH:mm')}</p>
                </div>
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.print();
    }

    // عرض تاريخ المرتبات
    static viewSalaryHistory(employeeId) {
        const employee = Database.getEmployeeById(employeeId);
        if (!employee) return;

        const salaryRecords = (Database.getItem('salaryRecords') || [])
            .filter(r => r.employeeId === employeeId)
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        Utils.closeModal(); // إغلاق النافذة الحالية

        Utils.createModal({
            title: `تاريخ مرتبات ${employee.name}`,
            content: `
                <div class="salary-history">
                    <div class="history-table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>الشهر/السنة</th>
                                    <th>الراتب الأساسي</th>
                                    <th>العمولات</th>
                                    <th>الخصومات</th>
                                    <th>المرتب النهائي</th>
                                    <th>تاريخ الحفظ</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${salaryRecords.length > 0 ? salaryRecords.map(record => `
                                    <tr>
                                        <td>${record.month}/${record.year}</td>
                                        <td>${Utils.formatCurrency(record.baseSalary)}</td>
                                        <td>${Utils.formatCurrency(record.totalCommission)}</td>
                                        <td>${Utils.formatCurrency(record.deductionAmount)}</td>
                                        <td><strong>${Utils.formatCurrency(record.finalSalary)}</strong></td>
                                        <td>${Utils.formatDate(record.createdAt, 'DD/MM/YYYY')}</td>
                                    </tr>
                                `).join('') : `
                                    <tr>
                                        <td colspan="6" class="no-data">
                                            <div class="no-data-message">
                                                <i class="fas fa-history"></i>
                                                <p>لا يوجد تاريخ مرتبات</p>
                                            </div>
                                        </td>
                                    </tr>
                                `}
                            </tbody>
                        </table>
                    </div>

                    <div class="modal-actions">
                        <button class="btn btn-primary" onclick="Employees.calculateEmployeeSalary('${employee.id}')">
                            <i class="fas fa-calculator"></i>
                            حساب مرتب جديد
                        </button>
                        <button class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            `,
            size: 'large'
        });
    }

    // حساب مرتبات جميع الموظفين
    static calculateSalaries() {
        const employees = this.currentEmployees;
        if (employees.length === 0) {
            Utils.showNotification('لا يوجد موظفون', 'warning');
            return;
        }

        const now = new Date();
        const monthName = now.toLocaleDateString('ar-EG', { month: 'long', year: 'numeric' });

        Utils.createModal({
            title: `حساب مرتبات جميع الموظفين - ${monthName}`,
            content: `
                <div class="all-salaries">
                    <div class="salaries-info">
                        <p>سيتم حساب مرتبات جميع الموظفين للشهر الحالي</p>
                    </div>

                    <div id="salaries-results" class="salaries-results">
                        <!-- سيتم تحميل النتائج هنا -->
                    </div>

                    <div class="modal-actions">
                        <button class="btn btn-primary" onclick="Employees.processAllSalaries()">
                            <i class="fas fa-calculator"></i>
                            بدء الحساب
                        </button>
                        <button class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            `,
            size: 'large'
        });
    }

    // معالجة مرتبات جميع الموظفين
    static processAllSalaries() {
        const resultsContainer = document.getElementById('salaries-results');
        if (!resultsContainer) return;

        resultsContainer.innerHTML = '<div class="loading">جاري حساب المرتبات...</div>';

        // محاكاة معالجة تدريجية
        setTimeout(() => {
            const employees = this.currentEmployees;
            const results = employees.map(employee => {
                // حساب مبسط للمرتب (يمكن تطويره أكثر)
                const baseSalary = employee.baseSalary || 0;
                const deductionAmount = (baseSalary / 30) * (employee.deductionDays || 0);
                const finalSalary = baseSalary - deductionAmount;

                return {
                    employee: employee,
                    baseSalary: baseSalary,
                    deductionAmount: deductionAmount,
                    finalSalary: finalSalary
                };
            });

            const totalSalaries = results.reduce((sum, r) => sum + r.finalSalary, 0);

            resultsContainer.innerHTML = `
                <div class="salaries-summary">
                    <div class="summary-item">
                        <label>إجمالي المرتبات:</label>
                        <span>${Utils.formatCurrency(totalSalaries)}</span>
                    </div>
                </div>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>الراتب الأساسي</th>
                            <th>الخصومات</th>
                            <th>المرتب النهائي</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${results.map(result => `
                            <tr>
                                <td>${result.employee.name}</td>
                                <td>${Utils.formatCurrency(result.baseSalary)}</td>
                                <td>${Utils.formatCurrency(result.deductionAmount)}</td>
                                <td><strong>${Utils.formatCurrency(result.finalSalary)}</strong></td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        }, 1000);
    }

    // حذف الموظف
    static async deleteEmployee(employeeId) {
        const employee = Database.getEmployeeById(employeeId);
        if (!employee) return;

        const confirmed = await Utils.confirm(
            `هل أنت متأكد من حذف الموظف "${employee.name}"؟`,
            'تأكيد الحذف'
        );

        if (confirmed) {
            const deleted = Database.deleteEmployee(employeeId);

            if (deleted) {
                Utils.showNotification('تم حذف الموظف بنجاح', 'success');
                this.loadData();
            } else {
                Utils.showNotification('فشل في حذف الموظف', 'error');
            }
        }
    }

    // تصدير البيانات
    static exportData() {
        const data = this.filteredEmployees.map(employee => ({
            'الاسم': employee.name,
            'رقم الهاتف': employee.phone,
            'رقم البطاقة الشخصية': employee.nationalId || '',
            'المسمى الوظيفي': employee.position || '',
            'الراتب الأساسي': employee.baseSalary || 0,
            'نسبة العمولة العامة': employee.generalCommission || 0,
            'نسبة البورسلين': employee.porcelainCommission || 0,
            'نسبة الزيركون': employee.zirconCommission || 0,
            'نسبة التقويم': employee.orthodonticCommission || 0,
            'نسبة الأطقم المتحركة': employee.removableCommission || 0,
            'أيام الخصم': employee.deductionDays || 0,
            'تاريخ التوظيف': Utils.formatDate(employee.createdAt, 'DD/MM/YYYY'),
            'الملاحظات': employee.notes || ''
        }));

        const filename = `employees_${Utils.formatDate(new Date(), 'YYYY-MM-DD')}.csv`;
        Utils.exportToCSV(data, filename);
    }
}
