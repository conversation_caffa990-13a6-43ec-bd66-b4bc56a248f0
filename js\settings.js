// إدارة الإعدادات المتطورة
console.log('🔄 بدء تحميل ملف settings.js المحدث...');

class Settings {
    static currentSettings = {};
    static currentTab = 'general';
    static isInitialized = false;
    static validationRules = {};
    static changeHistory = [];
    static autoSaveTimer = null;

    // تهيئة الكلاس
    static init() {
        if (this.isInitialized) {
            console.log('⚠️ كلاس Settings مهيأ مسبقاً');
            return;
        }

        console.log('🚀 تهيئة كلاس Settings...');
        this.setupValidationRules();
        this.loadSettings();
        this.isInitialized = true;
        console.log('✅ تم تهيئة كلاس Settings بنجاح');
    }

    // إعداد قواعد التحقق
    static setupValidationRules() {
        this.validationRules = {
            labName: { required: true, minLength: 3, maxLength: 100 },
            email: { required: false, pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
            phone: { required: false, pattern: /^[\d\s\-\+\(\)]+$/ },
            website: { required: false, pattern: /^https?:\/\/.+/ },
            taxRate: { required: false, min: 0, max: 100 },
            currency: { required: true }
        };
    }

    // تحميل الإعدادات
    static loadSettings() {
        try {
            this.currentSettings = Database.getSettings() || {};
            console.log('✅ تم تحميل الإعدادات:', Object.keys(this.currentSettings).length);
        } catch (error) {
            console.error('❌ خطأ في تحميل الإعدادات:', error);
            this.currentSettings = {};
        }
    }

    // حساب الإحصائيات
    static calculateStats() {
        const users = Database.getUsers() || [];
        const settings = this.currentSettings;
        const dataSize = this.calculateDataSize();

        return {
            totalUsers: users.length,
            totalSettings: Object.keys(settings).length,
            dataSize: this.formatDataSize(dataSize)
        };
    }

    // حساب حجم البيانات
    static calculateDataSize() {
        try {
            let totalSize = 0;
            for (let key in localStorage) {
                if (key.startsWith('dentalLab_')) {
                    totalSize += localStorage[key].length;
                }
            }
            return totalSize;
        } catch (error) {
            return 0;
        }
    }

    // تنسيق حجم البيانات
    static formatDataSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // عرض صفحة الإعدادات المتطورة
    static render() {
        try {
            console.log('🔄 عرض صفحة الإعدادات المتطورة...');
            
            const pageContent = document.getElementById('pageContent');
            if (!pageContent) {
                console.error('❌ لم يتم العثور على pageContent');
                return;
            }

            // حساب الإحصائيات
            const stats = this.calculateStats();

            pageContent.innerHTML = `
                <div class="settings-container">
                    <!-- عنوان الإدارة المطور -->
                    <div class="department-header settings">
                        <div class="department-header-content">
                            <div class="department-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="department-info">
                                <h1 class="department-name" data-text="إعدادات النظام">إعدادات النظام</h1>
                                <p class="department-description">مركز التحكم الشامل لإدارة جميع إعدادات النظام والتخصيص المتقدم</p>
                                <div class="department-stats">
                                    <div class="department-stat">
                                        <span class="department-stat-value">${stats.totalUsers}</span>
                                        <span class="department-stat-label">المستخدمين</span>
                                    </div>
                                    <div class="department-stat">
                                        <span class="department-stat-value">${stats.totalSettings}</span>
                                        <span class="department-stat-label">الإعدادات</span>
                                    </div>
                                    <div class="department-stat">
                                        <span class="department-stat-value">${stats.dataSize}</span>
                                        <span class="department-stat-label">حجم البيانات</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات السريعة -->
                    <div class="page-actions-container">
                        <div class="page-actions">
                            <button class="btn btn-primary" onclick="Settings.saveAllSettings()">
                                <i class="fas fa-save"></i>
                                حفظ جميع الإعدادات
                            </button>
                            <button class="btn btn-success" onclick="Settings.exportSettings()">
                                <i class="fas fa-download"></i>
                                تصدير الإعدادات
                            </button>
                            <button class="btn btn-info" onclick="Settings.importSettings()">
                                <i class="fas fa-upload"></i>
                                استيراد الإعدادات
                            </button>
                            <button class="btn btn-warning" onclick="Settings.resetToDefaults()">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين افتراضي
                            </button>
                            <button class="btn btn-secondary" onclick="Settings.showChangeHistory()">
                                <i class="fas fa-history"></i>
                                سجل التغييرات
                            </button>
                            <button class="btn btn-warning" onclick="Settings.resetUserPermissions()">
                                <i class="fas fa-user-shield"></i>
                                إعادة تعيين الصلاحيات
                            </button>
                            <button class="btn btn-info" onclick="Settings.checkCurrentUserPermissions()">
                                <i class="fas fa-search"></i>
                                فحص الصلاحيات
                            </button>
                        </div>
                    </div>

                    <!-- التبويبات المتطورة -->
                    <div class="advanced-tabs">
                        <div class="tabs-header">
                            <button class="tab-btn active" data-tab="general" onclick="Settings.switchTab('general')">
                                <i class="fas fa-info-circle"></i>
                                <span>الإعدادات العامة</span>
                            </button>
                            <button class="tab-btn" data-tab="appearance" onclick="Settings.switchTab('appearance')">
                                <i class="fas fa-palette"></i>
                                <span>المظهر والثيم</span>
                            </button>
                            <button class="tab-btn" data-tab="prices" onclick="Settings.switchTab('prices')">
                                <i class="fas fa-dollar-sign"></i>
                                <span>إدارة الأسعار</span>
                            </button>
                            <button class="tab-btn" data-tab="users" onclick="Settings.switchTab('users')">
                                <i class="fas fa-users"></i>
                                <span>إدارة المستخدمين</span>
                            </button>
                            <button class="tab-btn" data-tab="notifications" onclick="Settings.switchTab('notifications')">
                                <i class="fas fa-bell"></i>
                                <span>الإشعارات</span>
                            </button>
                            <button class="tab-btn" data-tab="security" onclick="Settings.switchTab('security')">
                                <i class="fas fa-shield-alt"></i>
                                <span>الأمان</span>
                            </button>
                            <button class="tab-btn" data-tab="backup" onclick="Settings.switchTab('backup')">
                                <i class="fas fa-database"></i>
                                <span>النسخ الاحتياطي</span>
                            </button>
                            <button class="tab-btn" data-tab="system" onclick="Settings.switchTab('system')">
                                <i class="fas fa-server"></i>
                                <span>النظام</span>
                            </button>
                        </div>

                        <!-- محتوى التبويبات -->
                        <div class="tabs-content">
                            ${this.renderGeneralTab()}
                            ${this.renderAppearanceTab()}
                            ${this.renderPricesTab()}
                            ${this.renderUsersTab()}
                            ${this.renderNotificationsTab()}
                            ${this.renderSecurityTab()}
                            ${this.renderBackupTab()}
                            ${this.renderSystemTab()}
                        </div>
                    </div>
                </div>
            `;

            // تهيئة الواجهة
            this.initInterface();

            // تحديث إحصائيات المستخدمين
            this.updateUsersStats();

            console.log('✅ تم عرض صفحة الإعدادات بنجاح');

        } catch (error) {
            console.error('❌ خطأ في عرض صفحة الإعدادات:', error);
            Utils.showNotification('حدث خطأ في عرض الصفحة', 'error');
        }
    }

    // تهيئة الواجهة
    static initInterface() {
        this.currentTab = 'general';
        
        // إعداد معالجات الأحداث
        setTimeout(() => {
            this.setupEventListeners();
            this.loadAllData();
            this.setupAutoSave();
        }, 100);

        console.log('✅ تم تهيئة واجهة الإعدادات');
    }

    // إعداد معالجات الأحداث
    static setupEventListeners() {
        // معالجات التبويبات
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabName = e.target.closest('.tab-btn').getAttribute('data-tab');
                this.switchTab(tabName);
            });
        });

        // معالجات النماذج
        this.setupFormListeners();

        // معالجات الملفات
        this.setupFileListeners();

        console.log('✅ تم إعداد معالجات الأحداث');
    }

    // إعداد معالجات النماذج
    static setupFormListeners() {
        // معالج تغيير الحقول
        document.addEventListener('input', (e) => {
            if (e.target.closest('.settings-container')) {
                this.handleFieldChange(e.target);
            }
        });

        // معالج تغيير القوائم المنسدلة
        document.addEventListener('change', (e) => {
            if (e.target.closest('.settings-container')) {
                this.handleFieldChange(e.target);
            }
        });
    }

    // إعداد معالجات الملفات
    static setupFileListeners() {
        const fileInputs = document.querySelectorAll('input[type="file"]');
        fileInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                this.handleFileUpload(e.target);
            });
        });
    }

    // إعداد الحفظ التلقائي
    static setupAutoSave() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
        }

        this.autoSaveTimer = setInterval(() => {
            this.autoSaveSettings();
        }, 30000); // كل 30 ثانية

        console.log('✅ تم إعداد الحفظ التلقائي');
    }

    // تبديل التبويبات
    static switchTab(tabName) {
        this.currentTab = tabName;

        // تحديث أزرار التبويبات
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`)?.classList.add('active');

        // تحديث محتوى التبويبات
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`)?.classList.add('active');

        console.log(`✅ تم التبديل إلى تبويب: ${tabName}`);
    }

    // عرض تبويب الإعدادات العامة
    static renderGeneralTab() {
        return `
            <div class="tab-content active" id="general-tab">
                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-building"></i> معلومات المعمل</h3>
                        <p>إعدادات المعلومات الأساسية للمعمل</p>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="lab-name">اسم المعمل *</label>
                            <input type="text" id="lab-name" name="labName" required
                                   placeholder="أدخل اسم المعمل">
                            <small class="field-help">الاسم الذي سيظهر في التقارير والفواتير</small>
                        </div>

                        <div class="form-group full-width">
                            <label for="lab-address">العنوان الكامل</label>
                            <textarea id="lab-address" name="address" rows="3"
                                      placeholder="أدخل العنوان الكامل للمعمل"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="lab-phone1">رقم الهاتف الأول *</label>
                            <input type="tel" id="lab-phone1" name="phone1" required
                                   placeholder="05xxxxxxxx">
                        </div>

                        <div class="form-group">
                            <label for="lab-phone2">رقم الهاتف الثاني</label>
                            <input type="tel" id="lab-phone2" name="phone2"
                                   placeholder="05xxxxxxxx">
                        </div>

                        <div class="form-group">
                            <label for="lab-phone3">رقم الهاتف الثالث</label>
                            <input type="tel" id="lab-phone3" name="phone3"
                                   placeholder="05xxxxxxxx">
                        </div>

                        <div class="form-group">
                            <label for="lab-email">البريد الإلكتروني</label>
                            <input type="email" id="lab-email" name="email"
                                   placeholder="<EMAIL>">
                        </div>

                        <div class="form-group">
                            <label for="lab-website">الموقع الإلكتروني</label>
                            <input type="url" id="lab-website" name="website"
                                   placeholder="https://www.dentallab.com">
                        </div>

                        <div class="form-group">
                            <label for="lab-currency">العملة الافتراضية *</label>
                            <select id="lab-currency" name="currency" required>
                                <option value="">اختر العملة</option>
                                <option value="جنيه">جنيه مصري (EGP)</option>
                                <option value="ريال">ريال سعودي (SAR)</option>
                                <option value="درهم">درهم إماراتي (AED)</option>
                                <option value="دولار">دولار أمريكي (USD)</option>
                                <option value="يورو">يورو (EUR)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="tax-rate">معدل الضريبة (%)</label>
                            <input type="number" id="tax-rate" name="taxRate"
                                   step="0.01" min="0" max="100" placeholder="0.00">
                            <small class="field-help">اتركه فارغاً إذا لم تكن هناك ضرائب</small>
                        </div>

                        <div class="form-group">
                            <label for="lab-logo">شعار المعمل</label>
                            <input type="file" id="lab-logo" name="logo" accept="image/*">
                            <small class="field-help">يُفضل صورة بحجم 200x200 بكسل</small>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-cogs"></i> إعدادات النظام العامة</h3>
                        <p>إعدادات تشغيل النظام الأساسية</p>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="default-language">اللغة الافتراضية</label>
                            <select id="default-language" name="defaultLanguage">
                                <option value="ar">العربية</option>
                                <option value="en">English</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="date-format">تنسيق التاريخ</label>
                            <select id="date-format" name="dateFormat">
                                <option value="dd/mm/yyyy">يوم/شهر/سنة</option>
                                <option value="mm/dd/yyyy">شهر/يوم/سنة</option>
                                <option value="yyyy-mm-dd">سنة-شهر-يوم</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="time-format">تنسيق الوقت</label>
                            <select id="time-format" name="timeFormat">
                                <option value="24">24 ساعة</option>
                                <option value="12">12 ساعة</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="work-hours-start">بداية ساعات العمل</label>
                            <input type="time" id="work-hours-start" name="workHoursStart" value="08:00">
                        </div>

                        <div class="form-group">
                            <label for="work-hours-end">نهاية ساعات العمل</label>
                            <input type="time" id="work-hours-end" name="workHoursEnd" value="18:00">
                        </div>

                        <div class="form-group">
                            <label for="default-delivery-days">أيام التسليم الافتراضية</label>
                            <input type="number" id="default-delivery-days" name="defaultDeliveryDays"
                                   min="1" max="30" value="7" placeholder="7">
                            <small class="field-help">عدد الأيام الافتراضي لتسليم العمل</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // عرض تبويب المظهر والثيم
    static renderAppearanceTab() {
        return `
            <div class="tab-content" id="appearance-tab">
                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-palette"></i> تخصيص الألوان</h3>
                        <p>تخصيص ألوان النظام حسب هوية المعمل</p>
                    </div>

                    <div class="color-customization">
                        <div class="color-preview">
                            <div class="preview-card">
                                <div class="preview-header" id="preview-header">
                                    <h4>معاينة التصميم</h4>
                                </div>
                                <div class="preview-content">
                                    <button class="preview-btn primary" id="preview-primary">زر رئيسي</button>
                                    <button class="preview-btn secondary" id="preview-secondary">زر ثانوي</button>
                                    <div class="preview-text">نص عادي</div>
                                </div>
                            </div>
                        </div>

                        <div class="color-controls">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="color-primary">اللون الرئيسي</label>
                                    <div class="color-input-group">
                                        <input type="color" id="color-primary" name="colorPrimary"
                                               value="#2563eb" onchange="Settings.updateColorPreview()">
                                        <input type="text" id="color-primary-hex" value="#2563eb"
                                               placeholder="#2563eb" onchange="Settings.updateColorFromHex(this, 'color-primary')">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="color-secondary">اللون الثانوي</label>
                                    <div class="color-input-group">
                                        <input type="color" id="color-secondary" name="colorSecondary"
                                               value="#64748b" onchange="Settings.updateColorPreview()">
                                        <input type="text" id="color-secondary-hex" value="#64748b"
                                               placeholder="#64748b" onchange="Settings.updateColorFromHex(this, 'color-secondary')">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="color-accent">لون التمييز</label>
                                    <div class="color-input-group">
                                        <input type="color" id="color-accent" name="colorAccent"
                                               value="#7c3aed" onchange="Settings.updateColorPreview()">
                                        <input type="text" id="color-accent-hex" value="#7c3aed"
                                               placeholder="#7c3aed" onchange="Settings.updateColorFromHex(this, 'color-accent')">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="color-success">لون النجاح</label>
                                    <div class="color-input-group">
                                        <input type="color" id="color-success" name="colorSuccess"
                                               value="#10b981" onchange="Settings.updateColorPreview()">
                                        <input type="text" id="color-success-hex" value="#10b981"
                                               placeholder="#10b981" onchange="Settings.updateColorFromHex(this, 'color-success')">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="color-warning">لون التحذير</label>
                                    <div class="color-input-group">
                                        <input type="color" id="color-warning" name="colorWarning"
                                               value="#f59e0b" onchange="Settings.updateColorPreview()">
                                        <input type="text" id="color-warning-hex" value="#f59e0b"
                                               placeholder="#f59e0b" onchange="Settings.updateColorFromHex(this, 'color-warning')">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="color-danger">لون الخطر</label>
                                    <div class="color-input-group">
                                        <input type="color" id="color-danger" name="colorDanger"
                                               value="#ef4444" onchange="Settings.updateColorPreview()">
                                        <input type="text" id="color-danger-hex" value="#ef4444"
                                               placeholder="#ef4444" onchange="Settings.updateColorFromHex(this, 'color-danger')">
                                    </div>
                                </div>
                            </div>

                            <div class="color-actions">
                                <button class="btn btn-secondary" onclick="Settings.resetColors()">
                                    <i class="fas fa-undo"></i>
                                    إعادة تعيين الألوان
                                </button>
                                <button class="btn btn-primary" onclick="Settings.applyColors()">
                                    <i class="fas fa-check"></i>
                                    تطبيق الألوان
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-moon"></i> إعدادات المظهر</h3>
                        <p>تخصيص مظهر النظام والخطوط</p>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="theme-mode">وضع المظهر</label>
                            <select id="theme-mode" name="themeMode" onchange="Settings.changeThemeMode(this.value)">
                                <option value="light">فاتح</option>
                                <option value="dark">داكن</option>
                                <option value="auto">تلقائي</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="font-size">حجم الخط</label>
                            <select id="font-size" name="fontSize" onchange="Settings.changeFontSize(this.value)">
                                <option value="small">صغير</option>
                                <option value="medium">متوسط</option>
                                <option value="large">كبير</option>
                                <option value="extra-large">كبير جداً</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="sidebar-style">نمط الشريط الجانبي</label>
                            <select id="sidebar-style" name="sidebarStyle">
                                <option value="expanded">موسع</option>
                                <option value="collapsed">مطوي</option>
                                <option value="mini">مصغر</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="animation-speed">سرعة الحركات</label>
                            <select id="animation-speed" name="animationSpeed">
                                <option value="slow">بطيء</option>
                                <option value="normal">عادي</option>
                                <option value="fast">سريع</option>
                                <option value="none">بدون حركات</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-image"></i> خلفية النظام</h3>
                        <p>تخصيص خلفية النظام والصور</p>
                    </div>

                    <div class="background-options">
                        <div class="background-type">
                            <label>
                                <input type="radio" name="backgroundType" value="color" checked>
                                <span>لون خالص</span>
                            </label>
                            <div class="background-control">
                                <input type="color" id="background-color" value="#f8fafc">
                            </div>
                        </div>

                        <div class="background-type">
                            <label>
                                <input type="radio" name="backgroundType" value="gradient">
                                <span>تدرج لوني</span>
                            </label>
                            <div class="background-control">
                                <input type="color" id="gradient-start" value="#3b82f6">
                                <input type="color" id="gradient-end" value="#8b5cf6">
                            </div>
                        </div>

                        <div class="background-type">
                            <label>
                                <input type="radio" name="backgroundType" value="image">
                                <span>صورة خلفية</span>
                            </label>
                            <div class="background-control">
                                <input type="file" id="background-image" accept="image/*">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // عرض تبويب إدارة الأسعار
    static renderPricesTab() {
        return `
            <div class="tab-content" id="prices-tab">
                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-dollar-sign"></i> إدارة أسعار التركيبات</h3>
                        <p>إدارة شاملة لأسعار جميع أنواع التركيبات السنية</p>
                        <div class="section-actions">
                            <button class="btn btn-primary" onclick="Settings.addNewPriceCategory()">
                                <i class="fas fa-plus"></i>
                                إضافة فئة جديدة
                            </button>
                            <button class="btn btn-success" onclick="Settings.importPrices()">
                                <i class="fas fa-upload"></i>
                                استيراد الأسعار
                            </button>
                            <button class="btn btn-info" onclick="Settings.exportPrices()">
                                <i class="fas fa-download"></i>
                                تصدير الأسعار
                            </button>
                        </div>
                    </div>

                    <div class="prices-management">
                        ${this.renderPriceCategories()}
                    </div>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-calculator"></i> إعدادات التسعير</h3>
                        <p>إعدادات عامة للتسعير والحسابات</p>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="price-rounding">تقريب الأسعار</label>
                            <select id="price-rounding" name="priceRounding">
                                <option value="none">بدون تقريب</option>
                                <option value="nearest">أقرب رقم صحيح</option>
                                <option value="up">تقريب لأعلى</option>
                                <option value="down">تقريب لأسفل</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="discount-limit">حد الخصم الأقصى (%)</label>
                            <input type="number" id="discount-limit" name="discountLimit"
                                   min="0" max="100" step="0.1" placeholder="20">
                        </div>

                        <div class="form-group">
                            <label for="price-validity">صلاحية الأسعار (أيام)</label>
                            <input type="number" id="price-validity" name="priceValidity"
                                   min="1" max="365" placeholder="30">
                        </div>

                        <div class="form-group">
                            <label for="auto-price-update">تحديث الأسعار التلقائي</label>
                            <select id="auto-price-update" name="autoPriceUpdate">
                                <option value="disabled">معطل</option>
                                <option value="monthly">شهرياً</option>
                                <option value="quarterly">كل 3 أشهر</option>
                                <option value="yearly">سنوياً</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="price-increase-rate">معدل زيادة الأسعار (%)</label>
                            <input type="number" id="price-increase-rate" name="priceIncreaseRate"
                                   min="0" max="50" step="0.1" placeholder="5">
                        </div>

                        <div class="form-group">
                            <label for="bulk-discount">خصم الكمية</label>
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="enable-bulk-discount" name="enableBulkDiscount">
                                    <span class="checkbox-custom"></span>
                                    تفعيل خصم الكمية
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="settings-section" id="bulk-discount-settings" style="display: none;">
                    <div class="section-header">
                        <h3><i class="fas fa-percentage"></i> إعدادات خصم الكمية</h3>
                        <p>تحديد نسب الخصم حسب الكمية</p>
                    </div>

                    <div class="bulk-discount-rules">
                        <div class="discount-rule">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label>من (قطعة)</label>
                                    <input type="number" name="bulk-from-1" min="1" value="5">
                                </div>
                                <div class="form-group">
                                    <label>إلى (قطعة)</label>
                                    <input type="number" name="bulk-to-1" min="1" value="10">
                                </div>
                                <div class="form-group">
                                    <label>نسبة الخصم (%)</label>
                                    <input type="number" name="bulk-discount-1" min="0" max="50" step="0.1" value="5">
                                </div>
                                <div class="form-group">
                                    <button class="btn btn-danger btn-sm" onclick="Settings.removeBulkRule(this)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <button class="btn btn-secondary" onclick="Settings.addBulkRule()">
                            <i class="fas fa-plus"></i>
                            إضافة قاعدة خصم
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // عرض فئات الأسعار
    static renderPriceCategories() {
        const prostheticTypes = Database.getProstheticTypes() || {};

        return Object.keys(prostheticTypes).map(category => `
            <div class="price-category-card">
                <div class="category-header">
                    <h4>${this.getCategoryDisplayName(category)}</h4>
                    <div class="category-actions">
                        <button class="btn btn-sm btn-secondary" onclick="Settings.editCategory('${category}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="Settings.deleteCategory('${category}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <div class="price-items">
                    ${Object.keys(prostheticTypes[category]).map(item => `
                        <div class="price-item">
                            <div class="item-info">
                                <span class="item-name">${item}</span>
                            </div>
                            <div class="item-price">
                                <input type="number" value="${prostheticTypes[category][item]}"
                                       min="0" step="0.01"
                                       onchange="Settings.updatePrice('${category}', '${item}', this.value)">
                                <span class="currency">${this.currentSettings.currency || 'جنيه'}</span>
                            </div>
                            <div class="item-actions">
                                <button class="btn btn-sm btn-danger" onclick="Settings.deletePrice('${category}', '${item}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `).join('')}

                    <div class="add-price-item">
                        <button class="btn btn-sm btn-primary" onclick="Settings.addPriceItem('${category}')">
                            <i class="fas fa-plus"></i>
                            إضافة عنصر جديد
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    // الحصول على اسم الفئة للعرض
    static getCategoryDisplayName(category) {
        const categoryNames = {
            porcelain: 'البورسلين',
            zircon: 'الزيركون',
            metal: 'المعدني',
            removable: 'المتحرك',
            orthodontic: 'التقويم',
            additional: 'الإضافي'
        };
        return categoryNames[category] || category;
    }

    // عرض إدارة الأدوار
    static renderRolesManagement() {
        const roles = {
            admin: {
                name: 'مدير النظام',
                description: 'صلاحيات كاملة لجميع أجزاء النظام',
                permissions: ['*'],
                color: '#dc2626'
            },
            manager: {
                name: 'مدير',
                description: 'صلاحيات إدارية محدودة',
                permissions: ['view_dashboard', 'manage_prosthetics', 'manage_doctors', 'manage_employees', 'view_financial', 'view_reports', 'manage_settings'],
                color: '#2563eb'
            },
            technician: {
                name: 'فني',
                description: 'صلاحيات العمل على التركيبات',
                permissions: ['view_dashboard', 'view_prosthetics', 'add_prosthetic', 'edit_prosthetic'],
                color: '#059669'
            },
            user: {
                name: 'مستخدم',
                description: 'صلاحيات محدودة للعرض فقط',
                permissions: ['view_dashboard', 'view_prosthetics'],
                color: '#7c3aed'
            }
        };

        return Object.keys(roles).map(roleKey => {
            const role = roles[roleKey];
            return `
                <div class="role-card">
                    <div class="role-header" style="border-left: 4px solid ${role.color}">
                        <div class="role-info">
                            <h4 class="role-name">${role.name}</h4>
                            <p class="role-description">${role.description}</p>
                        </div>
                        <div class="role-actions">
                            <button class="btn btn-sm btn-secondary" onclick="Settings.editRole('${roleKey}')">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </button>
                        </div>
                    </div>
                    <div class="role-permissions">
                        <h5>الصلاحيات:</h5>
                        <div class="permissions-list">
                            ${role.permissions.map(permission => `
                                <span class="permission-badge">${this.getPermissionDisplayName(permission)}</span>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    // الحصول على اسم الصلاحية للعرض
    static getPermissionDisplayName(permission) {
        const permissionNames = {
            '*': 'جميع الصلاحيات',
            'view_dashboard': 'عرض لوحة التحكم',
            'manage_prosthetics': 'إدارة التركيبات',
            'manage_doctors': 'إدارة الأطباء',
            'manage_employees': 'إدارة الموظفين',
            'view_financial': 'عرض المالية',
            'manage_financial': 'إدارة المالية',
            'view_reports': 'عرض التقارير',
            'manage_settings': 'إدارة الإعدادات',
            'manage_users': 'إدارة المستخدمين',
            'manage_backup': 'إدارة النسخ الاحتياطي',
            'add_prosthetic': 'إضافة تركيبة',
            'edit_prosthetic': 'تعديل تركيبة',
            'view_prosthetics': 'عرض التركيبات'
        };
        return permissionNames[permission] || permission;
    }

    // عرض صلاحيات المستخدم
    static renderUserPermissions(user) {
        if (!user.permissions || user.permissions.length === 0) {
            return '<span class="permission-badge">لا توجد صلاحيات</span>';
        }

        if (user.permissions.includes('*')) {
            return '<span class="permission-badge admin">جميع الصلاحيات</span>';
        }

        return user.permissions.slice(0, 3).map(permission =>
            `<span class="permission-badge">${this.getPermissionDisplayName(permission)}</span>`
        ).join('') + (user.permissions.length > 3 ?
            `<span class="permission-badge">+${user.permissions.length - 3}</span>` : '');
    }

    // تعديل دور
    static editRole(roleKey) {
        Utils.showNotification(`تعديل دور: ${roleKey}`, 'info');
        // يمكن إضافة نافذة تعديل الدور هنا لاحقاً
        console.log('تعديل دور:', roleKey);
    }

    // تصفية المستخدمين
    static filterUsers() {
        const roleFilter = document.getElementById('user-role-filter')?.value || '';
        const statusFilter = document.getElementById('user-status-filter')?.value || '';

        console.log('تصفية المستخدمين:', { roleFilter, statusFilter });

        // إعادة عرض جدول المستخدمين مع الفلاتر
        this.renderUsersTable(roleFilter, statusFilter);
    }

    // البحث في المستخدمين
    static searchUsers(searchTerm) {
        console.log('البحث في المستخدمين:', searchTerm);

        // إعادة عرض جدول المستخدمين مع البحث
        this.renderUsersTable('', '', searchTerm);
    }

    // إضافة مستخدم جديد
    static showAddUserModal() {
        Utils.showNotification('فتح نافذة إضافة مستخدم جديد', 'info');
        // يمكن إضافة نافذة إضافة المستخدم هنا لاحقاً
        console.log('إضافة مستخدم جديد');
    }

    // إدارة الصلاحيات
    static showPermissionsModal() {
        Utils.showNotification('فتح نافذة إدارة الصلاحيات', 'info');
        // يمكن إضافة نافذة إدارة الصلاحيات هنا لاحقاً
        console.log('إدارة الصلاحيات');
    }

    // تصدير المستخدمين
    static exportUsers() {
        try {
            const users = Database.getUsers();
            const exportData = users.map(user => ({
                name: user.name,
                username: user.username,
                role: user.role,
                email: user.email,
                phone: user.phone,
                isActive: user.isActive,
                createdAt: user.createdAt,
                lastLogin: user.lastLogin
            }));

            const filename = `users_export_${Utils.formatDate(new Date(), 'YYYY-MM-DD')}.json`;
            Utils.exportToJSON(exportData, filename);

            Utils.showNotification('تم تصدير بيانات المستخدمين بنجاح', 'success');
        } catch (error) {
            console.error('خطأ في تصدير المستخدمين:', error);
            Utils.showNotification('حدث خطأ في تصدير المستخدمين', 'error');
        }
    }

    // الحصول على الأحرف الأولى من الاسم
    static getInitials(name) {
        if (!name) return '??';
        const words = name.split(' ');
        if (words.length >= 2) {
            return words[0].charAt(0) + words[1].charAt(0);
        }
        return name.charAt(0) + (name.charAt(1) || '');
    }

    // الحصول على اسم الدور للعرض
    static getRoleDisplayName(role) {
        const roleNames = {
            admin: 'مدير النظام',
            manager: 'مدير',
            technician: 'فني',
            user: 'مستخدم'
        };
        return roleNames[role] || role;
    }

    // تعديل مستخدم
    static editUser(userId) {
        Utils.showNotification(`تعديل المستخدم: ${userId}`, 'info');
        console.log('تعديل مستخدم:', userId);
    }

    // إعادة تعيين كلمة مرور المستخدم
    static resetUserPassword(userId) {
        if (confirm('هل أنت متأكد من إعادة تعيين كلمة مرور هذا المستخدم؟')) {
            Utils.showNotification(`تم إعادة تعيين كلمة مرور المستخدم: ${userId}`, 'success');
            console.log('إعادة تعيين كلمة مرور:', userId);
        }
    }

    // تبديل حالة المستخدم
    static toggleUserStatus(userId) {
        const user = Database.getUserById(userId);
        if (user) {
            const newStatus = !user.isActive;
            Database.updateUser(userId, { isActive: newStatus });

            Utils.showNotification(
                `تم ${newStatus ? 'تفعيل' : 'إلغاء تفعيل'} المستخدم بنجاح`,
                'success'
            );

            // إعادة تحميل الجدول
            this.renderUsersTable();
        }
    }

    // حذف مستخدم
    static deleteUser(userId) {
        if (confirm('هل أنت متأكد من حذف هذا المستخدم؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
            try {
                Database.deleteUser(userId);
                Utils.showNotification('تم حذف المستخدم بنجاح', 'success');

                // إعادة تحميل الجدول
                this.renderUsersTable();
            } catch (error) {
                console.error('خطأ في حذف المستخدم:', error);
                Utils.showNotification('حدث خطأ في حذف المستخدم', 'error');
            }
        }
    }

    // تحديث إحصائيات المستخدمين
    static updateUsersStats() {
        try {
            const users = Database.getUsers() || [];

            const totalUsers = users.length;
            const activeUsers = users.filter(u => u.isActive).length;
            const adminUsers = users.filter(u => u.role === 'admin').length;
            const onlineUsers = 1; // المستخدم الحالي

            // تحديث العناصر إذا كانت موجودة
            const totalUsersEl = document.getElementById('total-users-stat');
            const activeUsersEl = document.getElementById('active-users-stat');
            const adminUsersEl = document.getElementById('admin-users-stat');
            const onlineUsersEl = document.getElementById('online-users-stat');

            if (totalUsersEl) totalUsersEl.textContent = totalUsers;
            if (activeUsersEl) activeUsersEl.textContent = activeUsers;
            if (adminUsersEl) adminUsersEl.textContent = adminUsers;
            if (onlineUsersEl) onlineUsersEl.textContent = onlineUsers;

        } catch (error) {
            console.error('خطأ في تحديث إحصائيات المستخدمين:', error);
        }
    }

    // عرض تبويب إدارة المستخدمين
    static renderUsersTab() {
        return `
            <div class="tab-content" id="users-tab">
                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-users"></i> إدارة المستخدمين والصلاحيات</h3>
                        <p>إدارة شاملة لحسابات المستخدمين وصلاحياتهم في النظام</p>
                        <div class="section-actions">
                            <button class="btn btn-primary" onclick="Settings.showAddUserModal()">
                                <i class="fas fa-user-plus"></i>
                                إضافة مستخدم جديد
                            </button>
                            <button class="btn btn-success" onclick="Settings.showPermissionsModal()">
                                <i class="fas fa-shield-alt"></i>
                                إدارة الصلاحيات
                            </button>
                            <button class="btn btn-info" onclick="Settings.exportUsers()">
                                <i class="fas fa-download"></i>
                                تصدير المستخدمين
                            </button>
                        </div>
                    </div>

                    <!-- إحصائيات المستخدمين -->
                    <div class="users-stats">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-value" id="total-users-stat">0</span>
                                <span class="stat-label">إجمالي المستخدمين</span>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon active">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-value" id="active-users-stat">0</span>
                                <span class="stat-label">المستخدمين النشطين</span>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon admin">
                                <i class="fas fa-user-shield"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-value" id="admin-users-stat">0</span>
                                <span class="stat-label">المديرين</span>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon online">
                                <i class="fas fa-circle"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-value" id="online-users-stat">1</span>
                                <span class="stat-label">متصل الآن</span>
                            </div>
                        </div>
                    </div>

                    <!-- فلاتر البحث -->
                    <div class="users-filters">
                        <div class="filter-group">
                            <label for="user-role-filter">تصفية حسب الدور:</label>
                            <select id="user-role-filter" onchange="Settings.filterUsers()">
                                <option value="">جميع الأدوار</option>
                                <option value="admin">مدير النظام</option>
                                <option value="manager">مدير</option>
                                <option value="technician">فني</option>
                                <option value="user">مستخدم</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="user-status-filter">تصفية حسب الحالة:</label>
                            <select id="user-status-filter" onchange="Settings.filterUsers()">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="user-search">البحث:</label>
                            <input type="search" id="user-search" placeholder="البحث في المستخدمين..."
                                   onkeyup="Settings.searchUsers(this.value)">
                        </div>
                    </div>

                    <div class="users-table-container">
                        <table class="data-table" id="users-table">
                            <thead>
                                <tr>
                                    <th>الصورة</th>
                                    <th>الاسم</th>
                                    <th>اسم المستخدم</th>
                                    <th>الدور</th>
                                    <th>الصلاحيات</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الهاتف</th>
                                    <th>الحالة</th>
                                    <th>آخر دخول</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="users-tbody">
                                ${this.renderUsersTable()}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- قسم إدارة الأدوار والصلاحيات -->
                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-shield-alt"></i> إدارة الأدوار والصلاحيات</h3>
                        <p>تحديد الصلاحيات لكل دور في النظام</p>
                    </div>

                    <div class="roles-management">
                        ${this.renderRolesManagement()}
                    </div>
                </div>
            </div>
        `;
    }

    // عرض تبويب الإشعارات
    static renderNotificationsTab() {
        return `
            <div class="tab-content" id="notifications-tab">
                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-bell"></i> إعدادات الإشعارات</h3>
                        <p>تخصيص أنواع الإشعارات ووسائل الإرسال</p>
                    </div>

                    <div class="notifications-grid">
                        <div class="notification-category">
                            <h4><i class="fas fa-desktop"></i> إشعارات النظام</h4>
                            <div class="notification-options">
                                <label class="notification-option">
                                    <input type="checkbox" name="notify-new-work" checked>
                                    <span class="checkbox-custom"></span>
                                    <div class="option-info">
                                        <span class="option-title">عمل جديد</span>
                                        <span class="option-desc">عند إضافة عمل جديد</span>
                                    </div>
                                </label>

                                <label class="notification-option">
                                    <input type="checkbox" name="notify-work-completed" checked>
                                    <span class="checkbox-custom"></span>
                                    <div class="option-info">
                                        <span class="option-title">اكتمال العمل</span>
                                        <span class="option-desc">عند اكتمال العمل</span>
                                    </div>
                                </label>

                                <label class="notification-option">
                                    <input type="checkbox" name="notify-delivery-due">
                                    <span class="checkbox-custom"></span>
                                    <div class="option-info">
                                        <span class="option-title">موعد التسليم</span>
                                        <span class="option-desc">قبل موعد التسليم بيوم</span>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <div class="notification-category">
                            <h4><i class="fas fa-envelope"></i> إشعارات البريد الإلكتروني</h4>
                            <div class="notification-options">
                                <label class="notification-option">
                                    <input type="checkbox" name="email-daily-report">
                                    <span class="checkbox-custom"></span>
                                    <div class="option-info">
                                        <span class="option-title">التقرير اليومي</span>
                                        <span class="option-desc">إرسال تقرير يومي بالأعمال</span>
                                    </div>
                                </label>

                                <label class="notification-option">
                                    <input type="checkbox" name="email-backup-complete">
                                    <span class="checkbox-custom"></span>
                                    <div class="option-info">
                                        <span class="option-title">اكتمال النسخ الاحتياطي</span>
                                        <span class="option-desc">عند اكتمال النسخ الاحتياطي</span>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <div class="notification-category">
                            <h4><i class="fas fa-sms"></i> الرسائل النصية</h4>
                            <div class="notification-options">
                                <label class="notification-option">
                                    <input type="checkbox" name="sms-work-ready">
                                    <span class="checkbox-custom"></span>
                                    <div class="option-info">
                                        <span class="option-title">العمل جاهز</span>
                                        <span class="option-desc">إشعار الطبيب عند جاهزية العمل</span>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-cog"></i> إعدادات الإرسال</h3>
                        <p>تكوين خوادم البريد الإلكتروني والرسائل النصية</p>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="smtp-server">خادم SMTP</label>
                            <input type="text" id="smtp-server" name="smtpServer" placeholder="smtp.gmail.com">
                        </div>

                        <div class="form-group">
                            <label for="smtp-port">منفذ SMTP</label>
                            <input type="number" id="smtp-port" name="smtpPort" placeholder="587">
                        </div>

                        <div class="form-group">
                            <label for="smtp-username">اسم المستخدم</label>
                            <input type="email" id="smtp-username" name="smtpUsername" placeholder="<EMAIL>">
                        </div>

                        <div class="form-group">
                            <label for="smtp-password">كلمة المرور</label>
                            <input type="password" id="smtp-password" name="smtpPassword">
                        </div>

                        <div class="form-group">
                            <label for="sms-provider">مزود الرسائل النصية</label>
                            <select id="sms-provider" name="smsProvider">
                                <option value="">اختر المزود</option>
                                <option value="twilio">Twilio</option>
                                <option value="nexmo">Nexmo</option>
                                <option value="local">محلي</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="sms-api-key">مفتاح API</label>
                            <input type="text" id="sms-api-key" name="smsApiKey">
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // عرض تبويب الأمان
    static renderSecurityTab() {
        return `
            <div class="tab-content" id="security-tab">
                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-shield-alt"></i> إعدادات الأمان</h3>
                        <p>تكوين إعدادات الأمان وحماية النظام</p>
                    </div>

                    <div class="security-grid">
                        <div class="security-card">
                            <div class="security-card-header">
                                <h4><i class="fas fa-lock"></i> كلمات المرور</h4>
                            </div>
                            <div class="security-card-content">
                                <div class="form-group">
                                    <label for="password-min-length">الحد الأدنى لطول كلمة المرور</label>
                                    <input type="number" id="password-min-length" name="passwordMinLength"
                                           min="4" max="20" value="8">
                                </div>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="requireUppercase" checked>
                                        <span class="checkbox-custom"></span>
                                        يتطلب أحرف كبيرة
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="requireNumbers" checked>
                                        <span class="checkbox-custom"></span>
                                        يتطلب أرقام
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="requireSpecialChars">
                                        <span class="checkbox-custom"></span>
                                        يتطلب رموز خاصة
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="security-card">
                            <div class="security-card-header">
                                <h4><i class="fas fa-clock"></i> الجلسات</h4>
                            </div>
                            <div class="security-card-content">
                                <div class="form-group">
                                    <label for="session-timeout">انتهاء الجلسة (دقائق)</label>
                                    <input type="number" id="session-timeout" name="sessionTimeout"
                                           min="5" max="1440" value="60">
                                </div>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="autoLogout" checked>
                                        <span class="checkbox-custom"></span>
                                        خروج تلقائي عند عدم النشاط
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="rememberLogin">
                                        <span class="checkbox-custom"></span>
                                        السماح بتذكر تسجيل الدخول
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-history"></i> سجل النشاطات</h3>
                        <p>تتبع وتسجيل نشاطات المستخدمين</p>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="logUserActions" checked>
                                <span class="checkbox-custom"></span>
                                تسجيل إجراءات المستخدمين
                            </label>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="logDataChanges" checked>
                                <span class="checkbox-custom"></span>
                                تسجيل تغييرات البيانات
                            </label>
                        </div>

                        <div class="form-group">
                            <label for="log-retention">مدة الاحتفاظ بالسجلات (أيام)</label>
                            <input type="number" id="log-retention" name="logRetention"
                                   min="1" max="365" value="90">
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // عرض تبويب النسخ الاحتياطي
    static renderBackupTab() {
        return `
            <div class="tab-content" id="backup-tab">
                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-database"></i> النسخ الاحتياطي التلقائي</h3>
                        <p>إعدادات النسخ الاحتياطي التلقائي والمجدول</p>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="auto-backup-enabled" name="autoBackupEnabled"
                                       onchange="Settings.toggleAutoBackup(this.checked)">
                                <span class="checkbox-custom"></span>
                                تفعيل النسخ الاحتياطي التلقائي
                            </label>
                        </div>

                        <div class="form-group">
                            <label for="backup-frequency">تكرار النسخ الاحتياطي</label>
                            <select id="backup-frequency" name="backupFrequency">
                                <option value="daily">يومياً</option>
                                <option value="weekly">أسبوعياً</option>
                                <option value="monthly">شهرياً</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="backup-time">وقت النسخ الاحتياطي</label>
                            <input type="time" id="backup-time" name="backupTime" value="02:00">
                        </div>

                        <div class="form-group">
                            <label for="backup-retention">عدد النسخ المحفوظة</label>
                            <input type="number" id="backup-retention" name="backupRetention"
                                   min="1" max="30" value="7">
                        </div>

                        <div class="form-group">
                            <label for="backup-location">مجلد الحفظ</label>
                            <div class="file-input-group">
                                <input type="text" id="backup-location" name="backupLocation"
                                       placeholder="اختر مجلد الحفظ" readonly>
                                <button class="btn btn-secondary" onclick="Settings.selectBackupLocation()">
                                    <i class="fas fa-folder-open"></i>
                                    تصفح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-cloud"></i> النسخ الاحتياطي السحابي</h3>
                        <p>ربط النظام بخدمات التخزين السحابي</p>
                    </div>

                    <div class="cloud-providers">
                        <div class="cloud-provider">
                            <div class="provider-info">
                                <i class="fab fa-google-drive"></i>
                                <span>Google Drive</span>
                            </div>
                            <div class="provider-status">
                                <span class="status-badge disconnected">غير متصل</span>
                                <button class="btn btn-primary btn-sm" onclick="Settings.connectCloudProvider('google')">
                                    ربط
                                </button>
                            </div>
                        </div>

                        <div class="cloud-provider">
                            <div class="provider-info">
                                <i class="fab fa-dropbox"></i>
                                <span>Dropbox</span>
                            </div>
                            <div class="provider-status">
                                <span class="status-badge disconnected">غير متصل</span>
                                <button class="btn btn-primary btn-sm" onclick="Settings.connectCloudProvider('dropbox')">
                                    ربط
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // عرض تبويب النظام
    static renderSystemTab() {
        return `
            <div class="tab-content" id="system-tab">
                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-info-circle"></i> معلومات النظام</h3>
                        <p>معلومات تفصيلية عن النظام والأداء</p>
                    </div>

                    <div class="system-info-grid">
                        <div class="info-card">
                            <div class="info-card-header">
                                <i class="fas fa-desktop"></i>
                                <h4>النظام</h4>
                            </div>
                            <div class="info-card-content">
                                <div class="info-item">
                                    <span class="info-label">إصدار النظام:</span>
                                    <span class="info-value">1.0.0</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">المتصفح:</span>
                                    <span class="info-value" id="browser-info">--</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">نظام التشغيل:</span>
                                    <span class="info-value" id="os-info">--</span>
                                </div>
                            </div>
                        </div>

                        <div class="info-card">
                            <div class="info-card-header">
                                <i class="fas fa-database"></i>
                                <h4>قاعدة البيانات</h4>
                            </div>
                            <div class="info-card-content">
                                <div class="info-item">
                                    <span class="info-label">حجم البيانات:</span>
                                    <span class="info-value" id="data-size">--</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">عدد التركيبات:</span>
                                    <span class="info-value" id="total-prosthetics">--</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">عدد الأطباء:</span>
                                    <span class="info-value" id="total-doctors">--</span>
                                </div>
                            </div>
                        </div>

                        <div class="info-card">
                            <div class="info-card-header">
                                <i class="fas fa-chart-line"></i>
                                <h4>الأداء</h4>
                            </div>
                            <div class="info-card-content">
                                <div class="info-item">
                                    <span class="info-label">استخدام الذاكرة:</span>
                                    <span class="info-value" id="memory-usage">--</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">وقت التحميل:</span>
                                    <span class="info-value" id="load-time">--</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">آخر تحديث:</span>
                                    <span class="info-value" id="last-update">--</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h3><i class="fas fa-tools"></i> أدوات النظام</h3>
                        <p>أدوات الصيانة والتحسين</p>
                    </div>

                    <div class="system-tools">
                        <div class="tool-card">
                            <div class="tool-info">
                                <i class="fas fa-broom"></i>
                                <div>
                                    <h4>تنظيف البيانات</h4>
                                    <p>حذف البيانات المؤقتة والملفات غير المستخدمة</p>
                                </div>
                            </div>
                            <button class="btn btn-warning" onclick="Settings.cleanupData()">
                                <i class="fas fa-broom"></i>
                                تنظيف
                            </button>
                        </div>

                        <div class="tool-card">
                            <div class="tool-info">
                                <i class="fas fa-compress-arrows-alt"></i>
                                <div>
                                    <h4>ضغط البيانات</h4>
                                    <p>ضغط قاعدة البيانات لتوفير المساحة</p>
                                </div>
                            </div>
                            <button class="btn btn-info" onclick="Settings.compressData()">
                                <i class="fas fa-compress-arrows-alt"></i>
                                ضغط
                            </button>
                        </div>

                        <div class="tool-card">
                            <div class="tool-info">
                                <i class="fas fa-check-double"></i>
                                <div>
                                    <h4>فحص سلامة البيانات</h4>
                                    <p>التحقق من سلامة قاعدة البيانات</p>
                                </div>
                            </div>
                            <button class="btn btn-success" onclick="Settings.checkDataIntegrity()">
                                <i class="fas fa-check-double"></i>
                                فحص
                            </button>
                        </div>

                        <div class="tool-card">
                            <div class="tool-info">
                                <i class="fas fa-exclamation-triangle"></i>
                                <div>
                                    <h4>إعادة تعيين النظام</h4>
                                    <p>إعادة تعيين جميع الإعدادات للقيم الافتراضية</p>
                                </div>
                            </div>
                            <button class="btn btn-danger" onclick="Settings.resetSystem()">
                                <i class="fas fa-exclamation-triangle"></i>
                                إعادة تعيين
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // عرض جدول المستخدمين
    static renderUsersTable(roleFilter = '', statusFilter = '', searchTerm = '') {
        let users = Database.getUsers() || [];

        // تطبيق الفلاتر
        if (roleFilter) {
            users = users.filter(user => user.role === roleFilter);
        }

        if (statusFilter) {
            const isActive = statusFilter === 'active';
            users = users.filter(user => user.isActive === isActive);
        }

        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            users = users.filter(user =>
                user.name.toLowerCase().includes(term) ||
                user.username.toLowerCase().includes(term) ||
                (user.email && user.email.toLowerCase().includes(term))
            );
        }

        // إذا كان هناك عنصر tbody، حدثه مباشرة
        const tbody = document.getElementById('users-tbody');
        if (tbody) {
            tbody.innerHTML = this.generateUsersTableRows(users);
            return '';
        }

        // وإلا أرجع HTML للاستخدام في render
        return this.generateUsersTableRows(users);
    }

    // إنشاء صفوف جدول المستخدمين
    static generateUsersTableRows(users) {

        if (users.length === 0) {
            return `
                <tr>
                    <td colspan="10" style="text-align: center; padding: 40px; color: #718096;">
                        <i class="fas fa-users" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                        <div>لا توجد مستخدمين مطابقين للبحث</div>
                    </td>
                </tr>
            `;
        }

        return users.map(user => `
            <tr>
                <td>
                    <div class="user-avatar">
                        ${user.avatar ? `<img src="${user.avatar}" alt="${user.name}">` :
                          `<span class="avatar-initials">${this.getInitials(user.name)}</span>`}
                    </div>
                </td>
                <td>${user.name}</td>
                <td>${user.username}</td>
                <td>
                    <span class="role-badge ${user.role}">
                        ${this.getRoleDisplayName(user.role)}
                    </span>
                </td>
                <td>
                    <div class="user-permissions">
                        ${this.renderUserPermissions(user)}
                    </div>
                </td>
                <td>${user.email || '--'}</td>
                <td>${user.phone || '--'}</td>
                <td>
                    <span class="status-badge ${user.isActive ? 'active' : 'inactive'}">
                        ${user.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td>${user.lastLogin ? Utils.formatDate(user.lastLogin) : 'لم يسجل دخول'}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="Settings.editUser('${user.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="Settings.resetUserPassword('${user.id}')" title="إعادة تعيين كلمة المرور">
                            <i class="fas fa-key"></i>
                        </button>
                        <button class="btn btn-sm btn-${user.isActive ? 'secondary' : 'success'}"
                                onclick="Settings.toggleUserStatus('${user.id}')"
                                title="${user.isActive ? 'إلغاء التفعيل' : 'تفعيل'}">
                            <i class="fas fa-${user.isActive ? 'ban' : 'check'}"></i>
                        </button>
                        ${user.id !== 'admin' ? `
                            <button class="btn btn-sm btn-danger" onclick="Settings.deleteUser('${user.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `).join('');
    }

    // الحصول على الأحرف الأولى من الاسم
    static getInitials(name) {
        return name.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2);
    }

    // الحصول على اسم الدور للعرض
    static getRoleDisplayName(role) {
        const roleNames = {
            admin: 'مدير النظام',
            manager: 'مدير',
            technician: 'فني',
            user: 'مستخدم'
        };
        return roleNames[role] || role;
    }

    // تحميل جميع البيانات
    static loadAllData() {
        console.log('📦 تحميل جميع بيانات الإعدادات...');

        try {
            this.loadGeneralSettings();
            this.loadAppearanceSettings();
            this.loadNotificationSettings();
            this.loadSecuritySettings();
            this.loadSystemInfo();

            console.log('✅ تم تحميل جميع البيانات');
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات:', error);
            Utils.showNotification('حدث خطأ في تحميل بعض البيانات', 'warning');
        }
    }

    // تحميل الإعدادات العامة
    static loadGeneralSettings() {
        const settings = this.currentSettings;

        this.setFieldValue('lab-name', settings.labName);
        this.setFieldValue('lab-address', settings.address);
        this.setFieldValue('lab-phone1', settings.phones?.[0]);
        this.setFieldValue('lab-phone2', settings.phones?.[1]);
        this.setFieldValue('lab-phone3', settings.phones?.[2]);
        this.setFieldValue('lab-email', settings.email);
        this.setFieldValue('lab-website', settings.website);
        this.setFieldValue('lab-currency', settings.currency);
        this.setFieldValue('tax-rate', settings.taxRate);
        this.setFieldValue('default-language', settings.defaultLanguage);
        this.setFieldValue('date-format', settings.dateFormat);
        this.setFieldValue('time-format', settings.timeFormat);
        this.setFieldValue('work-hours-start', settings.workHoursStart);
        this.setFieldValue('work-hours-end', settings.workHoursEnd);
        this.setFieldValue('default-delivery-days', settings.defaultDeliveryDays);
    }

    // تحميل إعدادات المظهر
    static loadAppearanceSettings() {
        const settings = this.currentSettings;

        this.setFieldValue('color-primary', settings.colorPrimary || '#2563eb');
        this.setFieldValue('color-secondary', settings.colorSecondary || '#64748b');
        this.setFieldValue('color-accent', settings.colorAccent || '#7c3aed');
        this.setFieldValue('color-success', settings.colorSuccess || '#10b981');
        this.setFieldValue('color-warning', settings.colorWarning || '#f59e0b');
        this.setFieldValue('color-danger', settings.colorDanger || '#ef4444');
        this.setFieldValue('theme-mode', settings.themeMode || 'light');
        this.setFieldValue('font-size', settings.fontSize || 'medium');
        this.setFieldValue('sidebar-style', settings.sidebarStyle || 'expanded');
        this.setFieldValue('animation-speed', settings.animationSpeed || 'normal');

        // تحديث معاينة الألوان
        this.updateColorPreview();
    }

    // تحميل إعدادات الإشعارات
    static loadNotificationSettings() {
        const settings = this.currentSettings;

        this.setCheckboxValue('notify-new-work', settings.notifyNewWork !== false);
        this.setCheckboxValue('notify-work-completed', settings.notifyWorkCompleted !== false);
        this.setCheckboxValue('notify-delivery-due', settings.notifyDeliveryDue);
        this.setCheckboxValue('email-daily-report', settings.emailDailyReport);
        this.setCheckboxValue('email-backup-complete', settings.emailBackupComplete);
        this.setCheckboxValue('sms-work-ready', settings.smsWorkReady);

        this.setFieldValue('smtp-server', settings.smtpServer);
        this.setFieldValue('smtp-port', settings.smtpPort);
        this.setFieldValue('smtp-username', settings.smtpUsername);
        this.setFieldValue('smtp-password', settings.smtpPassword);
        this.setFieldValue('sms-provider', settings.smsProvider);
        this.setFieldValue('sms-api-key', settings.smsApiKey);
    }

    // تحميل إعدادات الأمان
    static loadSecuritySettings() {
        const settings = this.currentSettings;

        this.setFieldValue('password-min-length', settings.passwordMinLength || 8);
        this.setCheckboxValue('requireUppercase', settings.requireUppercase !== false);
        this.setCheckboxValue('requireNumbers', settings.requireNumbers !== false);
        this.setCheckboxValue('requireSpecialChars', settings.requireSpecialChars);
        this.setFieldValue('session-timeout', settings.sessionTimeout || 60);
        this.setCheckboxValue('autoLogout', settings.autoLogout !== false);
        this.setCheckboxValue('rememberLogin', settings.rememberLogin);
        this.setCheckboxValue('logUserActions', settings.logUserActions !== false);
        this.setCheckboxValue('logDataChanges', settings.logDataChanges !== false);
        this.setFieldValue('log-retention', settings.logRetention || 90);
    }

    // تحميل معلومات النظام
    static loadSystemInfo() {
        try {
            // معلومات المتصفح
            const browserInfo = this.getBrowserInfo();
            this.setElementText('browser-info', browserInfo);

            // معلومات نظام التشغيل
            const osInfo = this.getOSInfo();
            this.setElementText('os-info', osInfo);

            // حجم البيانات
            const dataSize = this.formatDataSize(this.calculateDataSize());
            this.setElementText('data-size', dataSize);

            // إحصائيات البيانات
            const prosthetics = Database.getProsthetics() || [];
            const doctors = Database.getDoctors() || [];

            this.setElementText('total-prosthetics', prosthetics.length);
            this.setElementText('total-doctors', doctors.length);

            // معلومات الأداء
            this.setElementText('memory-usage', this.getMemoryUsage());
            this.setElementText('load-time', this.getLoadTime());

            // تنسيق التاريخ والوقت مع معالجة الأخطاء
            try {
                const lastUpdate = Utils.formatDateTime ?
                    Utils.formatDateTime(new Date()) :
                    Utils.formatDate(new Date(), 'DD/MM/YYYY HH:mm');
                this.setElementText('last-update', lastUpdate);
            } catch (dateError) {
                console.warn('خطأ في تنسيق التاريخ:', dateError);
                this.setElementText('last-update', new Date().toLocaleString('ar-EG'));
            }

            console.log('✅ تم تحميل معلومات النظام بنجاح');

        } catch (error) {
            console.error('❌ خطأ في تحميل معلومات النظام:', error);
            // عرض رسالة خطأ للمستخدم
            Utils.showNotification('حدث خطأ في تحميل معلومات النظام', 'warning');
        }
    }

    // وظائف مساعدة لتعيين القيم
    static setFieldValue(id, value) {
        const element = document.getElementById(id);
        if (element && value !== undefined && value !== null) {
            element.value = value;
        }
    }

    static setCheckboxValue(name, checked) {
        const element = document.querySelector(`[name="${name}"]`);
        if (element) {
            element.checked = !!checked;
        }
    }

    static setElementText(id, text) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = text || '--';
        }
    }

    // معالج تغيير الحقول
    static handleFieldChange(field) {
        const name = field.name || field.id;
        const value = field.type === 'checkbox' ? field.checked : field.value;

        // تسجيل التغيير
        this.recordChange(name, value);

        // تحديث الإعدادات المحلية
        this.currentSettings[name] = value;

        // تطبيق التغيير فوراً إذا كان مناسباً
        this.applyInstantChange(name, value);
    }

    // تسجيل التغيير في السجل
    static recordChange(field, value) {
        const change = {
            field,
            value,
            timestamp: new Date().toISOString(),
            user: Auth.currentUser?.name || 'مجهول'
        };

        this.changeHistory.unshift(change);

        // الاحتفاظ بآخر 100 تغيير فقط
        if (this.changeHistory.length > 100) {
            this.changeHistory = this.changeHistory.slice(0, 100);
        }
    }

    // تطبيق التغيير الفوري
    static applyInstantChange(field, value) {
        switch (field) {
            case 'theme-mode':
                this.changeThemeMode(value);
                break;
            case 'font-size':
                this.changeFontSize(value);
                break;
            case 'color-primary':
            case 'color-secondary':
            case 'color-accent':
                this.updateColorPreview();
                break;
        }
    }

    // حفظ جميع الإعدادات
    static saveAllSettings() {
        try {
            // جمع البيانات من جميع النماذج
            const formData = this.collectAllFormData();

            // التحقق من صحة البيانات
            if (!this.validateAllData(formData)) {
                return;
            }

            // دمج البيانات مع الإعدادات الحالية
            const updatedSettings = { ...this.currentSettings, ...formData };

            // حفظ في قاعدة البيانات
            const success = Database.saveSettings(updatedSettings);

            if (success) {
                this.currentSettings = updatedSettings;
                Utils.showNotification('تم حفظ جميع الإعدادات بنجاح', 'success');

                // تطبيق الإعدادات
                this.applyAllSettings();

                console.log('✅ تم حفظ الإعدادات بنجاح');
            } else {
                Utils.showNotification('حدث خطأ في حفظ الإعدادات', 'error');
            }

        } catch (error) {
            console.error('❌ خطأ في حفظ الإعدادات:', error);
            Utils.showNotification('حدث خطأ في حفظ الإعدادات', 'error');
        }
    }

    // جمع البيانات من جميع النماذج
    static collectAllFormData() {
        const formData = {};

        // جمع البيانات من جميع الحقول
        document.querySelectorAll('.settings-container input, .settings-container select, .settings-container textarea').forEach(field => {
            const name = field.name || field.id;
            if (name) {
                if (field.type === 'checkbox') {
                    formData[name] = field.checked;
                } else if (field.type === 'file') {
                    // معالجة الملفات بشكل منفصل
                } else {
                    formData[name] = field.value;
                }
            }
        });

        return formData;
    }

    // التحقق من صحة جميع البيانات
    static validateAllData(data) {
        const errors = [];

        // التحقق من الحقول المطلوبة
        if (!data.labName || data.labName.trim().length < 3) {
            errors.push('اسم المعمل مطلوب ويجب أن يكون 3 أحرف على الأقل');
        }

        if (!data.currency) {
            errors.push('العملة مطلوبة');
        }

        // التحقق من البريد الإلكتروني
        if (data.email && !this.validationRules.email.pattern.test(data.email)) {
            errors.push('البريد الإلكتروني غير صحيح');
        }

        // التحقق من الموقع الإلكتروني
        if (data.website && !this.validationRules.website.pattern.test(data.website)) {
            errors.push('الموقع الإلكتروني غير صحيح');
        }

        // عرض الأخطاء
        if (errors.length > 0) {
            Utils.showNotification(errors.join('\n'), 'error');
            return false;
        }

        return true;
    }

    // تطبيق جميع الإعدادات
    static applyAllSettings() {
        // تطبيق إعدادات المظهر
        this.applyColors();
        this.changeThemeMode(this.currentSettings.themeMode);
        this.changeFontSize(this.currentSettings.fontSize);

        // تحديث معلومات المعمل في الواجهة
        if (typeof ThemeManager !== 'undefined') {
            ThemeManager.updateLabInfo();
        }

        console.log('✅ تم تطبيق جميع الإعدادات');
    }

    // الحفظ التلقائي
    static autoSaveSettings() {
        if (Object.keys(this.currentSettings).length > 0) {
            Database.saveSettings(this.currentSettings);
            console.log('💾 تم الحفظ التلقائي للإعدادات');
        }
    }

    // معلومات المتصفح
    static getBrowserInfo() {
        const ua = navigator.userAgent;
        let browser = 'Unknown';

        if (ua.includes('Chrome')) browser = 'Chrome';
        else if (ua.includes('Firefox')) browser = 'Firefox';
        else if (ua.includes('Safari')) browser = 'Safari';
        else if (ua.includes('Edge')) browser = 'Edge';

        return browser;
    }

    // معلومات نظام التشغيل
    static getOSInfo() {
        const ua = navigator.userAgent;
        let os = 'Unknown';

        if (ua.includes('Windows')) os = 'Windows';
        else if (ua.includes('Mac')) os = 'macOS';
        else if (ua.includes('Linux')) os = 'Linux';
        else if (ua.includes('Android')) os = 'Android';
        else if (ua.includes('iOS')) os = 'iOS';

        return os;
    }

    // استخدام الذاكرة
    static getMemoryUsage() {
        if (performance.memory) {
            const used = Math.round(performance.memory.usedJSHeapSize / 1048576);
            const total = Math.round(performance.memory.totalJSHeapSize / 1048576);
            return `${used} / ${total} MB`;
        }
        return 'غير متاح';
    }

    // وقت التحميل
    static getLoadTime() {
        if (performance.timing) {
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            return `${loadTime} ms`;
        }
        return 'غير متاح';
    }

    // وظائف التفاعل مع الواجهة

    // تحديث معاينة الألوان
    static updateColorPreview() {
        const primary = document.getElementById('color-primary')?.value || '#2563eb';
        const secondary = document.getElementById('color-secondary')?.value || '#64748b';
        const accent = document.getElementById('color-accent')?.value || '#7c3aed';

        // تحديث حقول النص
        document.getElementById('color-primary-hex').value = primary;
        document.getElementById('color-secondary-hex').value = secondary;
        document.getElementById('color-accent-hex').value = accent;

        // تحديث المعاينة
        const previewHeader = document.getElementById('preview-header');
        const previewPrimary = document.getElementById('preview-primary');
        const previewSecondary = document.getElementById('preview-secondary');

        if (previewHeader) previewHeader.style.backgroundColor = primary;
        if (previewPrimary) previewPrimary.style.backgroundColor = primary;
        if (previewSecondary) previewSecondary.style.backgroundColor = secondary;
    }

    // تحديث اللون من النص
    static updateColorFromHex(input, colorInputId) {
        const colorInput = document.getElementById(colorInputId);
        if (colorInput && input.value.match(/^#[0-9A-F]{6}$/i)) {
            colorInput.value = input.value;
            this.updateColorPreview();
        }
    }

    // تطبيق الألوان
    static applyColors() {
        const colors = {
            primary: document.getElementById('color-primary')?.value,
            secondary: document.getElementById('color-secondary')?.value,
            accent: document.getElementById('color-accent')?.value,
            success: document.getElementById('color-success')?.value,
            warning: document.getElementById('color-warning')?.value,
            danger: document.getElementById('color-danger')?.value
        };

        // تطبيق الألوان على CSS
        const root = document.documentElement;
        if (colors.primary) root.style.setProperty('--primary-color', colors.primary);
        if (colors.secondary) root.style.setProperty('--secondary-color', colors.secondary);
        if (colors.accent) root.style.setProperty('--accent-color', colors.accent);
        if (colors.success) root.style.setProperty('--success-color', colors.success);
        if (colors.warning) root.style.setProperty('--warning-color', colors.warning);
        if (colors.danger) root.style.setProperty('--danger-color', colors.danger);

        Utils.showNotification('تم تطبيق الألوان بنجاح', 'success');
    }

    // إعادة تعيين الألوان
    static resetColors() {
        const defaultColors = {
            'color-primary': '#2563eb',
            'color-secondary': '#64748b',
            'color-accent': '#7c3aed',
            'color-success': '#10b981',
            'color-warning': '#f59e0b',
            'color-danger': '#ef4444'
        };

        Object.keys(defaultColors).forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.value = defaultColors[id];
            }
        });

        this.updateColorPreview();
        this.applyColors();
    }

    // تغيير وضع المظهر
    static changeThemeMode(mode) {
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add(`theme-${mode}`);

        if (mode === 'auto') {
            const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            document.body.classList.add(isDark ? 'theme-dark' : 'theme-light');
        }
    }

    // تغيير حجم الخط
    static changeFontSize(size) {
        document.body.className = document.body.className.replace(/font-size-\w+/g, '');
        document.body.classList.add(`font-size-${size}`);
    }

    // تصدير الإعدادات
    static exportSettings() {
        try {
            const settings = this.currentSettings;
            const dataStr = JSON.stringify(settings, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `dental-lab-settings-${Utils.formatDate(new Date())}.json`;
            link.click();

            Utils.showNotification('تم تصدير الإعدادات بنجاح', 'success');
        } catch (error) {
            console.error('خطأ في تصدير الإعدادات:', error);
            Utils.showNotification('حدث خطأ في تصدير الإعدادات', 'error');
        }
    }

    // استيراد الإعدادات
    static importSettings() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';

        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const settings = JSON.parse(e.target.result);
                        this.currentSettings = { ...this.currentSettings, ...settings };
                        this.loadAllData();
                        this.applyAllSettings();
                        Utils.showNotification('تم استيراد الإعدادات بنجاح', 'success');
                    } catch (error) {
                        Utils.showNotification('ملف الإعدادات غير صحيح', 'error');
                    }
                };
                reader.readAsText(file);
            }
        };

        input.click();
    }

    // إعادة تعيين افتراضي
    static resetToDefaults() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟\nسيتم فقدان جميع التخصيصات الحالية.')) {
            // إعادة تعيين الإعدادات
            this.currentSettings = {};
            Database.resetSettings();

            // إعادة تحميل الصفحة
            location.reload();
        }
    }

    // عرض سجل التغييرات
    static showChangeHistory() {
        const historyHtml = this.changeHistory.map(change => `
            <div class="change-item">
                <div class="change-field">${change.field}</div>
                <div class="change-value">${change.value}</div>
                <div class="change-time">${Utils.formatDateTime(change.timestamp)}</div>
                <div class="change-user">${change.user}</div>
            </div>
        `).join('');

        Utils.showModal('سجل التغييرات', `
            <div class="change-history">
                <div class="change-header">
                    <div>الحقل</div>
                    <div>القيمة</div>
                    <div>الوقت</div>
                    <div>المستخدم</div>
                </div>
                <div class="change-list">
                    ${historyHtml || '<div class="no-changes">لا توجد تغييرات مسجلة</div>'}
                </div>
            </div>
        `);
    }

    // إعادة تعيين صلاحيات المستخدمين
    static resetUserPermissions() {
        if (confirm('هل أنت متأكد من إعادة تعيين صلاحيات جميع المديرين؟\nسيتم إعطاء جميع المديرين صلاحيات كاملة.')) {
            if (typeof Auth !== 'undefined' && Auth.resetAllUserPermissions) {
                Auth.resetAllUserPermissions();

                // إعادة تحميل جدول المستخدمين
                setTimeout(() => {
                    this.loadAllData();
                }, 1000);
            } else {
                Utils.showNotification('وظيفة إعادة تعيين الصلاحيات غير متاحة', 'error');
            }
        }
    }

    // فحص صلاحيات المستخدم الحالي
    static checkCurrentUserPermissions() {
        if (typeof Auth !== 'undefined' && Auth.checkPermissionDetailed) {
            const permissions = [
                'manage_settings',
                'manage_users',
                'view_dashboard',
                'manage_prosthetics'
            ];

            console.log('🔍 فحص صلاحيات المستخدم الحالي:');
            permissions.forEach(permission => {
                Auth.checkPermissionDetailed(permission);
            });

            Utils.showNotification('تم فحص الصلاحيات - راجع وحدة التحكم للتفاصيل', 'info');
        }
    }

    // إظهار رسالة ترحيب بالصلاحيات
    static showPermissionsWelcome() {
        setTimeout(() => {
            if (typeof Auth !== 'undefined' && Auth.getCurrentUser) {
                const user = Auth.getCurrentUser();
                if (user) {
                    const hasSettingsPermission = Auth.hasPermission('manage_settings');

                    if (hasSettingsPermission) {
                        Utils.showNotification(
                            `مرحباً ${user.name}! لديك صلاحيات كاملة للوصول إلى الإعدادات`,
                            'success',
                            4000
                        );
                        console.log('✅ المستخدم لديه صلاحيات الإعدادات');
                    } else {
                        Utils.showNotification(
                            'تحذير: ليس لديك صلاحيات كافية لتعديل الإعدادات',
                            'warning',
                            5000
                        );
                        console.log('⚠️ المستخدم ليس لديه صلاحيات الإعدادات');
                    }
                }
            }
        }, 1000);
    }
}

// التأكد من أن الكلاس متاح عالمياً
window.Settings = Settings;

// تهيئة فورية إذا كان DOM جاهز
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        if (typeof Settings !== 'undefined') {
            Settings.init();
        }
    });
} else {
    if (typeof Settings !== 'undefined') {
        Settings.init();
    }
}

console.log('✅ تم تحميل ملف settings.js المحدث بنجاح');
