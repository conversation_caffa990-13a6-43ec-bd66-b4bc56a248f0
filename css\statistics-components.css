/* أنماط الإحصائيات الموحدة - تصميم حديث وجميل */

/* رأس الإدارة الموحد */
.department-header {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 50%, #c084fc 100%);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    color: white;
    box-shadow: 0 15px 30px rgba(139, 92, 246, 0.2);
    position: relative;
    overflow: hidden;
}

.department-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.05) 100%);
    opacity: 0.8;
}

.department-header::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: shimmer 8s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% {
        transform: rotate(0deg) scale(1);
        opacity: 0.3;
    }
    50% {
        transform: rotate(180deg) scale(1.1);
        opacity: 0.1;
    }
}

.header-main-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
    gap: 2rem;
}

.header-text-section {
    flex: 1;
}

.department-logo-title {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.department-logo {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
    border: 2px solid rgba(255, 255, 255, 0.4);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
}

.department-logo::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s;
}

.department-logo:hover::before {
    left: 100%;
}

.department-logo:hover {
    transform: scale(1.05) rotate(5deg);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.2) 100%);
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.title-text {
    flex: 1;
}

.department-title {
    margin: 0 0 0.25rem 0;
    font-size: 1.8rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    letter-spacing: -0.5px;
    color: #ffffff;
    position: relative;
    line-height: 1.2;
}

.department-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, #ffffff, transparent);
    border-radius: 2px;
}

.department-description {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 500;
    opacity: 0.9;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    line-height: 1.3;
    color: rgba(255, 255, 255, 0.85);
}

/* زر التحميل */
.download-section {
    position: relative;
}

.download-btn {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    color: white;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.download-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s;
}

.download-btn:hover::before {
    left: 100%;
}

.download-btn:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.35) 0%, rgba(255, 255, 255, 0.2) 100%);
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 15px 30px rgba(0,0,0,0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.download-btn:active {
    transform: translateY(-2px) scale(1.02);
}

/* قسم الإحصائيات */
.header-stats-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
}

.header-stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
}

/* مربعات الإحصائيات */
.stat-box {
    flex: 1;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 1rem 0.75rem;
    text-align: center;
    min-width: 100px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.stat-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ffffff, #f3e8ff, #e9d5ff, #ffffff);
    transform: scaleX(0);
    transform-origin: center;
    transition: transform 0.5s ease;
}

.stat-box::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    transition: all 0.5s ease;
    border-radius: 50%;
}

.stat-box:hover::before {
    transform: scaleX(1);
}

.stat-box:hover::after {
    width: 200px;
    height: 200px;
}

.stat-box:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.15) 100%);
    transform: translateY(-8px) scale(1.03);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    border-color: rgba(255, 255, 255, 0.5);
}

.stat-box:active {
    transform: translateY(-4px) scale(1.01);
}

/* قيم الإحصائيات */
.stat-value {
    font-size: 1.5rem;
    font-weight: 800;
    line-height: 1;
    margin-bottom: 0.25rem;
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    background: linear-gradient(135deg, #ffffff 0%, #f3e8ff 50%, #e9d5ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.stat-box:hover .stat-value {
    transform: scale(1.1);
    text-shadow: 0 6px 12px rgba(0,0,0,0.4);
}

/* تسميات الإحصائيات */
.stat-name {
    font-size: 0.7rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1.2;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.stat-box:hover .stat-name {
    color: white;
    letter-spacing: 1.2px;
}

/* ألوان مخصصة لكل نوع إحصائية */
.stat-box.total {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(30, 64, 175, 0.1) 100%);
    border-color: rgba(59, 130, 246, 0.3);
}

.stat-box.total:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.3) 0%, rgba(30, 64, 175, 0.2) 100%);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.2);
}

.stat-box.pending {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.2) 0%, rgba(217, 119, 6, 0.1) 100%);
    border-color: rgba(245, 158, 11, 0.3);
}

.stat-box.pending:hover {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.3) 0%, rgba(217, 119, 6, 0.2) 100%);
    box-shadow: 0 20px 40px rgba(245, 158, 11, 0.2);
}

.stat-box.completed {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(5, 150, 105, 0.1) 100%);
    border-color: rgba(16, 185, 129, 0.3);
}

.stat-box.completed:hover {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.3) 0%, rgba(5, 150, 105, 0.2) 100%);
    box-shadow: 0 20px 40px rgba(16, 185, 129, 0.2);
}

.stat-box.revenue {
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.2) 0%, rgba(8, 145, 178, 0.1) 100%);
    border-color: rgba(6, 182, 212, 0.3);
}

.stat-box.revenue:hover {
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.3) 0%, rgba(8, 145, 178, 0.2) 100%);
    box-shadow: 0 20px 40px rgba(6, 182, 212, 0.2);
}

/* تأثيرات إضافية للإحصائيات */
.stat-box.pulse {
    animation: pulse-glow 3s ease-in-out infinite;
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
    }
    50% {
        box-shadow: 0 0 30px rgba(255, 255, 255, 0.2);
    }
}

.stat-box.highlight {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 193, 7, 0.1) 100%);
    border-color: rgba(255, 215, 0, 0.4);
}

.stat-box.highlight:hover {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.3) 0%, rgba(255, 193, 7, 0.2) 100%);
    box-shadow: 0 20px 40px rgba(255, 215, 0, 0.3);
}

/* أيقونات الإحصائيات */
.stat-icon {
    width: 30px;
    height: 30px;
    border-radius: 8px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    color: white;
    margin: 0 auto 0.5rem auto;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s;
}

.stat-box:hover .stat-icon::before {
    left: 100%;
}

.stat-box:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

/* التجاوب للشاشات المختلفة */
@media (max-width: 1200px) {
    .department-logo {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
        border-radius: 14px;
    }

    .department-logo-title {
        gap: 0.75rem;
    }

    .department-title {
        font-size: 1.6rem;
    }

    .department-description {
        font-size: 0.85rem;
    }

    .stat-value {
        font-size: 1.3rem;
    }

    .stat-box {
        min-width: 90px;
        padding: 0.75rem 0.5rem;
    }
}

@media (max-width: 1024px) {
    .header-main-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .department-logo-title {
        justify-content: center;
        gap: 0.75rem;
    }

    .department-logo {
        width: 45px;
        height: 45px;
        font-size: 1.3rem;
        border-radius: 12px;
    }

    .header-stats-section {
        flex-wrap: wrap;
        gap: 0.75rem;
        justify-content: center;
        margin-top: 1.25rem;
        padding-top: 1.25rem;
    }

    .stat-box {
        min-width: 85px;
        padding: 0.75rem 0.5rem;
    }

    .stat-value {
        font-size: 1.2rem;
    }

    .department-title {
        font-size: 1.5rem;
    }

    .department-description {
        font-size: 0.8rem;
    }

    .download-btn {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
}

@media (max-width: 768px) {
    .department-header {
        padding: 1.25rem;
        border-radius: 12px;
    }

    .department-title {
        font-size: 1.4rem;
    }

    .department-description {
        font-size: 0.85rem;
    }

    .header-stats-section {
        flex-direction: column;
        gap: 0.75rem;
        margin-top: 1rem;
        padding-top: 1rem;
    }

    .stat-box {
        min-width: auto;
        flex: none;
        padding: 0.75rem;
        width: 100%;
        max-width: 150px;
        margin: 0 auto;
    }

    .stat-value {
        font-size: 1.1rem;
    }

    .stat-name {
        font-size: 0.65rem;
    }

    .download-btn {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .department-header {
        padding: 1rem;
        border-radius: 10px;
    }

    .department-title {
        font-size: 1.2rem;
    }

    .department-description {
        font-size: 0.8rem;
    }

    .stat-box {
        padding: 0.5rem;
        max-width: 120px;
    }

    .stat-value {
        font-size: 1rem;
    }

    .stat-name {
        font-size: 0.6rem;
        letter-spacing: 0.3px;
    }

    .download-btn {
        width: 28px;
        height: 28px;
        font-size: 0.7rem;
    }

    .stat-icon {
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
        margin-bottom: 0.25rem;
    }
}

/* تأثيرات خاصة للحالات المختلفة */
.stat-box.loading {
    opacity: 0.7;
    pointer-events: none;
}

.stat-box.loading .stat-value {
    animation: loading-pulse 1.5s ease-in-out infinite;
}

@keyframes loading-pulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.stat-box.error {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(220, 38, 38, 0.1) 100%);
    border-color: rgba(239, 68, 68, 0.3);
}

.stat-box.error:hover {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.3) 0%, rgba(220, 38, 38, 0.2) 100%);
    box-shadow: 0 20px 40px rgba(239, 68, 68, 0.2);
}

.stat-box.success {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(21, 128, 61, 0.1) 100%);
    border-color: rgba(34, 197, 94, 0.3);
}

.stat-box.success:hover {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.3) 0%, rgba(21, 128, 61, 0.2) 100%);
    box-shadow: 0 20px 40px rgba(34, 197, 94, 0.2);
}
