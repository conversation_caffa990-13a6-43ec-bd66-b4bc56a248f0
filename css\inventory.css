/* أنماط إدارة المخزن */

/* إحصائيات المخزون */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-card.warning {
    border-left: 4px solid #ff6b6b;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 1.5rem;
}

.stat-card.warning .stat-icon {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.stat-content h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
}

.stat-content p {
    margin: 5px 0 0 0;
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* أدوات التحكم */
.inventory-controls {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #bdc3c7;
}

.search-box input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #3498db;
}

.filter-controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.filter-controls select {
    padding: 10px 15px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    background: white;
    font-size: 0.9rem;
    min-width: 150px;
}

/* جدول المخزون */
.inventory-table-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.inventory-table {
    width: 100%;
    border-collapse: collapse;
}

.inventory-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 12px;
    text-align: right;
    font-weight: 600;
    font-size: 0.9rem;
}

.inventory-table td {
    padding: 12px;
    border-bottom: 1px solid #ecf0f1;
    text-align: right;
    vertical-align: middle;
}

.inventory-table tr:hover {
    background: #f8f9fa;
}

.inventory-table tr:last-child td {
    border-bottom: none;
}

/* حالة المخزون */
.status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    display: inline-block;
    min-width: 80px;
}

.status-normal {
    background: #d4edda;
    color: #155724;
}

.status-warning {
    background: #fff3cd;
    color: #856404;
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.btn-icon {
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.btn-icon:hover {
    transform: scale(1.1);
}

.btn-icon:first-child {
    background: #3498db;
    color: white;
}

.btn-icon:first-child:hover {
    background: #2980b9;
}

.btn-icon:last-child {
    background: #e74c3c;
    color: white;
}

.btn-icon:last-child:hover {
    background: #c0392b;
}

/* حالة فارغة */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #7f8c8d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-state p {
    font-size: 1.1rem;
    margin: 0;
}

/* نماذج المخزون */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* تجاوب الشاشات الصغيرة */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .inventory-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .filter-controls {
        justify-content: space-between;
    }
    
    .inventory-table-container {
        overflow-x: auto;
    }
    
    .inventory-table {
        min-width: 800px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
}

/* تحسينات إضافية */
.btn-outline {
    background: transparent;
    border: 2px solid #3498db;
    color: #3498db;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-outline:hover {
    background: #3498db;
    color: white;
}

/* تحسين عرض الأرقام */
.inventory-table td:nth-child(3),
.inventory-table td:nth-child(5),
.inventory-table td:nth-child(6) {
    text-align: center;
    font-weight: 600;
}

/* تمييز الصفوف منخفضة المخزون */
.inventory-table tr.low-stock {
    background: #fff5f5;
}

.inventory-table tr.low-stock:hover {
    background: #fed7d7;
}
