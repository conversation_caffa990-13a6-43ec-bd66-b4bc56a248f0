// نسخة مبسطة وآمنة من تأثيرات الإحصائيات

class SimpleStatistics {
    
    // تهيئة بسيطة وآمنة
    static init() {
        if (this.initialized) return;
        this.initialized = true;
        
        try {
            this.addBasicEffects();
        } catch (error) {
            console.warn('خطأ في تهيئة الإحصائيات البسيطة:', error);
        }
    }
    
    // إضافة تأثيرات أساسية فقط
    static addBasicEffects() {
        const statBoxes = document.querySelectorAll('.stat-box:not(.simple-initialized)');
        
        statBoxes.forEach(box => {
            box.classList.add('simple-initialized');
            
            // تأثير بسيط عند التمرير
            box.addEventListener('mouseenter', () => {
                box.style.transform = 'translateY(-3px)';
                box.style.transition = 'all 0.3s ease';
            });
            
            box.addEventListener('mouseleave', () => {
                box.style.transform = '';
            });
            
            // تأثير بسيط عند النقر
            box.addEventListener('click', () => {
                box.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    box.style.transform = '';
                }, 150);
            });
        });
    }
    
    // تحديث بسيط للقيم
    static updateStats(data) {
        if (!data) return;
        
        try {
            if (data.totalWorks !== undefined) {
                this.updateValue('totalWorks', data.totalWorks);
            }
            if (data.pendingWorks !== undefined) {
                this.updateValue('pendingWorks', data.pendingWorks);
            }
            if (data.completedWorks !== undefined) {
                this.updateValue('completedWorks', data.completedWorks);
            }
            if (data.totalRevenue !== undefined) {
                this.updateValue('totalRevenue', data.totalRevenue, true);
            }
        } catch (error) {
            console.warn('خطأ في تحديث الإحصائيات:', error);
        }
    }
    
    // تحديث قيمة واحدة
    static updateValue(elementId, value, isRevenue = false) {
        const element = document.getElementById(elementId);
        if (!element) return;
        
        try {
            if (isRevenue) {
                element.textContent = value.toLocaleString() + ' ج.م';
            } else {
                element.textContent = value.toLocaleString();
            }
            
            // تأثير بسيط للتحديث
            element.style.color = '#10b981';
            setTimeout(() => {
                element.style.color = '';
            }, 1000);
        } catch (error) {
            console.warn('خطأ في تحديث القيمة:', error);
        }
    }
}

// تهيئة تلقائية آمنة
if (typeof window !== 'undefined') {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            SimpleStatistics.init();
        });
    } else {
        SimpleStatistics.init();
    }
}
