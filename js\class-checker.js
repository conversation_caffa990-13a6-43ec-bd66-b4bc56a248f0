// فحص تحميل الكلاسات المطلوبة
console.log('🔄 بدء تحميل ملف class-checker.js...');

class ClassChecker {
    static requiredClasses = [
        'Utils',
        'Database',
        'Auth',
        'Navigation',
        'Dashboard',
        'Prosthetics',
        'Doctors',
        'Employees',
        'ExternalLabs',
        'Financial',
        'Reports',
        'Settings',
        'Inventory',
        'LoginManager'
    ];

    // فحص تحميل الكلاسات المطلوبة
    static checkClassesLoaded() {
        const missingClasses = [];
        const loadedClasses = [];

        this.requiredClasses.forEach(className => {
            if (typeof window[className] === 'undefined') {
                missingClasses.push(className);
                console.error(`❌ الكلاس ${className} غير محمل`);
            } else {
                loadedClasses.push(className);
                console.log(`✅ الكلاس ${className} محمل بنجاح`);
            }
        });

        if (missingClasses.length > 0) {
            console.error('❌ الكلاسات المفقودة:', missingClasses);
            console.log('✅ الكلاسات المحملة:', loadedClasses);
            return false;
        }

        console.log('✅ جميع الكلاسات محملة بنجاح');
        return true;
    }

    // فحص تحميل كلاس معين
    static checkClass(className) {
        const isLoaded = typeof window[className] !== 'undefined';
        console.log(`🔍 فحص الكلاس ${className}:`, isLoaded ? '✅ محمل' : '❌ غير محمل');
        return isLoaded;
    }

    // تقرير مفصل عن حالة الكلاسات
    static generateReport() {
        console.log('📊 تقرير حالة الكلاسات:');
        console.log('================================');
        
        const report = {
            total: this.requiredClasses.length,
            loaded: 0,
            missing: 0,
            details: {}
        };

        this.requiredClasses.forEach(className => {
            const isLoaded = typeof window[className] !== 'undefined';
            report.details[className] = isLoaded;
            
            if (isLoaded) {
                report.loaded++;
                console.log(`✅ ${className}: محمل`);
            } else {
                report.missing++;
                console.log(`❌ ${className}: غير محمل`);
            }
        });

        console.log('================================');
        console.log(`📈 الإحصائيات: ${report.loaded}/${report.total} محمل`);
        console.log(`✅ محملة: ${report.loaded}`);
        console.log(`❌ مفقودة: ${report.missing}`);
        
        return report;
    }

    // فحص دوري للكلاسات
    static startPeriodicCheck(interval = 5000) {
        console.log(`🔄 بدء الفحص الدوري كل ${interval}ms`);
        
        setInterval(() => {
            const allLoaded = this.checkClassesLoaded();
            if (!allLoaded) {
                console.warn('⚠️ بعض الكلاسات لا تزال غير محملة');
            }
        }, interval);
    }

    // إعداد فحص الكلاسات عند تحميل الصفحة
    static setupInitialCheck() {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                console.log('🔍 فحص تحميل الكلاسات...');
                ClassChecker.checkClassesLoaded();
                ClassChecker.generateReport();
            }, 1000);
        });
    }

    // تهيئة فاحص الكلاسات
    static init() {
        this.setupInitialCheck();
        console.log('✅ تم تهيئة فاحص الكلاسات بنجاح');
    }
}

// تهيئة فاحص الكلاسات
ClassChecker.init();

// تسجيل اكتمال تحميل الملف
console.log('✅ تم تحميل ملف class-checker.js بالكامل');

// التأكد من أن الكلاس متاح عالمياً
window.ClassChecker = ClassChecker;
