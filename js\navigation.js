// نظام التنقل والصفحات
class Navigation {
    static currentPage = 'dashboard';
    static pageHistory = [];

    // تهيئة نظام التنقل
    static init() {
        this.setupEventListeners();
        this.setupSidebar();
    }

    // إعداد مستمعي الأحداث
    static setupEventListeners() {
        // روابط القائمة الجانبية
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.getAttribute('data-page');
                this.loadPage(page);
            });
        });

        // زر تبديل القائمة الجانبية
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        // إغلاق القائمة الجانبية عند النقر خارجها في الجوال
        document.addEventListener('click', (e) => {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');

            if (window.innerWidth <= 768 &&
                sidebar &&
                sidebarToggle &&
                !sidebar.contains(e.target) &&
                !sidebarToggle.contains(e.target)) {
                sidebar.classList.remove('open');
            }
        });

        // معالجة تغيير حجم النافذة
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    // إعداد القائمة الجانبية
    static setupSidebar() {
        this.updateSidebarPermissions();
    }

    // تحديث القائمة الجانبية حسب الصلاحيات
    static updateSidebarPermissions() {
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            const page = link.getAttribute('data-page');
            const hasPermission = this.checkPagePermission(page);
            
            if (hasPermission) {
                link.style.display = 'flex';
            } else {
                link.style.display = 'none';
            }
        });
    }

    // التحقق من صلاحية الصفحة
    static checkPagePermission(page) {
        if (!Auth.isLoggedIn()) return false;

        const pagePermissions = {
            dashboard: 'view_dashboard',
            prosthetics: 'view_prosthetics',
            doctors: 'view_doctors',
            employees: 'view_employees',
            'external-labs': 'view_external_labs',
            financial: 'view_financial',
            reports: 'view_reports',
            backup: 'manage_backup',
            settings: 'manage_settings'
        };

        const requiredPermission = pagePermissions[page];
        return !requiredPermission || Auth.hasPermission(requiredPermission);
    }

    // تحميل صفحة
    static loadPage(page) {
        // تم تعطيل شرط الصلاحية مؤقتاً لحل مشكلة عدم ظهور صفحة التركيبات
        // if (!this.checkPagePermission(page)) {
        //     Utils.showNotification('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error');
        //     return;
        // }

        // إضافة الصفحة الحالية إلى التاريخ
        if (this.currentPage && this.currentPage !== page) {
            this.pageHistory.push(this.currentPage);
        }

        this.currentPage = page;
        this.updateActiveNavLink(page);
        this.renderPage(page);

        // تحديث الشريط السفلي
        if (typeof BottomBar !== 'undefined') {
            const pageName = BottomBar.getPageDisplayName(page);
            BottomBar.updateCurrentSection(pageName);
        }

        // إغلاق القائمة الجانبية في الجوال
        if (window.innerWidth <= 768) {
            this.closeSidebar();
        }
    }

    // تحديث الرابط النشط في القائمة
    static updateActiveNavLink(page) {
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        const activeLink = document.querySelector(`[data-page="${page}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }

    // عرض محتوى الصفحة
    static renderPage(page) {
        const pageContent = document.getElementById('pageContent');
        if (!pageContent) return;

        // إظهار مؤشر التحميل
        pageContent.innerHTML = `
            <div class="loading-container">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>جاري التحميل...</span>
                </div>
            </div>
        `;

        // تحميل محتوى الصفحة
        setTimeout(() => {
            switch (page) {
                case 'dashboard':
                    this.renderDashboard();
                    break;
                case 'prosthetics':
                    this.renderProsthetics();
                    break;
                case 'doctors':
                    this.renderDoctors();
                    break;
                case 'employees':
                    this.renderEmployees();
                    break;
                case 'external-labs':
                    this.renderExternalLabs();
                    break;
                case 'financial':
                    this.renderFinancial();
                    break;
                case 'reports':
                    this.renderReports();
                    break;
                case 'backup':
                    this.renderBackup();
                    break;
                case 'settings':
                    this.renderSettings();
                    break;
                case 'inventory':
                    this.renderInventory();
                    break;
                default:
                    this.renderNotFound();
            }
        }, 300);
    }

    // عرض لوحة التحكم
    static renderDashboard() {
        if (typeof Dashboard !== 'undefined') {
            // تأخير صغير للتأكد من تحميل DOM
            setTimeout(() => {
                Dashboard.render();
            }, 50);
        } else {
            this.renderPlaceholder('لوحة التحكم', 'tachometer-alt');
        }
    }

    // عرض صفحة إدارة التركيبات
    static renderProsthetics() {
        if (typeof Prosthetics !== 'undefined') {
            // تأخير صغير للتأكد من تحميل DOM
            setTimeout(() => {
                Prosthetics.render();
            }, 50);
        } else {
            this.renderPlaceholder('إدارة التركيبات', 'tooth');
        }
    }



    // عرض صفحة الأطباء
    static renderDoctors() {
        if (typeof Doctors !== 'undefined') {
            Doctors.render();
        } else {
            this.renderPlaceholder('إدارة الأطباء', 'user-md');
        }
    }

    // عرض صفحة الموظفين
    static renderEmployees() {
        if (typeof Employees !== 'undefined') {
            Employees.render();
        } else {
            this.renderPlaceholder('إدارة الموظفين', 'users');
        }
    }

    // عرض صفحة المعامل الخارجية
    static renderExternalLabs() {
        if (typeof ExternalLabs !== 'undefined') {
            ExternalLabs.render();
        } else {
            this.renderPlaceholder('المعامل الخارجية', 'building');
        }
    }

    // عرض صفحة النظام المالي
    static renderFinancial() {
        if (typeof Financial !== 'undefined') {
            Financial.render();
        } else {
            this.renderPlaceholder('النظام المالي', 'chart-line');
        }
    }

    // عرض صفحة التقارير
    static renderReports() {
        if (typeof Reports !== 'undefined') {
            Reports.render();
        } else {
            this.renderPlaceholder('التقارير', 'chart-bar');
        }
    }

    // عرض صفحة النسخ الاحتياطي
    static renderBackup() {
        if (typeof Backup !== 'undefined') {
            Backup.render();
        } else {
            this.renderPlaceholder('النسخ الاحتياطي', 'download');
        }
    }

    // عرض صفحة النسخ الاحتياطي
    static renderBackup() {
        if (typeof Backup !== 'undefined') {
            Backup.render();
        } else {
            this.renderPlaceholder('النسخ الاحتياطي', 'download');
        }
    }

    // عرض صفحة الإعدادات
    static renderSettings() {
        if (typeof Settings !== 'undefined') {
            Settings.render();
        } else {
            this.renderPlaceholder('الإعدادات', 'cog');
        }
    }

    // عرض صفحة غير موجودة
    static renderNotFound() {
        const pageContent = document.getElementById('pageContent');
        pageContent.innerHTML = `
            <div class="not-found-container">
                <div class="not-found-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h2>الصفحة غير موجودة</h2>
                    <p>الصفحة التي تبحث عنها غير متوفرة</p>
                    <button class="btn btn-primary" onclick="Navigation.loadPage('dashboard')">
                        <i class="fas fa-home"></i>
                        العودة للرئيسية
                    </button>
                </div>
            </div>
        `;
    }

    // عرض محتوى مؤقت
    static renderPlaceholder(title, icon) {
        const pageContent = document.getElementById('pageContent');
        pageContent.innerHTML = `
            <div class="page-placeholder">
                <div class="placeholder-content">
                    <i class="fas fa-${icon}"></i>
                    <h2>${title}</h2>
                    <p>هذه الصفحة قيد التطوير</p>
                </div>
            </div>
        `;
    }

    // تبديل القائمة الجانبية
    static toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.toggle('open');
        }
    }

    // إغلاق القائمة الجانبية
    static closeSidebar() {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.remove('open');
        }
    }

    // فتح القائمة الجانبية
    static openSidebar() {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.add('open');
        }
    }

    // معالجة تغيير حجم النافذة
    static handleResize() {
        if (window.innerWidth > 768) {
            this.closeSidebar();
        }
    }

    // العودة للصفحة السابقة
    static goBack() {
        if (this.pageHistory.length > 0) {
            const previousPage = this.pageHistory.pop();
            this.loadPage(previousPage);
        }
    }

    // الحصول على الصفحة الحالية
    static getCurrentPage() {
        return this.currentPage;
    }

    // إعادة تحميل الصفحة الحالية
    static reloadCurrentPage() {
        this.renderPage(this.currentPage);
    }

    // تحديث عنوان الصفحة
    static updatePageTitle(title) {
        document.title = `${title} - نظام إدارة معمل الأسنان`;
    }

    // إضافة breadcrumb
    static updateBreadcrumb(items) {
        const breadcrumbContainer = document.querySelector('.breadcrumb');
        if (!breadcrumbContainer) return;

        const breadcrumbHTML = items.map((item, index) => {
            const isLast = index === items.length - 1;
            if (isLast) {
                return `<span class="breadcrumb-item active">${item.title}</span>`;
            } else {
                return `<a href="#" class="breadcrumb-item" onclick="Navigation.loadPage('${item.page}')">${item.title}</a>`;
            }
        }).join('<i class="fas fa-chevron-left breadcrumb-separator"></i>');

        breadcrumbContainer.innerHTML = breadcrumbHTML;
    }

    // عرض صفحة إدارة المخزن
    static renderInventory() {
        console.log('تم استدعاء renderInventory');
        if (typeof loadInventoryPage === 'function') {
            console.log('تم العثور على loadInventoryPage، جاري التحميل...');
            loadInventoryPage();
        } else {
            console.error('وظيفة loadInventoryPage غير موجودة');
            this.renderNotFound();
        }
    }

    // وظيفة مساعدة للتوافق مع الكود الموجود
    static navigateTo(page) {
        this.loadPage(page);
    }
}
