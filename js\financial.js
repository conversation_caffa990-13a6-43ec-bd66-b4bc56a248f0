// النظام المالي
class Financial {
    static currentAccounts = [];
    static currentExpenses = [];
    static currentPayments = [];

    // عرض صفحة النظام المالي
    static render() {
        const pageContent = document.getElementById('pageContent');
        
        pageContent.innerHTML = `
            <div class="financial-container">
                <!-- عنوان الإدارة المطور -->
                <div class="department-header financial">
                    <div class="department-header-content">
                        <div class="department-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="department-info">
                            <h1 class="department-name" data-text="النظام المالي">النظام المالي</h1>
                            <p class="department-description">إدارة شاملة للحسابات والمصروفات والدفعات والتقارير المالية</p>
                            <div class="department-stats">
                                <div class="department-stat">
                                    <span class="department-stat-value" id="header-total-income">0</span>
                                    <span class="department-stat-label">إجمالي الإيرادات</span>
                                </div>
                                <div class="department-stat">
                                    <span class="department-stat-value" id="header-total-expenses">0</span>
                                    <span class="department-stat-label">إجمالي المصروفات</span>
                                </div>
                                <div class="department-stat">
                                    <span class="department-stat-value" id="header-net-profit">0</span>
                                    <span class="department-stat-label">صافي الربح</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="page-actions-container">
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="Financial.showAddAccountModal()">
                            <i class="fas fa-plus"></i>
                            إضافة حساب
                        </button>
                        <button class="btn btn-warning" onclick="Financial.showAddExpenseModal()">
                            <i class="fas fa-minus"></i>
                            إضافة مصروف
                        </button>
                        <button class="btn btn-success" onclick="Financial.showAddPaymentModal()">
                            <i class="fas fa-dollar-sign"></i>
                            إضافة دفعة
                        </button>
                        <button class="btn btn-info" onclick="Financial.generateReport()">
                            <i class="fas fa-file-alt"></i>
                            تقرير مالي
                        </button>
                    </div>
                </div>

                <!-- الإحصائيات المالية -->
                <div class="financial-stats">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon success">
                                    <i class="fas fa-arrow-up"></i>
                                </div>
                            </div>
                            <div class="stat-value" id="total-income">0</div>
                            <div class="stat-label">إجمالي الإيرادات</div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon danger">
                                    <i class="fas fa-arrow-down"></i>
                                </div>
                            </div>
                            <div class="stat-value" id="total-expenses">0</div>
                            <div class="stat-label">إجمالي المصروفات</div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon primary">
                                    <i class="fas fa-balance-scale"></i>
                                </div>
                            </div>
                            <div class="stat-value" id="net-profit">0</div>
                            <div class="stat-label">صافي الربح</div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon warning">
                                    <i class="fas fa-clock"></i>
                                </div>
                            </div>
                            <div class="stat-value" id="pending-payments">0</div>
                            <div class="stat-label">دفعات معلقة</div>
                        </div>
                    </div>
                </div>

                <!-- علامات التبويب -->
                <div class="financial-tabs">
                    <div class="tab-buttons">
                        <button class="tab-btn active" data-tab="accounts">الحسابات</button>
                        <button class="tab-btn" data-tab="expenses">المصروفات</button>
                        <button class="tab-btn" data-tab="payments">الدفعات</button>
                        <button class="tab-btn" data-tab="reports">التقارير</button>
                    </div>

                    <!-- محتوى علامة تبويب الحسابات -->
                    <div class="tab-content active" id="accounts-tab">
                        <div class="table-container">
                            <div class="table-header">
                                <h3 class="table-title">الحسابات المالية</h3>
                                <div class="table-actions">
                                    <button class="btn btn-primary" onclick="Financial.showAddAccountModal()">
                                        <i class="fas fa-plus"></i>
                                        إضافة حساب
                                    </button>
                                </div>
                            </div>
                            
                            <div class="table-wrapper">
                                <table class="data-table" id="accounts-table">
                                    <thead>
                                        <tr>
                                            <th>اسم الحساب</th>
                                            <th>النوع</th>
                                            <th>الرصيد</th>
                                            <th>تاريخ الإنشاء</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="accounts-tbody">
                                        <!-- سيتم تحميل البيانات هنا -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- محتوى علامة تبويب المصروفات -->
                    <div class="tab-content" id="expenses-tab">
                        <div class="table-container">
                            <div class="table-header">
                                <h3 class="table-title">المصروفات</h3>
                                <div class="table-actions">
                                    <button class="btn btn-warning" onclick="Financial.showAddExpenseModal()">
                                        <i class="fas fa-minus"></i>
                                        إضافة مصروف
                                    </button>
                                </div>
                            </div>
                            
                            <div class="table-wrapper">
                                <table class="data-table" id="expenses-table">
                                    <thead>
                                        <tr>
                                            <th>الوصف</th>
                                            <th>الفئة</th>
                                            <th>المبلغ</th>
                                            <th>التاريخ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="expenses-tbody">
                                        <!-- سيتم تحميل البيانات هنا -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- محتوى علامة تبويب الدفعات -->
                    <div class="tab-content" id="payments-tab">
                        <div class="table-container">
                            <div class="table-header">
                                <h3 class="table-title">الدفعات</h3>
                                <div class="table-actions">
                                    <button class="btn btn-success" onclick="Financial.showAddPaymentModal()">
                                        <i class="fas fa-dollar-sign"></i>
                                        إضافة دفعة
                                    </button>
                                </div>
                            </div>
                            
                            <div class="table-wrapper">
                                <table class="data-table" id="payments-table">
                                    <thead>
                                        <tr>
                                            <th>رقم الحالة</th>
                                            <th>المريض</th>
                                            <th>المبلغ المدفوع</th>
                                            <th>المبلغ المتبقي</th>
                                            <th>طريقة الدفع</th>
                                            <th>التاريخ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="payments-tbody">
                                        <!-- سيتم تحميل البيانات هنا -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- محتوى علامة تبويب التقارير -->
                    <div class="tab-content" id="reports-tab">
                        <div class="reports-container">
                            <div class="report-filters">
                                <div class="filter-row">
                                    <div class="filter-group">
                                        <label>من تاريخ:</label>
                                        <input type="date" id="report-from-date">
                                    </div>
                                    <div class="filter-group">
                                        <label>إلى تاريخ:</label>
                                        <input type="date" id="report-to-date">
                                    </div>
                                    <div class="filter-group">
                                        <label>نوع التقرير:</label>
                                        <select id="report-type">
                                            <option value="summary">ملخص مالي</option>
                                            <option value="income">تقرير الإيرادات</option>
                                            <option value="expenses">تقرير المصروفات</option>
                                            <option value="payments">تقرير الدفعات</option>
                                        </select>
                                    </div>
                                    <button class="btn btn-primary" onclick="Financial.generateCustomReport()">
                                        <i class="fas fa-chart-bar"></i>
                                        إنشاء التقرير
                                    </button>
                                </div>
                            </div>
                            
                            <div id="report-content" class="report-content">
                                <!-- سيتم تحميل التقرير هنا -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.loadData();
        this.setupEventListeners();
        this.updateStats();
    }

    // تحميل البيانات
    static loadData() {
        this.currentAccounts = Database.getItem('accounts') || [];
        this.currentExpenses = Database.getItem('expenses') || [];
        this.currentPayments = Database.getItem('payments') || [];
        
        this.updateAccountsTable();
        this.updateExpensesTable();
        this.updatePaymentsTable();
    }

    // إعداد مستمعي الأحداث
    static setupEventListeners() {
        // علامات التبويب
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabName = e.target.getAttribute('data-tab');
                this.switchTab(tabName);
            });
        });

        // تعيين التواريخ الافتراضية للتقارير
        const fromDate = document.getElementById('report-from-date');
        const toDate = document.getElementById('report-to-date');
        
        if (fromDate && toDate) {
            const now = new Date();
            const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
            
            fromDate.value = Utils.formatDate(firstDay, 'YYYY-MM-DD');
            toDate.value = Utils.formatDate(now, 'YYYY-MM-DD');
        }
    }

    // تبديل علامات التبويب
    static switchTab(tabName) {
        // إزالة الفئة النشطة من جميع الأزرار والمحتوى
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

        // إضافة الفئة النشطة للزر والمحتوى المحدد
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.getElementById(`${tabName}-tab`).classList.add('active');
    }

    // تحديث الإحصائيات
    static updateStats() {
        // حساب الإيرادات من التركيبات المكتملة
        const prosthetics = Database.getProsthetics();
        const completedProsthetics = prosthetics.filter(p => p.status === 'completed' || p.status === 'delivered');
        const totalIncome = completedProsthetics.reduce((sum, p) => sum + (p.totalPrice || 0), 0);

        // حساب المصروفات
        const totalExpenses = this.currentExpenses.reduce((sum, e) => sum + (e.amount || 0), 0);

        // حساب صافي الربح
        const netProfit = totalIncome - totalExpenses;

        // حساب الدفعات المعلقة
        const pendingPayments = this.currentPayments.filter(p => p.status === 'pending').length;

        // تحديث العرض في البطاقات
        document.getElementById('total-income').textContent = Utils.formatCurrency(totalIncome);
        document.getElementById('total-expenses').textContent = Utils.formatCurrency(totalExpenses);
        document.getElementById('net-profit').textContent = Utils.formatCurrency(netProfit);
        document.getElementById('pending-payments').textContent = pendingPayments;

        // تحديث إحصائيات العنوان
        const headerTotalIncome = document.getElementById('header-total-income');
        const headerTotalExpenses = document.getElementById('header-total-expenses');
        const headerNetProfit = document.getElementById('header-net-profit');

        if (headerTotalIncome) headerTotalIncome.textContent = Utils.formatCurrency(totalIncome);
        if (headerTotalExpenses) headerTotalExpenses.textContent = Utils.formatCurrency(totalExpenses);
        if (headerNetProfit) headerNetProfit.textContent = Utils.formatCurrency(netProfit);

        // تغيير لون صافي الربح حسب القيمة
        const netProfitElement = document.getElementById('net-profit');
        if (netProfit > 0) {
            netProfitElement.style.color = '#27ae60';
        } else if (netProfit < 0) {
            netProfitElement.style.color = '#e74c3c';
        } else {
            netProfitElement.style.color = '#7f8c8d';
        }
    }

    // تحديث جدول الحسابات
    static updateAccountsTable() {
        const tbody = document.getElementById('accounts-tbody');
        if (!tbody) return;

        tbody.innerHTML = this.currentAccounts.map(account => `
            <tr>
                <td><strong>${account.name}</strong></td>
                <td>
                    <span class="account-type-badge ${account.type}">
                        ${this.getAccountTypeDisplayName(account.type)}
                    </span>
                </td>
                <td>
                    <strong class="${account.balance >= 0 ? 'positive' : 'negative'}">
                        ${Utils.formatCurrency(account.balance || 0)}
                    </strong>
                </td>
                <td>${Utils.formatDate(account.createdAt, 'DD/MM/YYYY')}</td>
                <td class="actions-cell">
                    <div class="action-buttons">
                        <button class="btn-icon btn-primary" onclick="Financial.editAccount('${account.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon btn-info" onclick="Financial.viewAccountTransactions('${account.id}')" title="المعاملات">
                            <i class="fas fa-list"></i>
                        </button>
                        <button class="btn-icon btn-danger" onclick="Financial.deleteAccount('${account.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        if (this.currentAccounts.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="no-data">
                        <div class="no-data-message">
                            <i class="fas fa-wallet"></i>
                            <p>لا توجد حسابات مالية</p>
                        </div>
                    </td>
                </tr>
            `;
        }
    }

    // تحديث جدول المصروفات
    static updateExpensesTable() {
        const tbody = document.getElementById('expenses-tbody');
        if (!tbody) return;

        tbody.innerHTML = this.currentExpenses.map(expense => `
            <tr>
                <td>${expense.description}</td>
                <td>
                    <span class="expense-category-badge">
                        ${expense.category}
                    </span>
                </td>
                <td><strong>${Utils.formatCurrency(expense.amount || 0)}</strong></td>
                <td>${Utils.formatDate(expense.date, 'DD/MM/YYYY')}</td>
                <td class="actions-cell">
                    <div class="action-buttons">
                        <button class="btn-icon btn-primary" onclick="Financial.editExpense('${expense.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon btn-danger" onclick="Financial.deleteExpense('${expense.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        if (this.currentExpenses.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="no-data">
                        <div class="no-data-message">
                            <i class="fas fa-receipt"></i>
                            <p>لا توجد مصروفات</p>
                        </div>
                    </td>
                </tr>
            `;
        }
    }

    // تحديث جدول الدفعات
    static updatePaymentsTable() {
        const tbody = document.getElementById('payments-tbody');
        if (!tbody) return;

        tbody.innerHTML = this.currentPayments.map(payment => `
            <tr>
                <td><strong>${payment.caseNumber}</strong></td>
                <td>${payment.patientName}</td>
                <td><strong>${Utils.formatCurrency(payment.paidAmount || 0)}</strong></td>
                <td><strong>${Utils.formatCurrency(payment.remainingAmount || 0)}</strong></td>
                <td>
                    <span class="payment-method-badge">
                        ${this.getPaymentMethodDisplayName(payment.paymentMethod)}
                    </span>
                </td>
                <td>${Utils.formatDate(payment.date, 'DD/MM/YYYY')}</td>
                <td class="actions-cell">
                    <div class="action-buttons">
                        <button class="btn-icon btn-primary" onclick="Financial.editPayment('${payment.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon btn-warning" onclick="Financial.printReceipt('${payment.id}')" title="طباعة إيصال">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn-icon btn-danger" onclick="Financial.deletePayment('${payment.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        if (this.currentPayments.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="no-data">
                        <div class="no-data-message">
                            <i class="fas fa-money-bill"></i>
                            <p>لا توجد دفعات</p>
                        </div>
                    </td>
                </tr>
            `;
        }
    }

    // الحصول على اسم عرض نوع الحساب
    static getAccountTypeDisplayName(type) {
        const typeNames = {
            cash: 'نقدي',
            bank: 'بنكي',
            credit: 'ائتماني',
            savings: 'توفير'
        };
        return typeNames[type] || type;
    }

    // الحصول على اسم عرض طريقة الدفع
    static getPaymentMethodDisplayName(method) {
        const methodNames = {
            cash: 'نقدي',
            card: 'بطاقة ائتمان',
            bank_transfer: 'تحويل بنكي',
            check: 'شيك'
        };
        return methodNames[method] || method;
    }

    // عرض نافذة إضافة حساب جديد
    static showAddAccountModal() {
        Utils.createModal({
            title: 'إضافة حساب مالي جديد',
            content: `
                <form id="add-account-form" class="account-form">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="account-name">اسم الحساب *</label>
                            <input type="text" id="account-name" name="name" required>
                        </div>

                        <div class="form-group">
                            <label for="account-type">نوع الحساب *</label>
                            <select id="account-type" name="type" required>
                                <option value="">اختر النوع</option>
                                <option value="cash">نقدي</option>
                                <option value="bank">بنكي</option>
                                <option value="credit">ائتماني</option>
                                <option value="savings">توفير</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="account-balance">الرصيد الابتدائي</label>
                            <input type="number" id="account-balance" name="balance" step="0.01" value="0">
                        </div>

                        <div class="form-group">
                            <label for="account-number">رقم الحساب</label>
                            <input type="text" id="account-number" name="accountNumber">
                        </div>

                        <div class="form-group">
                            <label for="bank-name">اسم البنك</label>
                            <input type="text" id="bank-name" name="bankName">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="account-notes">ملاحظات</label>
                        <textarea id="account-notes" name="notes" rows="3"></textarea>
                    </div>

                    <div class="modal-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ الحساب
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            `,
            size: 'medium'
        });

        this.setupAddAccountForm();
    }

    // إعداد نموذج إضافة الحساب
    static setupAddAccountForm() {
        const form = document.getElementById('add-account-form');
        if (!form) return;

        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveAccount();
        });
    }

    // حفظ الحساب
    static async saveAccount() {
        const form = document.getElementById('add-account-form');
        if (!form) return;

        const formData = new FormData(form);

        // التحقق من صحة البيانات
        const name = formData.get('name').trim();
        const type = formData.get('type');

        if (!name) {
            Utils.showNotification('يرجى إدخال اسم الحساب', 'warning');
            return;
        }

        if (!type) {
            Utils.showNotification('يرجى اختيار نوع الحساب', 'warning');
            return;
        }

        // جمع بيانات الحساب
        const accountData = {
            id: Utils.generateId(),
            name: name,
            type: type,
            balance: parseFloat(formData.get('balance')) || 0,
            accountNumber: formData.get('accountNumber')?.trim() || '',
            bankName: formData.get('bankName')?.trim() || '',
            notes: formData.get('notes')?.trim() || '',
            createdAt: new Date().toISOString(),
            createdBy: Auth.getCurrentUser()?.id
        };

        // حفظ في قاعدة البيانات
        this.currentAccounts.push(accountData);
        const saved = Database.setItem('accounts', this.currentAccounts);

        if (saved) {
            Database.addActivity({
                type: 'account_added',
                text: `تم إضافة حساب مالي جديد: ${accountData.name}`,
                icon: 'wallet',
                userId: Auth.getCurrentUser()?.id
            });

            Utils.showNotification('تم حفظ الحساب بنجاح', 'success');
            Utils.closeModal();
            this.loadData();
            this.updateStats();
        } else {
            Utils.showNotification('فشل في حفظ الحساب', 'error');
        }
    }

    // عرض نافذة إضافة مصروف جديد
    static showAddExpenseModal() {
        Utils.createModal({
            title: 'إضافة مصروف جديد',
            content: `
                <form id="add-expense-form" class="expense-form">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="expense-description">وصف المصروف *</label>
                            <input type="text" id="expense-description" name="description" required>
                        </div>

                        <div class="form-group">
                            <label for="expense-category">الفئة *</label>
                            <select id="expense-category" name="category" required>
                                <option value="">اختر الفئة</option>
                                <option value="materials">مواد خام</option>
                                <option value="equipment">معدات</option>
                                <option value="salaries">مرتبات</option>
                                <option value="utilities">مرافق</option>
                                <option value="maintenance">صيانة</option>
                                <option value="marketing">تسويق</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="expense-amount">المبلغ *</label>
                            <input type="number" id="expense-amount" name="amount" step="0.01" min="0" required>
                        </div>

                        <div class="form-group">
                            <label for="expense-date">التاريخ *</label>
                            <input type="date" id="expense-date" name="date" required>
                        </div>

                        <div class="form-group">
                            <label for="expense-account">الحساب</label>
                            <select id="expense-account" name="accountId">
                                <option value="">اختر الحساب</option>
                                ${this.currentAccounts.map(account =>
                                    `<option value="${account.id}">${account.name}</option>`
                                ).join('')}
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="expense-notes">ملاحظات</label>
                        <textarea id="expense-notes" name="notes" rows="3"></textarea>
                    </div>

                    <div class="modal-actions">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save"></i>
                            حفظ المصروف
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            `,
            size: 'medium'
        });

        // تعيين التاريخ الافتراضي (اليوم)
        const dateInput = document.getElementById('expense-date');
        if (dateInput) {
            dateInput.value = Utils.formatDate(new Date(), 'YYYY-MM-DD');
        }

        this.setupAddExpenseForm();
    }

    // إعداد نموذج إضافة المصروف
    static setupAddExpenseForm() {
        const form = document.getElementById('add-expense-form');
        if (!form) return;

        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveExpense();
        });
    }

    // حفظ المصروف
    static async saveExpense() {
        const form = document.getElementById('add-expense-form');
        if (!form) return;

        const formData = new FormData(form);

        // التحقق من صحة البيانات
        const description = formData.get('description').trim();
        const category = formData.get('category');
        const amount = parseFloat(formData.get('amount'));
        const date = formData.get('date');

        if (!description) {
            Utils.showNotification('يرجى إدخال وصف المصروف', 'warning');
            return;
        }

        if (!category) {
            Utils.showNotification('يرجى اختيار فئة المصروف', 'warning');
            return;
        }

        if (!amount || amount <= 0) {
            Utils.showNotification('يرجى إدخال مبلغ صحيح', 'warning');
            return;
        }

        if (!date) {
            Utils.showNotification('يرجى تحديد التاريخ', 'warning');
            return;
        }

        // جمع بيانات المصروف
        const expenseData = {
            id: Utils.generateId(),
            description: description,
            category: category,
            amount: amount,
            date: date,
            accountId: formData.get('accountId') || null,
            notes: formData.get('notes')?.trim() || '',
            createdAt: new Date().toISOString(),
            createdBy: Auth.getCurrentUser()?.id
        };

        // حفظ في قاعدة البيانات
        this.currentExpenses.push(expenseData);
        const saved = Database.setItem('expenses', this.currentExpenses);

        if (saved) {
            Database.addActivity({
                type: 'expense_added',
                text: `تم إضافة مصروف جديد: ${expenseData.description} - ${Utils.formatCurrency(expenseData.amount)}`,
                icon: 'minus',
                userId: Auth.getCurrentUser()?.id
            });

            Utils.showNotification('تم حفظ المصروف بنجاح', 'success');
            Utils.closeModal();
            this.loadData();
            this.updateStats();
        } else {
            Utils.showNotification('فشل في حفظ المصروف', 'error');
        }
    }

    // عرض نافذة إضافة دفعة جديدة
    static showAddPaymentModal() {
        const prosthetics = Database.getProsthetics();

        Utils.createModal({
            title: 'إضافة دفعة جديدة',
            content: `
                <form id="add-payment-form" class="payment-form">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="payment-prosthetic">التركيبة *</label>
                            <select id="payment-prosthetic" name="prostheticId" required onchange="Financial.updatePaymentInfo()">
                                <option value="">اختر التركيبة</option>
                                ${prosthetics.map(p =>
                                    `<option value="${p.id}" data-price="${p.totalPrice}" data-patient="${p.patientName}" data-case="${p.caseNumber}">
                                        ${p.caseNumber} - ${p.patientName} (${Utils.formatCurrency(p.totalPrice)})
                                    </option>`
                                ).join('')}
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="payment-amount">المبلغ المدفوع *</label>
                            <input type="number" id="payment-amount" name="paidAmount" step="0.01" min="0" required>
                        </div>

                        <div class="form-group">
                            <label for="payment-method">طريقة الدفع *</label>
                            <select id="payment-method" name="paymentMethod" required>
                                <option value="">اختر طريقة الدفع</option>
                                <option value="cash">نقدي</option>
                                <option value="card">بطاقة ائتمان</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                                <option value="check">شيك</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="payment-date">تاريخ الدفع *</label>
                            <input type="date" id="payment-date" name="date" required>
                        </div>

                        <div class="form-group">
                            <label for="payment-account">الحساب</label>
                            <select id="payment-account" name="accountId">
                                <option value="">اختر الحساب</option>
                                ${this.currentAccounts.map(account =>
                                    `<option value="${account.id}">${account.name}</option>`
                                ).join('')}
                            </select>
                        </div>
                    </div>

                    <div class="payment-summary" id="payment-summary" style="display: none;">
                        <div class="summary-row">
                            <span>إجمالي التكلفة:</span>
                            <span id="total-cost">0 جنيه</span>
                        </div>
                        <div class="summary-row">
                            <span>المبلغ المدفوع:</span>
                            <span id="paid-amount">0 جنيه</span>
                        </div>
                        <div class="summary-row">
                            <span>المبلغ المتبقي:</span>
                            <span id="remaining-amount">0 جنيه</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="payment-notes">ملاحظات</label>
                        <textarea id="payment-notes" name="notes" rows="3"></textarea>
                    </div>

                    <div class="modal-actions">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i>
                            حفظ الدفعة
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            `,
            size: 'medium'
        });

        // تعيين التاريخ الافتراضي (اليوم)
        const dateInput = document.getElementById('payment-date');
        if (dateInput) {
            dateInput.value = Utils.formatDate(new Date(), 'YYYY-MM-DD');
        }

        this.setupAddPaymentForm();
    }

    // تحديث معلومات الدفعة
    static updatePaymentInfo() {
        const prostheticSelect = document.getElementById('payment-prosthetic');
        const summary = document.getElementById('payment-summary');

        if (!prostheticSelect || !summary) return;

        const selectedOption = prostheticSelect.selectedOptions[0];
        if (!selectedOption || !selectedOption.value) {
            summary.style.display = 'none';
            return;
        }

        const totalPrice = parseFloat(selectedOption.getAttribute('data-price')) || 0;
        const paidAmountInput = document.getElementById('payment-amount');
        const paidAmount = parseFloat(paidAmountInput?.value) || 0;
        const remainingAmount = totalPrice - paidAmount;

        document.getElementById('total-cost').textContent = Utils.formatCurrency(totalPrice);
        document.getElementById('paid-amount').textContent = Utils.formatCurrency(paidAmount);
        document.getElementById('remaining-amount').textContent = Utils.formatCurrency(remainingAmount);

        summary.style.display = 'block';

        // تحديث المبلغ المتبقي عند تغيير المبلغ المدفوع
        if (paidAmountInput) {
            paidAmountInput.addEventListener('input', () => {
                const newPaidAmount = parseFloat(paidAmountInput.value) || 0;
                const newRemainingAmount = totalPrice - newPaidAmount;
                document.getElementById('paid-amount').textContent = Utils.formatCurrency(newPaidAmount);
                document.getElementById('remaining-amount').textContent = Utils.formatCurrency(newRemainingAmount);
            });
        }
    }

    // إعداد نموذج إضافة الدفعة
    static setupAddPaymentForm() {
        const form = document.getElementById('add-payment-form');
        if (!form) return;

        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.savePayment();
        });
    }

    // حفظ الدفعة
    static async savePayment() {
        const form = document.getElementById('add-payment-form');
        if (!form) return;

        const formData = new FormData(form);

        // التحقق من صحة البيانات
        const prostheticId = formData.get('prostheticId');
        const paidAmount = parseFloat(formData.get('paidAmount'));
        const paymentMethod = formData.get('paymentMethod');
        const date = formData.get('date');

        if (!prostheticId) {
            Utils.showNotification('يرجى اختيار التركيبة', 'warning');
            return;
        }

        if (!paidAmount || paidAmount <= 0) {
            Utils.showNotification('يرجى إدخال مبلغ صحيح', 'warning');
            return;
        }

        if (!paymentMethod) {
            Utils.showNotification('يرجى اختيار طريقة الدفع', 'warning');
            return;
        }

        if (!date) {
            Utils.showNotification('يرجى تحديد التاريخ', 'warning');
            return;
        }

        // الحصول على بيانات التركيبة
        const prosthetic = Database.getProstheticById(prostheticId);
        if (!prosthetic) {
            Utils.showNotification('التركيبة غير موجودة', 'error');
            return;
        }

        const totalPrice = prosthetic.totalPrice || 0;
        const remainingAmount = totalPrice - paidAmount;

        // جمع بيانات الدفعة
        const paymentData = {
            id: Utils.generateId(),
            prostheticId: prostheticId,
            caseNumber: prosthetic.caseNumber,
            patientName: prosthetic.patientName,
            totalAmount: totalPrice,
            paidAmount: paidAmount,
            remainingAmount: remainingAmount,
            paymentMethod: paymentMethod,
            date: date,
            accountId: formData.get('accountId') || null,
            notes: formData.get('notes')?.trim() || '',
            status: remainingAmount <= 0 ? 'completed' : 'partial',
            createdAt: new Date().toISOString(),
            createdBy: Auth.getCurrentUser()?.id
        };

        // حفظ في قاعدة البيانات
        this.currentPayments.push(paymentData);
        const saved = Database.setItem('payments', this.currentPayments);

        if (saved) {
            Database.addActivity({
                type: 'payment_added',
                text: `تم إضافة دفعة جديدة: ${prosthetic.caseNumber} - ${Utils.formatCurrency(paidAmount)}`,
                icon: 'dollar-sign',
                userId: Auth.getCurrentUser()?.id
            });

            Utils.showNotification('تم حفظ الدفعة بنجاح', 'success');
            Utils.closeModal();
            this.loadData();
            this.updateStats();
        } else {
            Utils.showNotification('فشل في حفظ الدفعة', 'error');
        }
    }

    // طباعة إيصال الدفع
    static printReceipt(paymentId) {
        const payment = this.currentPayments.find(p => p.id === paymentId);
        if (!payment) return;

        const settings = Database.getSettings();

        // إنشاء نافذة طباعة
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>إيصال دفع - ${payment.caseNumber}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #000; padding-bottom: 20px; }
                    .receipt-details { margin-bottom: 20px; }
                    .detail-row { margin-bottom: 10px; display: flex; justify-content: space-between; }
                    .label { font-weight: bold; }
                    .total { border-top: 2px solid #000; padding-top: 10px; font-size: 1.2em; font-weight: bold; }
                    .footer { margin-top: 30px; text-align: center; font-size: 0.9em; color: #666; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>${settings.labName || 'معمل الأسنان المتخصص'}</h1>
                    <p>${settings.address || ''}</p>
                    <p>هاتف: ${settings.phones ? settings.phones.join(' - ') : ''}</p>
                    <h2>إيصال دفع</h2>
                </div>
                <div class="receipt-details">
                    <div class="detail-row">
                        <span class="label">رقم الإيصال:</span>
                        <span>${payment.id}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">رقم الحالة:</span>
                        <span>${payment.caseNumber}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">اسم المريض:</span>
                        <span>${payment.patientName}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">إجمالي التكلفة:</span>
                        <span>${Utils.formatCurrency(payment.totalAmount)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">المبلغ المدفوع:</span>
                        <span>${Utils.formatCurrency(payment.paidAmount)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">المبلغ المتبقي:</span>
                        <span>${Utils.formatCurrency(payment.remainingAmount)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">طريقة الدفع:</span>
                        <span>${this.getPaymentMethodDisplayName(payment.paymentMethod)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">تاريخ الدفع:</span>
                        <span>${Utils.formatDate(payment.date, 'DD/MM/YYYY')}</span>
                    </div>
                    ${payment.notes ? `
                        <div class="detail-row">
                            <span class="label">ملاحظات:</span>
                            <span>${payment.notes}</span>
                        </div>
                    ` : ''}
                </div>
                <div class="footer">
                    <p>تاريخ الطباعة: ${Utils.formatDate(new Date(), 'DD/MM/YYYY HH:mm')}</p>
                    <p>شكراً لثقتكم بنا</p>
                </div>
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.print();
    }

    // إنشاء تقرير مالي
    static generateReport() {
        const now = new Date();
        const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);

        this.generateCustomReport(
            Utils.formatDate(firstDay, 'YYYY-MM-DD'),
            Utils.formatDate(now, 'YYYY-MM-DD'),
            'summary'
        );
    }

    // إنشاء تقرير مخصص
    static generateCustomReport() {
        const fromDate = document.getElementById('report-from-date')?.value;
        const toDate = document.getElementById('report-to-date')?.value;
        const reportType = document.getElementById('report-type')?.value;

        if (!fromDate || !toDate) {
            Utils.showNotification('يرجى تحديد فترة التقرير', 'warning');
            return;
        }

        const reportContent = document.getElementById('report-content');
        if (!reportContent) return;

        // فلترة البيانات حسب التاريخ
        const prosthetics = Database.getProsthetics().filter(p => {
            const date = new Date(p.createdAt);
            return date >= new Date(fromDate) && date <= new Date(toDate);
        });

        const expenses = this.currentExpenses.filter(e => {
            const date = new Date(e.date);
            return date >= new Date(fromDate) && date <= new Date(toDate);
        });

        const payments = this.currentPayments.filter(p => {
            const date = new Date(p.date);
            return date >= new Date(fromDate) && date <= new Date(toDate);
        });

        let reportHTML = '';

        switch (reportType) {
            case 'summary':
                reportHTML = this.generateSummaryReport(prosthetics, expenses, payments, fromDate, toDate);
                break;
            case 'income':
                reportHTML = this.generateIncomeReport(prosthetics, fromDate, toDate);
                break;
            case 'expenses':
                reportHTML = this.generateExpensesReport(expenses, fromDate, toDate);
                break;
            case 'payments':
                reportHTML = this.generatePaymentsReport(payments, fromDate, toDate);
                break;
        }

        reportContent.innerHTML = reportHTML;
    }

    // إنشاء تقرير ملخص
    static generateSummaryReport(prosthetics, expenses, payments, fromDate, toDate) {
        const totalIncome = prosthetics.reduce((sum, p) => sum + (p.totalPrice || 0), 0);
        const totalExpenses = expenses.reduce((sum, e) => sum + (e.amount || 0), 0);
        const totalPayments = payments.reduce((sum, p) => sum + (p.paidAmount || 0), 0);
        const netProfit = totalIncome - totalExpenses;

        return `
            <div class="report-summary">
                <h3>التقرير المالي الملخص</h3>
                <p>من ${Utils.formatDate(fromDate, 'DD/MM/YYYY')} إلى ${Utils.formatDate(toDate, 'DD/MM/YYYY')}</p>

                <div class="summary-stats">
                    <div class="summary-stat">
                        <label>إجمالي الإيرادات:</label>
                        <span class="positive">${Utils.formatCurrency(totalIncome)}</span>
                    </div>
                    <div class="summary-stat">
                        <label>إجمالي المصروفات:</label>
                        <span class="negative">${Utils.formatCurrency(totalExpenses)}</span>
                    </div>
                    <div class="summary-stat">
                        <label>إجمالي المدفوعات:</label>
                        <span class="info">${Utils.formatCurrency(totalPayments)}</span>
                    </div>
                    <div class="summary-stat total">
                        <label>صافي الربح:</label>
                        <span class="${netProfit >= 0 ? 'positive' : 'negative'}">${Utils.formatCurrency(netProfit)}</span>
                    </div>
                </div>

                <div class="report-actions">
                    <button class="btn btn-primary" onclick="Financial.printReport()">
                        <i class="fas fa-print"></i>
                        طباعة التقرير
                    </button>
                    <button class="btn btn-success" onclick="Financial.exportReport()">
                        <i class="fas fa-download"></i>
                        تصدير التقرير
                    </button>
                </div>
            </div>
        `;
    }

    // طباعة التقرير
    static printReport() {
        const reportContent = document.getElementById('report-content');
        if (!reportContent) return;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>التقرير المالي</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .positive { color: #27ae60; }
                    .negative { color: #e74c3c; }
                    .info { color: #3498db; }
                    .summary-stat { display: flex; justify-content: space-between; margin-bottom: 10px; }
                    .total { border-top: 2px solid #000; padding-top: 10px; font-weight: bold; }
                </style>
            </head>
            <body>
                ${reportContent.innerHTML}
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.print();
    }
}
