/* الإجراءات السريعة والبحث السريع */

/* قسم الإجراءات السريعة */
.quick-actions-section {
    margin-bottom: var(--spacing-2xl);
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.quick-actions-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
}

.section-title {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.section-title i {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* شبكة الإجراءات السريعة */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
}

/* بطاقة الإجراء السريع */
.quick-action-card {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    min-height: 100px;
}

.quick-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left var(--transition-slow);
}

.quick-action-card:hover::before {
    left: 100%;
}

.quick-action-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.quick-action-card:active {
    transform: translateY(-2px) scale(1.01);
}

/* أيقونة الإجراء */
.action-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: var(--text-white);
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.action-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    filter: blur(10px);
    opacity: 0.3;
    z-index: -1;
}

.action-icon.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.action-icon.success {
    background: linear-gradient(135deg, var(--success-color), #45b7aa);
}

.action-icon.warning {
    background: linear-gradient(135deg, var(--warning-color), #ffd93d);
    color: var(--text-primary);
}

.action-icon.info {
    background: linear-gradient(135deg, var(--info-color), #339af0);
}

.action-icon.secondary {
    background: linear-gradient(135deg, var(--secondary-color), #6a4c93);
}

.action-icon.danger {
    background: linear-gradient(135deg, var(--error-color), #ff5252);
}

.action-icon.dark {
    background: linear-gradient(135deg, #2c3e50, #34495e);
}

.action-icon.gradient {
    background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* محتوى الإجراء */
.action-content {
    flex: 1;
}

.action-content h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.action-content p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.4;
}

/* سهم الإجراء */
.action-arrow {
    color: var(--text-muted);
    font-size: var(--font-size-lg);
    transition: all var(--transition-fast);
}

.quick-action-card:hover .action-arrow {
    color: var(--primary-color);
    transform: translateX(-5px);
}

/* البحث السريع */
.quick-search-modal {
    max-width: 100%;
}

.search-input-container {
    margin-bottom: var(--spacing-xl);
}

.search-box-large {
    position: relative;
    max-width: 100%;
}

.search-box-large i {
    position: absolute;
    right: var(--spacing-lg);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    font-size: var(--font-size-xl);
    z-index: 2;
}

.search-box-large input {
    width: 100%;
    padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-lg) 60px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-lg);
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all var(--transition-normal);
}

.search-box-large input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: var(--bg-secondary);
}

/* فلاتر البحث */
.search-filters {
    margin-bottom: var(--spacing-xl);
}

.filter-chips {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.filter-chip {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-chip:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.filter-chip.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-white);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.filter-chip i {
    font-size: var(--font-size-sm);
}

/* نتائج البحث */
.search-results {
    min-height: 300px;
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    background: var(--bg-primary);
}

.search-placeholder,
.search-loading,
.search-no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
    text-align: center;
    min-height: 200px;
}

.search-placeholder i,
.search-loading i,
.search-no-results i {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-md);
    color: var(--text-muted);
}

.search-loading i {
    color: var(--primary-color);
    animation: spin 1s linear infinite;
}

.search-results-header {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.search-results-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.search-results-list {
    padding: var(--spacing-sm);
}

/* عنصر نتيجة البحث */
.search-result-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-fast);
    margin-bottom: var(--spacing-sm);
}

.search-result-item:hover {
    background: var(--bg-secondary);
    transform: translateX(-5px);
    box-shadow: var(--shadow-sm);
}

.result-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.result-content {
    flex: 1;
}

.result-content h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.result-content p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.result-status {
    margin-left: var(--spacing-md);
}

.result-arrow {
    color: var(--text-muted);
    font-size: var(--font-size-base);
    transition: all var(--transition-fast);
}

.search-result-item:hover .result-arrow {
    color: var(--primary-color);
    transform: translateX(-3px);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 1024px) and (min-width: 769px) {
    .quick-actions-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-md);
    }
}
    
    .quick-action-card {
        padding: var(--spacing-lg);
        min-height: 80px;
    }
    
    .action-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-xl);
    }
    
    .action-content h3 {
        font-size: var(--font-size-base);
    }
    
    .action-content p {
        font-size: var(--font-size-xs);
    }
    
    .filter-chips {
        justify-content: center;
    }
    
    .search-box-large input {
        padding: var(--spacing-md) var(--spacing-lg) var(--spacing-md) 50px;
        font-size: var(--font-size-base);
    }
    
    .search-box-large i {
        right: var(--spacing-md);
        font-size: var(--font-size-lg);
    }
}

/* تأثيرات إضافية */
.quick-action-card {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

.quick-action-card:nth-child(1) { animation-delay: 0.1s; }
.quick-action-card:nth-child(2) { animation-delay: 0.2s; }
.quick-action-card:nth-child(3) { animation-delay: 0.3s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تأثير النبض للأيقونات */
.action-icon {
    animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.quick-action-card:hover .action-icon {
    animation: none;
    transform: scale(1.1);
}

/* تحسينات إضافية */
.search-result-item {
    border-left: 3px solid transparent;
    transition: all var(--transition-fast);
}

.search-result-item:hover {
    border-left-color: var(--primary-color);
}

.filter-chip {
    position: relative;
    overflow: hidden;
}

.filter-chip::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-normal);
}

.filter-chip:hover::before {
    left: 100%;
}

/* نافذة باقي الوظائف */
.more-actions-modal {
    max-width: 100%;
}

.actions-categories {
    display: grid;
    gap: var(--spacing-xl);
}

.action-category {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.category-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--border-color);
}

.category-title i {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.category-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
}

.action-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.action-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left var(--transition-normal);
}

.action-item:hover::before {
    left: 100%;
}

.action-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.action-item-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--text-white);
    flex-shrink: 0;
}

.action-item-icon.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.action-item-icon.success {
    background: linear-gradient(135deg, var(--success-color), #45b7aa);
}

.action-item-icon.warning {
    background: linear-gradient(135deg, var(--warning-color), #ffd93d);
    color: var(--text-primary);
}

.action-item-icon.info {
    background: linear-gradient(135deg, var(--info-color), #339af0);
}

.action-item-icon.secondary {
    background: linear-gradient(135deg, var(--secondary-color), #6a4c93);
}

.action-item-icon.danger {
    background: linear-gradient(135deg, var(--error-color), #ff5252);
}

.action-item-icon.dark {
    background: linear-gradient(135deg, #2c3e50, #34495e);
}

.action-item-icon.gradient {
    background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
    animation: gradientShift 3s ease-in-out infinite;
}

.action-item-content {
    flex: 1;
}

.action-item-content h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.action-item-content p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.4;
}

/* التصميم المتجاوب لنافذة باقي الوظائف */
@media (max-width: 768px) {
    .category-actions {
        grid-template-columns: 1fr;
    }

    .action-item {
        padding: var(--spacing-md);
    }

    .action-item-icon {
        width: 35px;
        height: 35px;
        font-size: var(--font-size-base);
    }

    .action-item-content h4 {
        font-size: var(--font-size-sm);
    }

    .action-item-content p {
        font-size: var(--font-size-xs);
    }
}

/* نوافذ تصدير البيانات */
.export-success-modal,
.error-modal {
    text-align: center;
    padding: var(--spacing-lg);
}

.success-icon,
.error-icon {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
}

.success-icon {
    color: var(--success-color);
}

.error-icon {
    color: var(--error-color);
}

.export-success-modal h3,
.error-modal h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.export-details {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
    border: 1px solid var(--border-color);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item .label {
    font-weight: 500;
    color: var(--text-secondary);
}

.detail-item .value {
    font-weight: 600;
    color: var(--text-primary);
}

.export-note {
    background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
    border-left: 4px solid var(--info-color);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
}

.export-note i {
    color: var(--info-color);
    font-size: var(--font-size-lg);
    margin-top: var(--spacing-xs);
}

.export-note p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    margin: 0;
}

.error-modal p {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
    line-height: 1.5;
    margin-bottom: var(--spacing-lg);
}
