// الإعدادات السريعة
class QuickSettings {
    static isOpen = false;
    static currentSettings = {};
    static isInitialized = false;
    static panelRightOffset = 50; // مقدار الإزاحة نحو اليمين بالبكسل
    static loginUpdateTimer = null; // تايمر تحديث شاشة تسجيل الدخول
    static isSaving = false; // منع الحفظ المتعدد المتزامن

    // تهيئة الإعدادات السريعة
    static init() {
        if (this.isInitialized) return;

        this.loadSettings();

        // تطبيق الإعدادات المحملة
        this.applySettings(this.currentSettings);

        this.setupEventListeners();
        this.isInitialized = true;

        console.log('تم تهيئة الإعدادات السريعة بنجاح مع الإعدادات:', this.currentSettings);
    }

    // تحميل الإعدادات
    static loadSettings() {
        try {
            console.log('🔄 بدء تحميل الإعدادات...');

            // محاولة تحميل الإعدادات من localStorage أولاً
            const localSettings = localStorage.getItem('labSettings');
            console.log('📦 البيانات الخام من localStorage:', localSettings);

            if (localSettings) {
                const parsedSettings = JSON.parse(localSettings);
                // دمج مع الإعدادات الافتراضية (الإعدادات المحفوظة لها الأولوية)
                this.currentSettings = { ...this.getDefaultSettings(), ...parsedSettings };
                console.log('✅ تم تحميل الإعدادات من localStorage:', this.currentSettings);
            } else {
                console.log('⚠️ لا توجد إعدادات في localStorage، محاولة التحميل من قاعدة البيانات...');

                // إذا لم توجد في localStorage، تحميل من قاعدة البيانات
                if (typeof Database !== 'undefined' && Database.getSettings) {
                    const dbSettings = Database.getSettings();
                    this.currentSettings = { ...this.getDefaultSettings(), ...dbSettings };
                    console.log('✅ تم تحميل الإعدادات من قاعدة البيانات:', this.currentSettings);

                    // حفظ في localStorage للمرة القادمة
                    localStorage.setItem('labSettings', JSON.stringify(this.currentSettings));
                    console.log('💾 تم حفظ الإعدادات في localStorage للمرة القادمة');
                } else {
                    console.log('⚠️ قاعدة البيانات غير متاحة، استخدام الإعدادات الافتراضية');
                    this.currentSettings = this.getDefaultSettings();
                }
            }

            console.log('🎉 انتهى تحميل الإعدادات بنجاح');

        } catch (error) {
            console.error('❌ خطأ في تحميل الإعدادات:', error);
            this.currentSettings = this.getDefaultSettings();
            console.log('🔄 تم استخدام الإعدادات الافتراضية بسبب الخطأ');
        }
    }

    // الحصول على الإعدادات الافتراضية
    static getDefaultSettings() {
        return {
            // معلومات المعمل الأساسية
            labName: 'معمل الأسنان المتخصص',
            labSubtitle: 'نظام الإدارة المتطور',
            labDescription: 'معمل متخصص في تركيبات الأسنان والتقويم',

            // معلومات الاتصال
            labPhone: '',
            labMobile: '',
            labEmail: '',
            labWebsite: '',
            labAddress: '',

            // الإعدادات المالية
            currency: 'جنيه',
            taxRate: 14,
            labLicense: '',

            // إعدادات النظام
            notifications: true,
            sounds: false,
            autoSave: true,
            sessionTimeout: 60,
            autoBackup: false,
            backupInterval: 7,
            maxBackups: 10,

            // إعدادات اللغة والمنطقة
            language: 'ar',
            dateFormat: 'DD/MM/YYYY',
            showHijri: true,
            timezone: 'Africa/Cairo',

            // إعدادات المظهر
            fontSize: 16,
            animations: true,
            showSidebar: true
        };
    }

    // إعداد مستمعي الأحداث
    static setupEventListeners() {
        const settingsBtn = document.getElementById('quickSettingsBtn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.toggleQuickSettings();
            });
        }

        // إغلاق عند النقر خارج النافذة
        document.addEventListener('click', (event) => {
            const settingsPanel = document.querySelector('.quick-settings-panel');
            const settingsBtn = document.getElementById('quickSettingsBtn');

            if (this.isOpen && settingsPanel &&
                !settingsPanel.contains(event.target) &&
                !settingsBtn.contains(event.target)) {
                this.closeQuickSettings();
            }
        });

        // إغلاق عند الضغط على Escape
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.isOpen) {
                this.closeQuickSettings();
            }
        });

        // اختصار لوحة المفاتيح (Ctrl + ,)
        document.addEventListener('keydown', (event) => {
            if (event.ctrlKey && event.key === ',') {
                event.preventDefault();
                this.toggleQuickSettings();
            }
        });

        // إعادة تعديل موضع النافذة عند تغيير حجم النافذة
        window.addEventListener('resize', () => {
            if (this.isOpen) {
                const panel = document.querySelector('.quick-settings-panel');
                if (panel) {
                    // إعادة تحديد الموضع بناءً على الزر
                    this.positionPanelRelativeToButton(panel);

                    // إعادة تطبيق التعديلات
                    setTimeout(() => {
                        this.adjustPanelPosition(panel);
                    }, 100);
                }
            }
        });
    }

    // تبديل نافذة الإعدادات السريعة
    static toggleQuickSettings() {
        console.log('تبديل الإعدادات، الحالة الحالية:', this.isOpen);
        try {
            if (this.isOpen) {
                this.closeQuickSettings();
            } else {
                this.openQuickSettings();
            }
        } catch (error) {
            console.error('خطأ في تبديل الإعدادات:', error);
        }
    }

    // فتح نافذة الإعدادات السريعة
    static openQuickSettings() {
        console.log('فتح نافذة الإعدادات...');
        try {
            this.createQuickSettingsPanel();
            this.isOpen = true;

            const settingsBtn = document.getElementById('quickSettingsBtn');
            if (settingsBtn) {
                settingsBtn.classList.add('active');
                console.log('تم تفعيل زر الإعدادات');
            } else {
                console.warn('لم يتم العثور على زر الإعدادات');
            }
        } catch (error) {
            console.error('خطأ في فتح الإعدادات:', error);
        }
    }

    // إغلاق نافذة الإعدادات السريعة
    static closeQuickSettings() {
        const panel = document.querySelector('.quick-settings-panel');
        if (panel) {
            panel.classList.add('closing');
            setTimeout(() => {
                panel.remove();
            }, 300);
        }
        
        this.isOpen = false;

        // إعادة تعيين حالة الحفظ عند الإغلاق
        this.isSaving = false;
        console.log('🔓 تم إعادة تعيين isSaving عند الإغلاق');

        const settingsBtn = document.getElementById('quickSettingsBtn');
        if (settingsBtn) {
            settingsBtn.classList.remove('active');
        }
    }

    // إنشاء لوحة الإعدادات السريعة
    static createQuickSettingsPanel() {
        console.log('إنشاء لوحة الإعدادات...');

        try {
            // إزالة اللوحة الموجودة إن وجدت
            const existingPanel = document.querySelector('.quick-settings-panel');
            if (existingPanel) {
                console.log('إزالة النافذة الموجودة');
                existingPanel.remove();
            }

            const panel = document.createElement('div');
            panel.className = 'quick-settings-panel';
            panel.innerHTML = this.getQuickSettingsHTML();

            console.log('تم إنشاء النافذة، إضافتها للصفحة...');

            // إضافة النافذة للصفحة مباشرة
            document.body.appendChild(panel);

            console.log('تم إضافة النافذة، تحديد الموضع...');

            // تحديد موضع النافذة بناءً على موضع الزر
            this.positionPanelRelativeToButton(panel);

            // تحديث القيم
            this.updateSettingsValues();

            // تحديث القيم من localStorage للتأكد من عرض أحدث البيانات
            this.loadSettingsFromLocalStorage();

            // إعداد مستمعي الأحداث للإعدادات
            this.setupSettingsEventListeners();

            console.log('تطبيق تأثير الظهور...');

            // تأثير الظهور
            setTimeout(() => {
                panel.classList.add('show');
                console.log('تم تطبيق فئة show');
                // تعديل موضع النافذة إذا كانت ستخرج من الشاشة
                this.adjustPanelPosition(panel);
            }, 10);

        } catch (error) {
            console.error('خطأ في إنشاء لوحة الإعدادات:', error);
        }
    }

    // تحديد موضع النافذة بناءً على موضع الزر
    static positionPanelRelativeToButton(panel) {
        const settingsBtn = document.getElementById('quickSettingsBtn');
        if (!settingsBtn || !panel) {
            console.warn('لا يمكن تحديد موضع النافذة - الزر أو النافذة غير موجود');
            return;
        }

        const btnRect = settingsBtn.getBoundingClientRect();
        console.log('موضع الزر:', btnRect);

        // تحريك النافذة نحو اليمين من موضع الزر
        const rightOffset = this.panelRightOffset;

        // تعيين موضع النافذة
        panel.style.position = 'fixed';
        panel.style.top = (btnRect.bottom + 10) + 'px';
        panel.style.right = (window.innerWidth - btnRect.right + rightOffset) + 'px';
        panel.style.zIndex = '1000';

        console.log('تم تعيين موضع النافذة:', {
            top: panel.style.top,
            right: panel.style.right,
            rightOffset: rightOffset,
            position: panel.style.position
        });
    }

    // HTML لوحة الإعدادات السريعة
    static getQuickSettingsHTML() {
        return `
            <div class="quick-settings-header">
                <h3>
                    <i class="fas fa-cog"></i>
                    الإعدادات السريعة
                </h3>
                <button class="close-btn" onclick="QuickSettings.closeQuickSettings()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="quick-settings-body">
                <!-- القائمة الجانبية -->
                <div class="settings-sidebar">
                    <div class="settings-nav">
                        <button class="settings-nav-item active" data-tab="general">
                            <i class="fas fa-home"></i>
                            <span>عام</span>
                        </button>
                        <button class="settings-nav-item" data-tab="lab-info">
                            <i class="fas fa-building"></i>
                            <span>بيانات المعمل</span>
                        </button>
                        <button class="settings-nav-item" data-tab="appearance">
                            <i class="fas fa-palette"></i>
                            <span>المظهر</span>
                        </button>
                        <button class="settings-nav-item" data-tab="system">
                            <i class="fas fa-cogs"></i>
                            <span>النظام</span>
                        </button>
                        <button class="settings-nav-item" data-tab="language">
                            <i class="fas fa-globe"></i>
                            <span>اللغة والمنطقة</span>
                        </button>
                        <button class="settings-nav-item" data-tab="backup">
                            <i class="fas fa-shield-alt"></i>
                            <span>النسخ الاحتياطي</span>
                        </button>
                    </div>
                </div>

                <!-- محتوى الإعدادات -->
                <div class="settings-content">
                    <!-- تبويب عام -->
                    <div class="settings-tab active" data-tab="general">
                        <div class="settings-section">
                            <h4>
                                <i class="fas fa-tachometer-alt"></i>
                                الإعدادات العامة
                            </h4>

                            <div class="setting-item">
                                <label>الوضع</label>
                                <div class="setting-control">
                                    <button class="theme-option ${ThemeManager?.getCurrentTheme() === 'light' ? 'active' : ''}"
                                            data-theme="light">
                                        <i class="fas fa-sun"></i>
                                        نهاري
                                    </button>
                                    <button class="theme-option ${ThemeManager?.getCurrentTheme() === 'dark' ? 'active' : ''}"
                                            data-theme="dark">
                                        <i class="fas fa-moon"></i>
                                        ليلي
                                    </button>
                                </div>
                            </div>

                            <div class="setting-item">
                                <label>الإشعارات</label>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="notifications" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="setting-item">
                                <label for="language">اللغة</label>
                                <div class="setting-control">
                                    <select id="language">
                                        <option value="ar">العربية</option>
                                        <option value="en">English</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب بيانات المعمل -->
                    <div class="settings-tab" data-tab="lab-info">
                        <div class="settings-section">
                            <h4>
                                <i class="fas fa-building"></i>
                                معلومات المعمل الأساسية
                            </h4>

                            <div class="setting-item">
                                <label for="settingsLabName">اسم المعمل</label>
                                <div class="setting-control">
                                    <input type="text" id="settingsLabName" placeholder="معمل الأسنان المتخصص">
                                </div>
                            </div>

                            <div class="setting-item">
                                <label for="settingsLabSubtitle">العنوان الفرعي</label>
                                <div class="setting-control">
                                    <input type="text" id="settingsLabSubtitle" placeholder="نظام الإدارة المتطور">
                                </div>
                            </div>

                            <div class="setting-item">
                                <label for="settingsLabDescription">وصف المعمل</label>
                                <div class="setting-control">
                                    <textarea id="settingsLabDescription" rows="3" placeholder="وصف مختصر عن المعمل وخدماته"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h4>
                                <i class="fas fa-image"></i>
                                شعار المعمل
                            </h4>

                            <div class="logo-upload-section">
                                <div class="current-logo">
                                    <div class="logo-preview" id="logoPreview">
                                        <i class="fas fa-tooth"></i>
                                    </div>
                                    <div class="logo-info">
                                        <p>الشعار الحالي</p>
                                        <small>يُنصح بحجم 100x100 بكسل</small>
                                    </div>
                                </div>

                                <div class="logo-upload-controls">
                                    <input type="file" id="logoUpload" accept="image/*" style="display: none;">
                                    <button class="btn btn-primary" onclick="document.getElementById('logoUpload').click()">
                                        <i class="fas fa-upload"></i>
                                        رفع شعار جديد
                                    </button>
                                    <button class="btn btn-secondary" onclick="QuickSettings.resetLogo()">
                                        <i class="fas fa-undo"></i>
                                        استخدام الافتراضي
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h4>
                                <i class="fas fa-address-card"></i>
                                معلومات الاتصال
                            </h4>

                            <div class="setting-item">
                                <label for="labPhone">رقم الهاتف</label>
                                <div class="setting-control">
                                    <input type="tel" id="labPhone" placeholder="+20 ************">
                                </div>
                            </div>

                            <div class="setting-item">
                                <label for="labMobile">رقم الجوال</label>
                                <div class="setting-control">
                                    <input type="tel" id="labMobile" placeholder="+20 ************">
                                </div>
                            </div>

                            <div class="setting-item">
                                <label for="labEmail">البريد الإلكتروني</label>
                                <div class="setting-control">
                                    <input type="email" id="labEmail" placeholder="<EMAIL>">
                                </div>
                            </div>

                            <div class="setting-item">
                                <label for="labWebsite">الموقع الإلكتروني</label>
                                <div class="setting-control">
                                    <input type="url" id="labWebsite" placeholder="https://www.dentallab.com">
                                </div>
                            </div>

                            <div class="setting-item">
                                <label for="labAddress">العنوان</label>
                                <div class="setting-control">
                                    <textarea id="labAddress" rows="3" placeholder="العنوان الكامل للمعمل"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h4>
                                <i class="fas fa-money-bill-wave"></i>
                                الإعدادات المالية
                            </h4>

                            <div class="setting-item">
                                <label for="currency">العملة</label>
                                <div class="setting-control">
                                    <select id="currency">
                                        <option value="جنيه">جنيه مصري (EGP)</option>
                                        <option value="ريال">ريال سعودي (SAR)</option>
                                        <option value="درهم">درهم إماراتي (AED)</option>
                                        <option value="دولار">دولار أمريكي (USD)</option>
                                        <option value="دينار">دينار كويتي (KWD)</option>
                                    </select>
                                </div>
                            </div>

                            <div class="setting-item">
                                <label for="taxRate">معدل الضريبة (%)</label>
                                <div class="setting-control">
                                    <input type="number" id="taxRate" min="0" max="100" step="0.1" placeholder="14">
                                </div>
                            </div>

                            <div class="setting-item">
                                <label for="labLicense">رقم الترخيص</label>
                                <div class="setting-control">
                                    <input type="text" id="labLicense" placeholder="رقم ترخيص المعمل">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب المظهر -->
                    <div class="settings-tab" data-tab="appearance">
                        <div class="settings-section">
                            <h4>
                                <i class="fas fa-palette"></i>
                                المظهر والعرض
                            </h4>

                            <div class="setting-item">
                                <label>الوضع</label>
                                <div class="setting-control">
                                    <button class="theme-option ${ThemeManager?.getCurrentTheme() === 'light' ? 'active' : ''}"
                                            data-theme="light">
                                        <i class="fas fa-sun"></i>
                                        نهاري
                                    </button>
                                    <button class="theme-option ${ThemeManager?.getCurrentTheme() === 'dark' ? 'active' : ''}"
                                            data-theme="dark">
                                        <i class="fas fa-moon"></i>
                                        ليلي
                                    </button>
                                </div>
                            </div>

                            <div class="setting-item">
                                <label for="fontSize">حجم الخط</label>
                                <div class="setting-control">
                                    <input type="range" id="fontSize" min="12" max="20" value="16" step="1">
                                    <span class="range-value">16px</span>
                                </div>
                            </div>

                            <div class="setting-item">
                                <label>الرسوم المتحركة</label>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="animations" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="setting-item">
                                <label>عرض الشريط الجانبي</label>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="showSidebar" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب النظام -->
                    <div class="settings-tab" data-tab="system">
                        <div class="settings-section">
                            <h4>
                                <i class="fas fa-cogs"></i>
                                إعدادات النظام
                            </h4>

                            <div class="setting-item">
                                <label>الأصوات</label>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="sounds">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="setting-item">
                                <label>حفظ تلقائي</label>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="autoSave" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="setting-item">
                                <label for="sessionTimeout">انتهاء الجلسة (دقائق)</label>
                                <div class="setting-control">
                                    <input type="number" id="sessionTimeout" min="5" max="480" value="60">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب اللغة والمنطقة -->
                    <div class="settings-tab" data-tab="language">
                        <div class="settings-section">
                            <h4>
                                <i class="fas fa-globe"></i>
                                اللغة والمنطقة
                            </h4>

                            <div class="setting-item">
                                <label for="dateFormat">تنسيق التاريخ</label>
                                <div class="setting-control">
                                    <select id="dateFormat">
                                        <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                                        <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                                        <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                                    </select>
                                </div>
                            </div>

                            <div class="setting-item">
                                <label>عرض التاريخ الهجري</label>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="showHijri" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="setting-item">
                                <label for="timezone">المنطقة الزمنية</label>
                                <div class="setting-control">
                                    <select id="timezone">
                                        <option value="Africa/Cairo">القاهرة (GMT+2)</option>
                                        <option value="Asia/Riyadh">الرياض (GMT+3)</option>
                                        <option value="Asia/Dubai">دبي (GMT+4)</option>
                                        <option value="Asia/Kuwait">الكويت (GMT+3)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب النسخ الاحتياطي -->
                    <div class="settings-tab" data-tab="backup">
                        <div class="settings-section">
                            <h4>
                                <i class="fas fa-shield-alt"></i>
                                النسخ الاحتياطي والأمان
                            </h4>

                            <div class="setting-item">
                                <label>النسخ الاحتياطي التلقائي</label>
                                <div class="setting-control">
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="autoBackup">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="setting-item">
                                <label for="backupInterval">فترة النسخ الاحتياطي (أيام)</label>
                                <div class="setting-control">
                                    <input type="number" id="backupInterval" min="1" max="30" value="7">
                                </div>
                            </div>

                            <div class="setting-item">
                                <label for="maxBackups">عدد النسخ المحفوظة</label>
                                <div class="setting-control">
                                    <input type="number" id="maxBackups" min="1" max="50" value="10">
                                </div>
                            </div>

                            <div class="backup-actions">
                                <button class="btn btn-primary">
                                    <i class="fas fa-download"></i>
                                    إنشاء نسخة احتياطية الآن
                                </button>
                                <button class="btn btn-secondary">
                                    <i class="fas fa-upload"></i>
                                    استعادة من نسخة احتياطية
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="quick-settings-footer">
                <button class="btn btn-primary" onclick="QuickSettings.saveSettingsBasic();">
                    <i class="fas fa-save"></i>
                    حفظ الإعدادات
                </button>
                <button class="btn btn-secondary" onclick="QuickSettings.resetSettings()">
                    <i class="fas fa-undo"></i>
                    إعادة تعيين
                </button>
                <button class="btn btn-info" onclick="QuickSettings.openFullSettings()">
                    <i class="fas fa-cog"></i>
                    الإعدادات الكاملة
                </button>
            </div>
        `;
    }

    // تحميل الإعدادات من localStorage وتحديث النموذج
    static loadSettingsFromLocalStorage() {
        try {
            console.log('📦 تحميل الإعدادات من localStorage...');
            const savedData = localStorage.getItem('labSettings');
            if (savedData) {
                const savedSettings = JSON.parse(savedData);
                console.log('✅ تم تحميل الإعدادات من localStorage:', savedSettings);

                // دمج الإعدادات المحفوظة مع الحالية (بحذر لتجنب الحلقات اللا نهائية)
                this.currentSettings = {
                    ...this.currentSettings,
                    ...savedSettings,
                    // تجنب نسخ الوظائف أو المراجع الدائرية
                    lastUpdated: savedSettings.lastUpdated || new Date().toISOString()
                };
                console.log('🔄 تم دمج الإعدادات بأمان');
            } else {
                console.log('⚠️ لا توجد إعدادات محفوظة في localStorage');
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل الإعدادات من localStorage:', error);
            // في حالة الخطأ، لا نغير currentSettings
        }
    }

    // تحديث قيم الإعدادات
    static updateSettingsValues() {
        console.log('🔄 بدء تحديث قيم الإعدادات في النموذج...');
        const settings = this.currentSettings;
        console.log('📋 الإعدادات الحالية:', settings);

        // معلومات المعمل الأساسية
        const labNameInput = document.getElementById('settingsLabName');
        if (labNameInput) {
            labNameInput.value = settings.labName || '';
            console.log('✅ تم تحديث اسم المعمل في النموذج:', labNameInput.value);
        } else {
            console.warn('❌ لم يتم العثور على حقل اسم المعمل');
        }

        const labSubtitleInput = document.getElementById('settingsLabSubtitle');
        if (labSubtitleInput) {
            labSubtitleInput.value = settings.labSubtitle || '';
            console.log('✅ تم تحديث العنوان الفرعي في النموذج:', labSubtitleInput.value);
        } else {
            console.warn('❌ لم يتم العثور على حقل العنوان الفرعي');
        }

        const labDescriptionInput = document.getElementById('settingsLabDescription');
        if (labDescriptionInput) {
            labDescriptionInput.value = settings.labDescription || '';
            console.log('✅ تم تحديث وصف المعمل في النموذج:', labDescriptionInput.value);
        } else {
            console.warn('❌ لم يتم العثور على حقل وصف المعمل');
        }

        // الشعار
        const logoPreview = document.getElementById('logoPreview');
        if (logoPreview && settings.labLogo) {
            logoPreview.innerHTML = `<img src="${settings.labLogo}" alt="شعار المعمل">`;
        }

        // معلومات الاتصال
        const contactFields = {
            'labPhone': settings.labPhone || '',
            'labMobile': settings.labMobile || '',
            'labEmail': settings.labEmail || '',
            'labWebsite': settings.labWebsite || '',
            'labAddress': settings.labAddress || ''
        };

        Object.entries(contactFields).forEach(([fieldId, value]) => {
            const field = document.getElementById(fieldId);
            if (field) field.value = value;
        });

        // الإعدادات المالية
        const currencySelect = document.getElementById('currency');
        if (currencySelect) currencySelect.value = settings.currency || 'جنيه';

        const taxRateInput = document.getElementById('taxRate');
        if (taxRateInput) taxRateInput.value = settings.taxRate || '';

        const labLicenseInput = document.getElementById('labLicense');
        if (labLicenseInput) labLicenseInput.value = settings.labLicense || '';

        // إعدادات النظام
        const notificationsToggle = document.getElementById('notifications');
        if (notificationsToggle) notificationsToggle.checked = settings.notifications !== false;

        const soundsToggle = document.getElementById('sounds');
        if (soundsToggle) soundsToggle.checked = settings.sounds || false;

        const autoSaveToggle = document.getElementById('autoSave');
        if (autoSaveToggle) autoSaveToggle.checked = settings.autoSave !== false;

        const sessionTimeoutInput = document.getElementById('sessionTimeout');
        if (sessionTimeoutInput) sessionTimeoutInput.value = settings.sessionTimeout || 60;

        const autoBackupToggle = document.getElementById('autoBackup');
        if (autoBackupToggle) autoBackupToggle.checked = settings.autoBackup || false;

        const backupIntervalInput = document.getElementById('backupInterval');
        if (backupIntervalInput) backupIntervalInput.value = settings.backupInterval || 7;

        const maxBackupsInput = document.getElementById('maxBackups');
        if (maxBackupsInput) maxBackupsInput.value = settings.maxBackups || 10;

        // إعدادات اللغة والمنطقة
        const languageSelect = document.getElementById('language');
        if (languageSelect) languageSelect.value = settings.language || 'ar';

        const dateFormatSelect = document.getElementById('dateFormat');
        if (dateFormatSelect) dateFormatSelect.value = settings.dateFormat || 'DD/MM/YYYY';

        const showHijriToggle = document.getElementById('showHijri');
        if (showHijriToggle) showHijriToggle.checked = settings.showHijri !== false;

        const timezoneSelect = document.getElementById('timezone');
        if (timezoneSelect) timezoneSelect.value = settings.timezone || 'Africa/Cairo';

        // إعدادات المظهر
        const fontSizeRange = document.getElementById('fontSize');
        if (fontSizeRange) {
            fontSizeRange.value = settings.fontSize || 16;
            this.updateFontSizeDisplay(fontSizeRange.value);
        }

        const animationsToggle = document.getElementById('animations');
        if (animationsToggle) animationsToggle.checked = settings.animations !== false;

        const showSidebarToggle = document.getElementById('showSidebar');
        if (showSidebarToggle) showSidebarToggle.checked = settings.showSidebar !== false;
    }

    // إعداد مستمعي أحداث الإعدادات
    static setupSettingsEventListeners() {
        // التبويبات
        document.querySelectorAll('.settings-nav-item').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabName = e.target.closest('.settings-nav-item').getAttribute('data-tab');
                this.switchTab(tabName);
            });
        });

        // أزرار الوضع
        document.querySelectorAll('.theme-option').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const theme = e.target.closest('.theme-option').getAttribute('data-theme');
                if (typeof ThemeManager !== 'undefined') {
                    ThemeManager.setTheme(theme);
                    this.updateThemeButtons();
                }
            });
        });

        // حجم الخط
        const fontSizeRange = document.getElementById('fontSize');
        if (fontSizeRange) {
            fontSizeRange.addEventListener('input', (e) => {
                this.updateFontSizeDisplay(e.target.value);
                this.applyFontSize(e.target.value);
            });
        }

        // تحديث فوري لاسم المعمل (بدون حفظ تلقائي)
        const labNameInput = document.getElementById('settingsLabName');
        if (labNameInput) {
            console.log('✅ تم العثور على حقل اسم المعمل، إضافة مستمع الأحداث');
            labNameInput.addEventListener('input', (e) => {
                console.log('🎯 تم تغيير اسم المعمل إلى:', e.target.value);
                // تحديث العرض فقط بدون حفظ تلقائي
                this.updateLabNameDisplayOnly(e.target.value);
            });
        } else {
            console.warn('⚠️ لم يتم العثور على حقل اسم المعمل (settingsLabName)');
        }

        const labSubtitleInput = document.getElementById('settingsLabSubtitle');
        if (labSubtitleInput) {
            labSubtitleInput.addEventListener('input', (e) => {
                // تحديث العرض فقط بدون حفظ تلقائي
                this.updateLabSubtitleDisplayOnly(e.target.value);
            });
        }

        // رفع الشعار
        const logoUpload = document.getElementById('logoUpload');
        if (logoUpload) {
            console.log('✅ تم العثور على حقل رفع الشعار، إضافة مستمع الأحداث');
            logoUpload.addEventListener('change', (e) => {
                console.log('🎯 تم اختيار ملف شعار جديد');
                this.handleLogoUpload(e);
            });
        } else {
            console.warn('⚠️ لم يتم العثور على حقل رفع الشعار (logoUpload)');
        }

        // تحديث فوري لمعلومات الاتصال
        this.setupContactInfoListeners();
    }

    // تبديل التبويبات
    static switchTab(tabName) {
        // إزالة الفئة النشطة من جميع التبويبات
        document.querySelectorAll('.settings-nav-item').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelectorAll('.settings-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        // إضافة الفئة النشطة للتبويب المحدد
        const activeNavItem = document.querySelector(`[data-tab="${tabName}"]`);
        const activeTab = document.querySelector(`.settings-tab[data-tab="${tabName}"]`);

        if (activeNavItem) activeNavItem.classList.add('active');
        if (activeTab) activeTab.classList.add('active');
    }

    // معالجة رفع الشعار
    static handleLogoUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        // التحقق من نوع الملف
        if (!file.type.startsWith('image/')) {
            if (typeof Utils !== 'undefined') {
                Utils.showNotification('يرجى اختيار ملف صورة صالح', 'error');
            }
            return;
        }

        // التحقق من حجم الملف (أقل من 2MB)
        if (file.size > 2 * 1024 * 1024) {
            if (typeof Utils !== 'undefined') {
                Utils.showNotification('حجم الملف كبير جداً. يرجى اختيار صورة أقل من 2MB', 'error');
            }
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            const logoData = e.target.result;
            console.log('🖼️ تم رفع شعار جديد');

            const logoPreview = document.getElementById('logoPreview');
            if (logoPreview) {
                logoPreview.innerHTML = `<img src="${logoData}" alt="شعار المعمل">`;
            }

            // تحديث الشعار في الواجهة الرئيسية
            this.updateMainLogo(logoData);

            // حفظ الشعار في الإعدادات
            this.currentSettings.labLogo = logoData;

            // حفظ فوري في localStorage
            this.saveLogoToLocalStorage(logoData);

            // تحديث شاشة تسجيل الدخول فوراً
            this.updateLoginScreenLogo(logoData);

            // إظهار إشعار النجاح
            if (typeof Utils !== 'undefined') {
                Utils.showNotification('تم رفع الشعار بنجاح', 'success');
            }
        };
        reader.readAsDataURL(file);
    }

    // إعادة تعيين الشعار للافتراضي
    static resetLogo() {
        console.log('🔄 إعادة تعيين الشعار للافتراضي');

        const logoPreview = document.getElementById('logoPreview');
        if (logoPreview) {
            logoPreview.innerHTML = '<i class="fas fa-tooth"></i>';
        }

        // تحديث الشعار في الواجهة الرئيسية
        this.updateMainLogo(null);

        // إزالة الشعار من الإعدادات
        delete this.currentSettings.labLogo;

        // حفظ فوري في localStorage
        this.saveLogoToLocalStorage(null);

        // تحديث شاشة تسجيل الدخول فوراً
        this.updateLoginScreenLogo(null);

        // إظهار إشعار
        if (typeof Utils !== 'undefined') {
            Utils.showNotification('تم إعادة تعيين الشعار للافتراضي', 'success');
        }
    }

    // تحديث الشعار في الواجهة الرئيسية
    static updateMainLogo(logoSrc) {
        const mainLogo = document.querySelector('.lab-logo');
        if (mainLogo) {
            if (logoSrc) {
                mainLogo.innerHTML = `<img src="${logoSrc}" alt="شعار المعمل" style="width: 100%; height: 100%; object-fit: cover; border-radius: inherit;">`;
            } else {
                mainLogo.innerHTML = '<i class="fas fa-tooth"></i>';
            }
        }
    }

    // إعداد مستمعي معلومات الاتصال (بدون حفظ تلقائي)
    static setupContactInfoListeners() {
        const contactFields = ['labPhone', 'labMobile', 'labEmail', 'labWebsite', 'labAddress'];

        contactFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('input', (e) => {
                    console.log(`📞 تحديث ${fieldId}:`, e.target.value);

                    // تحديث الإعدادات الحالية فقط (بدون حفظ تلقائي)
                    this.currentSettings[fieldId] = e.target.value;
                });
            }
        });
    }

    // حفظ حقل واحد في localStorage فوراً (للاستخدام الداخلي فقط)
    static saveFieldToLocalStorage(fieldName, value) {
        try {
            const currentSettings = JSON.parse(localStorage.getItem('labSettings') || '{}');
            currentSettings[fieldName] = value;
            currentSettings.lastUpdated = new Date().toISOString();
            localStorage.setItem('labSettings', JSON.stringify(currentSettings));
            console.log(`💾 تم حفظ ${fieldName} فورياً في localStorage:`, value);
        } catch (error) {
            console.error(`❌ خطأ في حفظ ${fieldName}:`, error);
        }
    }

    // حفظ الشعار في localStorage فوراً
    static saveLogoToLocalStorage(logoData) {
        try {
            const currentSettings = JSON.parse(localStorage.getItem('labSettings') || '{}');
            currentSettings.labLogo = logoData;
            currentSettings.lastUpdated = new Date().toISOString();
            localStorage.setItem('labSettings', JSON.stringify(currentSettings));
            console.log('💾 تم حفظ الشعار فورياً في localStorage');
        } catch (error) {
            console.error('❌ خطأ في حفظ الشعار:', error);
        }
    }

    // إظهار مؤشر الحفظ التلقائي (معطل حالياً)
    static showAutoSaveIndicator(inputElement) {
        // تم تعطيل الحفظ التلقائي
        console.log('ℹ️ مؤشر الحفظ التلقائي معطل');
        return;
    }

    // تحديث عرض حجم الخط
    static updateFontSizeDisplay(value) {
        const rangeValue = document.querySelector('.range-value');
        if (rangeValue) {
            rangeValue.textContent = value + 'px';
        }
    }

    // تطبيق حجم الخط
    static applyFontSize(size) {
        document.documentElement.style.setProperty('--font-size-base', size + 'px');
    }

    // تحديث أزرار الوضع
    static updateThemeButtons() {
        const currentTheme = ThemeManager?.getCurrentTheme() || 'light';
        document.querySelectorAll('.theme-option').forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-theme') === currentTheme) {
                btn.classList.add('active');
            }
        });
    }

    // تحديث عرض اسم المعمل (مع الحفظ التلقائي)
    static updateLabNameDisplay(name) {
        if (!name || !name.trim()) return;

        console.log('🏷️ تحديث اسم المعمل فورياً:', name.trim());

        // تحديث في الشريط العلوي
        const labNameElements = document.querySelectorAll('#labName, .lab-name');
        labNameElements.forEach(element => {
            if (element) {
                element.textContent = name.trim();
                console.log('✅ تم تحديث اسم المعمل في الشريط العلوي');
            }
        });

        // تحديث في الشريط الجانبي
        const sidebarLabName = document.querySelector('.sidebar-header h2');
        if (sidebarLabName) {
            sidebarLabName.textContent = name.trim();
            console.log('✅ تم تحديث اسم المعمل في الشريط الجانبي');
        }

        // تحديث في شاشة تسجيل الدخول فوراً
        this.updateLoginScreenLabName(name.trim());

        // تحديث مباشر للعنصر في شاشة تسجيل الدخول
        this.forceUpdateLoginScreenElement(name.trim());

        // تحديث الإعدادات الحالية
        this.currentSettings.labName = name.trim();

        // حفظ فوري في localStorage للحفاظ على التغيير
        const currentSettings = JSON.parse(localStorage.getItem('labSettings') || '{}');
        currentSettings.labName = name.trim();
        currentSettings.lastUpdated = new Date().toISOString();
        localStorage.setItem('labSettings', JSON.stringify(currentSettings));
        console.log('💾 تم حفظ اسم المعمل فورياً في localStorage');

        // تحديث شاشة تسجيل الدخول مباشرة باستخدام الوظيفة العامة
        if (typeof window.updateLoginScreen === 'function') {
            setTimeout(() => {
                window.updateLoginScreen();
                console.log('🔄 تم تحديث شاشة تسجيل الدخول مباشرة');
            }, 100);
        }

        // تحديث دوري للتأكد من التحديث
        this.scheduleLoginScreenUpdate();
    }

    // تحديث عرض اسم المعمل (بدون حفظ تلقائي)
    static updateLabNameDisplayOnly(name) {
        if (!name || !name.trim()) return;

        console.log('🏷️ تحديث عرض اسم المعمل فقط (بدون حفظ):', name.trim());

        // تحديث في الشريط العلوي
        const labNameElements = document.querySelectorAll('#labName, .lab-name');
        labNameElements.forEach(element => {
            if (element) {
                element.textContent = name.trim();
                console.log('✅ تم تحديث اسم المعمل في الشريط العلوي');
            }
        });

        // تحديث في الشريط الجانبي
        const sidebarLabName = document.querySelector('.sidebar-header h2');
        if (sidebarLabName) {
            sidebarLabName.textContent = name.trim();
            console.log('✅ تم تحديث اسم المعمل في الشريط الجانبي');
        }

        // تحديث الإعدادات الحالية فقط (بدون حفظ)
        this.currentSettings.labName = name.trim();
    }

    // تحديث اسم المعمل في شاشة تسجيل الدخول فوراً
    static updateLoginScreenLabName(labName) {
        try {
            console.log('🔄 تحديث اسم المعمل في شاشة تسجيل الدخول:', labName);

            // تحديث عنوان النظام في شاشة تسجيل الدخول
            const systemTitle = document.getElementById('loginSystemTitle');
            if (systemTitle) {
                const newTitle = `نظام إدارة ${labName}`;
                systemTitle.textContent = newTitle;
                console.log('✅ تم تحديث عنوان النظام في شاشة تسجيل الدخول:', newTitle);
            } else {
                console.log('⚠️ عنصر عنوان النظام غير موجود في شاشة تسجيل الدخول');
            }

            // تحديث شاشة تسجيل الدخول باستخدام الوظيفة العامة
            if (typeof window.updateLoginScreen === 'function') {
                window.updateLoginScreen();
                console.log('✅ تم استدعاء updateLoginScreen العامة');
            }

            // إرسال حدث تحديث مخصص
            const updateEvent = new CustomEvent('loginLabNameUpdated', {
                detail: { labName },
                bubbles: true
            });
            document.dispatchEvent(updateEvent);
            console.log('📡 تم إرسال حدث تحديث اسم المعمل في شاشة تسجيل الدخول');

            // إرسال حدث storage مصطنع لتحفيز التحديث
            const storageEvent = new StorageEvent('storage', {
                key: 'labSettings',
                newValue: localStorage.getItem('labSettings'),
                oldValue: null,
                storageArea: localStorage
            });
            window.dispatchEvent(storageEvent);
            console.log('📡 تم إرسال حدث storage مصطنع');

        } catch (error) {
            console.error('❌ خطأ في تحديث اسم المعمل في شاشة تسجيل الدخول:', error);
        }
    }

    // فرض تحديث عنصر شاشة تسجيل الدخول مباشرة
    static forceUpdateLoginScreenElement(labName) {
        try {
            console.log('🎯 فرض تحديث عنصر شاشة تسجيل الدخول مباشرة:', labName);

            // البحث عن العنصر في جميع النوافذ والإطارات
            const windows = [window];
            if (window.parent && window.parent !== window) {
                windows.push(window.parent);
            }
            if (window.top && window.top !== window) {
                windows.push(window.top);
            }

            let updated = false;
            windows.forEach(win => {
                try {
                    const systemTitle = win.document.getElementById('loginSystemTitle');
                    if (systemTitle) {
                        const newTitle = `نظام إدارة ${labName}`;
                        systemTitle.textContent = newTitle;
                        console.log('✅ تم تحديث العنصر مباشرة في النافذة:', newTitle);
                        updated = true;
                    }
                } catch (e) {
                    // تجاهل أخطاء الوصول للنوافذ الأخرى
                }
            });

            if (!updated) {
                console.log('⚠️ لم يتم العثور على عنصر شاشة تسجيل الدخول في أي نافذة');
            }

        } catch (error) {
            console.error('❌ خطأ في فرض تحديث عنصر شاشة تسجيل الدخول:', error);
        }
    }

    // تحديث الشعار في شاشة تسجيل الدخول فوراً
    static updateLoginScreenLogo(logoData) {
        try {
            console.log('🖼️ تحديث الشعار في شاشة تسجيل الدخول');

            // تحديث الشعار في شاشة تسجيل الدخول
            const loginLogo = document.getElementById('loginLogo');
            if (loginLogo) {
                if (logoData) {
                    loginLogo.innerHTML = `<img src="${logoData}" alt="شعار المعمل" style="width: 100%; height: 100%; object-fit: cover; border-radius: inherit;">`;
                    console.log('✅ تم تحديث الشعار في شاشة تسجيل الدخول');
                } else {
                    loginLogo.innerHTML = '<i class="fas fa-tooth"></i>';
                    console.log('✅ تم إعادة تعيين الشعار الافتراضي في شاشة تسجيل الدخول');
                }
            } else {
                console.log('⚠️ عنصر الشعار غير موجود في شاشة تسجيل الدخول');
            }

            // تحديث شاشة تسجيل الدخول باستخدام الوظيفة العامة
            if (typeof window.updateLoginScreen === 'function') {
                setTimeout(() => {
                    window.updateLoginScreen();
                    console.log('🔄 تم تحديث شاشة تسجيل الدخول مباشرة');
                }, 100);
            }

            // إرسال حدث تحديث مخصص للشعار
            const updateEvent = new CustomEvent('loginLogoUpdated', {
                detail: { logoData },
                bubbles: true
            });
            document.dispatchEvent(updateEvent);
            console.log('📡 تم إرسال حدث تحديث الشعار في شاشة تسجيل الدخول');

        } catch (error) {
            console.error('❌ خطأ في تحديث الشعار في شاشة تسجيل الدخول:', error);
        }
    }

    // جدولة تحديث شاشة تسجيل الدخول
    static scheduleLoginScreenUpdate() {
        // إلغاء التحديث السابق إن وجد
        if (this.loginUpdateTimer) {
            clearTimeout(this.loginUpdateTimer);
        }

        // جدولة تحديث جديد
        this.loginUpdateTimer = setTimeout(() => {
            try {
                console.log('⏰ تحديث مجدول لشاشة تسجيل الدخول');

                // تحديث باستخدام الوظيفة العامة
                if (typeof window.updateLoginScreen === 'function') {
                    window.updateLoginScreen();
                }

                // تحديث مباشر للعنصر
                const settings = JSON.parse(localStorage.getItem('labSettings') || '{}');
                if (settings.labName) {
                    this.forceUpdateLoginScreenElement(settings.labName);
                }

            } catch (error) {
                console.error('❌ خطأ في التحديث المجدول:', error);
            }
        }, 500);
    }

    // تحديث عرض العنوان الفرعي (مع الحفظ التلقائي)
    static updateLabSubtitleDisplay(subtitle) {
        if (!subtitle || !subtitle.trim()) return;

        console.log('📝 تحديث العنوان الفرعي فورياً:', subtitle.trim());

        // تحديث في الشريط العلوي
        const labSubtitleElements = document.querySelectorAll('#labSubtitle, .lab-subtitle');
        labSubtitleElements.forEach(element => {
            if (element) {
                element.textContent = subtitle.trim();
                console.log('✅ تم تحديث العنوان الفرعي في الشريط العلوي');
            }
        });

        // تحديث في الشريط الجانبي
        const sidebarLabSubtitle = document.querySelector('.sidebar-header p');
        if (sidebarLabSubtitle) {
            sidebarLabSubtitle.textContent = subtitle.trim();
            console.log('✅ تم تحديث العنوان الفرعي في الشريط الجانبي');
        }

        // تحديث الإعدادات الحالية
        this.currentSettings.labSubtitle = subtitle.trim();

        // حفظ فوري في localStorage للحفاظ على التغيير
        const currentSettings = JSON.parse(localStorage.getItem('labSettings') || '{}');
        currentSettings.labSubtitle = subtitle.trim();
        currentSettings.lastUpdated = new Date().toISOString();
        localStorage.setItem('labSettings', JSON.stringify(currentSettings));
        console.log('💾 تم حفظ العنوان الفرعي فورياً في localStorage');
    }

    // تحديث عرض العنوان الفرعي (بدون حفظ تلقائي)
    static updateLabSubtitleDisplayOnly(subtitle) {
        if (!subtitle || !subtitle.trim()) return;

        console.log('📝 تحديث عرض العنوان الفرعي فقط (بدون حفظ):', subtitle.trim());

        // تحديث في الشريط العلوي
        const labSubtitleElements = document.querySelectorAll('#labSubtitle, .lab-subtitle');
        labSubtitleElements.forEach(element => {
            if (element) {
                element.textContent = subtitle.trim();
                console.log('✅ تم تحديث العنوان الفرعي في الشريط العلوي');
            }
        });

        // تحديث في الشريط الجانبي
        const sidebarLabSubtitle = document.querySelector('.sidebar-header p');
        if (sidebarLabSubtitle) {
            sidebarLabSubtitle.textContent = subtitle.trim();
            console.log('✅ تم تحديث العنوان الفرعي في الشريط الجانبي');
        }

        // تحديث الإعدادات الحالية فقط (بدون حفظ)
        this.currentSettings.labSubtitle = subtitle.trim();
    }

    // حفظ الإعدادات
    static saveSettings() {
        console.log('🚀 تم استدعاء saveSettings()');

        // منع الاستدعاءات المتعددة المتزامنة
        if (this.isSaving) {
            console.log('⚠️ عملية حفظ جارية بالفعل، تجاهل الطلب');
            console.log('🔍 حالة isSaving:', this.isSaving);
            return;
        }
        this.isSaving = true;
        console.log('🔒 تم تعيين isSaving إلى true');

        // فحص أولي للتأكد من أن كل شيء يعمل
        console.log('🔍 فحص أولي:');
        console.log('- typeof localStorage:', typeof localStorage);
        console.log('- typeof JSON:', typeof JSON);
        console.log('- typeof document:', typeof document);

        try {
            // تسجيل مفصل للتصحيح
            console.log('✅ بدء حفظ الإعدادات...');

            // التحقق من وجود العناصر المطلوبة
            console.log('🔍 فحص العناصر المطلوبة...');

            const labNameElement = document.getElementById('settingsLabName');
            const labSubtitleElement = document.getElementById('settingsLabSubtitle');
            const labDescriptionElement = document.getElementById('settingsLabDescription');

            console.log('📋 حالة العناصر:', {
                labNameElement: !!labNameElement,
                labSubtitleElement: !!labSubtitleElement,
                labDescriptionElement: !!labDescriptionElement
            });

            // قراءة القيم من النماذج
            const labNameValue = labNameElement?.value || '';
            const labSubtitleValue = labSubtitleElement?.value || '';
            const labDescriptionValue = labDescriptionElement?.value || '';

            console.log('📝 القيم المقروءة:', {
                labName: labNameValue,
                labSubtitle: labSubtitleValue,
                labDescription: labDescriptionValue
            });

            // تحميل الإعدادات المحفوظة من localStorage أولاً
            let savedSettings = {};
            try {
                const savedData = localStorage.getItem('labSettings');
                if (savedData) {
                    savedSettings = JSON.parse(savedData);
                    console.log('📦 الإعدادات المحفوظة من localStorage:', savedSettings);
                }
            } catch (error) {
                console.warn('⚠️ خطأ في تحميل الإعدادات من localStorage:', error);
            }

            // دمج الإعدادات: localStorage أولاً، ثم currentSettings، ثم القيم الجديدة
            console.log('🔧 بناء الإعدادات الجديدة...');
            console.log('📋 الإعدادات الحالية:', this.currentSettings);

            const newSettings = {
                // البدء بالإعدادات المحفوظة من localStorage
                ...savedSettings,
                // ثم الإعدادات الحالية
                ...this.currentSettings,

                // ثم الكتابة فوقها بالقيم الجديدة من النموذج
                // معلومات المعمل الأساسية
                labName: labNameValue || savedSettings.labName || this.currentSettings.labName || 'معمل الأسنان المتخصص',
                labSubtitle: labSubtitleValue || savedSettings.labSubtitle || this.currentSettings.labSubtitle || 'نظام الإدارة المتطور',
                labDescription: labDescriptionValue || savedSettings.labDescription || this.currentSettings.labDescription || '',

                // معلومات الاتصال
                labPhone: document.getElementById('labPhone')?.value || this.currentSettings.labPhone || '',
                labMobile: document.getElementById('labMobile')?.value || this.currentSettings.labMobile || '',
                labEmail: document.getElementById('labEmail')?.value || this.currentSettings.labEmail || '',
                labWebsite: document.getElementById('labWebsite')?.value || this.currentSettings.labWebsite || '',
                labAddress: document.getElementById('labAddress')?.value || this.currentSettings.labAddress || '',

                // الإعدادات المالية
                currency: document.getElementById('currency')?.value || this.currentSettings.currency || 'جنيه',
                taxRate: parseFloat(document.getElementById('taxRate')?.value) || this.currentSettings.taxRate || 0,
                labLicense: document.getElementById('labLicense')?.value || this.currentSettings.labLicense || '',

                // إعدادات النظام
                notifications: document.getElementById('notifications')?.checked ?? this.currentSettings.notifications ?? true,
                sounds: document.getElementById('sounds')?.checked ?? this.currentSettings.sounds ?? false,
                autoSave: document.getElementById('autoSave')?.checked ?? this.currentSettings.autoSave ?? true,
                sessionTimeout: parseInt(document.getElementById('sessionTimeout')?.value) || this.currentSettings.sessionTimeout || 60,
                autoBackup: document.getElementById('autoBackup')?.checked ?? this.currentSettings.autoBackup ?? false,
                backupInterval: parseInt(document.getElementById('backupInterval')?.value) || this.currentSettings.backupInterval || 7,
                maxBackups: parseInt(document.getElementById('maxBackups')?.value) || this.currentSettings.maxBackups || 10,

                // إعدادات اللغة والمنطقة
                language: document.getElementById('language')?.value || this.currentSettings.language || 'ar',
                dateFormat: document.getElementById('dateFormat')?.value || this.currentSettings.dateFormat || 'DD/MM/YYYY',
                showHijri: document.getElementById('showHijri')?.checked ?? this.currentSettings.showHijri ?? true,
                timezone: document.getElementById('timezone')?.value || this.currentSettings.timezone || 'Africa/Cairo',

                // إعدادات المظهر
                fontSize: parseInt(document.getElementById('fontSize')?.value) || this.currentSettings.fontSize || 16,
                animations: document.getElementById('animations')?.checked ?? this.currentSettings.animations ?? true,
                showSidebar: document.getElementById('showSidebar')?.checked ?? this.currentSettings.showSidebar ?? true,

                // إضافة timestamp للتتبع
                lastUpdated: new Date().toISOString()
            };

            console.log('الإعدادات الجديدة المجمعة:', newSettings);

            // حفظ في قاعدة البيانات
            if (typeof Database !== 'undefined') {
                const dbSaveResult = Database.saveSettings(newSettings);
                console.log('نتيجة حفظ قاعدة البيانات:', dbSaveResult);
            } else {
                console.warn('Database غير متاح، لن يتم الحفظ في قاعدة البيانات');
            }

            // تحديث الإعدادات الحالية
            this.currentSettings = newSettings;
            console.log('تم تحديث currentSettings:', this.currentSettings);

            // حفظ في localStorage أيضاً
            console.log('💾 محاولة حفظ الإعدادات في localStorage...');
            console.log('📦 البيانات للحفظ:', newSettings);

            const settingsString = JSON.stringify(newSettings);
            console.log('📝 البيانات كـ JSON:', settingsString.substring(0, 200) + '...');

            localStorage.setItem('labSettings', settingsString);
            console.log('✅ تم حفظ الإعدادات في localStorage بنجاح');

            // التحقق من الحفظ
            const verifySettings = localStorage.getItem('labSettings');
            console.log('🔍 التحقق من الحفظ في localStorage:', JSON.parse(verifySettings));

            // تطبيق الإعدادات
            this.applySettings(newSettings);

            // إظهار رسالة نجاح
            console.log('🔔 محاولة إظهار إشعار النجاح...');
            if (typeof Utils !== 'undefined') {
                console.log('✅ Utils متاح، إرسال إشعار...');

                // التحقق من وجود container الإشعارات
                const notificationsContainer = document.getElementById('notifications');
                console.log('📦 container الإشعارات موجود:', !!notificationsContainer);

                if (!notificationsContainer) {
                    console.warn('⚠️ container الإشعارات غير موجود، إنشاؤه...');
                    const container = document.createElement('div');
                    container.id = 'notifications';
                    container.className = 'notifications-container';
                    document.body.appendChild(container);
                    console.log('✅ تم إنشاء container الإشعارات');
                }

                Utils.showNotification('تم حفظ الإعدادات بنجاح', 'success');
                console.log('✅ تم إرسال إشعار النجاح');
            } else {
                console.warn('⚠️ Utils غير متاح، استخدام alert');
                alert('تم حفظ الإعدادات بنجاح');
            }

            // إرسال حدث تحديث بيانات المعمل
            this.dispatchLabInfoUpdateEvent(newSettings);

            // فرض تحديث شاشة تسجيل الدخول
            this.forceUpdateLoginScreen(newSettings);

            // فرض تحديث الواجهة الرئيسية فوراً
            this.forceUpdateMainInterface(newSettings);

            // إغلاق النافذة
            this.closeQuickSettings();

        } catch (error) {
            console.error('❌ خطأ في حفظ الإعدادات:', error);
            console.error('📍 تفاصيل الخطأ:', {
                message: error.message,
                stack: error.stack,
                name: error.name
            });

            // محاولة تحديد مكان الخطأ
            if (error.message.includes('getElementById')) {
                console.error('🔍 الخطأ متعلق بالعثور على عنصر في DOM');
            } else if (error.message.includes('JSON')) {
                console.error('🔍 الخطأ متعلق بتحليل أو تحويل JSON');
            } else if (error.message.includes('localStorage')) {
                console.error('🔍 الخطأ متعلق بـ localStorage');
            }

            console.log('🔔 محاولة إظهار إشعار الخطأ...');
            if (typeof Utils !== 'undefined') {
                console.log('✅ Utils متاح، إرسال إشعار خطأ...');

                // التحقق من وجود container الإشعارات
                const notificationsContainer = document.getElementById('notifications');
                console.log('📦 container الإشعارات موجود:', !!notificationsContainer);

                if (!notificationsContainer) {
                    console.warn('⚠️ container الإشعارات غير موجود، إنشاؤه...');
                    const container = document.createElement('div');
                    container.id = 'notifications';
                    container.className = 'notifications-container';
                    document.body.appendChild(container);
                    console.log('✅ تم إنشاء container الإشعارات');
                }

                Utils.showNotification(`فشل في حفظ الإعدادات: ${error.message}`, 'error');
                console.log('✅ تم إرسال إشعار الخطأ');
            } else {
                console.warn('⚠️ Utils غير متاح، استخدام alert');
                alert(`فشل في حفظ الإعدادات: ${error.message}`);
            }
        } finally {
            // إعادة تعيين حالة الحفظ
            this.isSaving = false;
            console.log('🔓 تم إعادة تعيين حالة الحفظ');
        }
    }

    // وظيفة حفظ مبسطة للاختبار
    static saveSettingsSimple() {
        console.log('🧪 اختبار حفظ مبسط...');

        try {
            // قراءة القيم الأساسية فقط
            const labNameElement = document.getElementById('settingsLabName');
            const labSubtitleElement = document.getElementById('settingsLabSubtitle');

            if (!labNameElement) {
                alert('لم يتم العثور على حقل اسم المعمل');
                return;
            }

            const labName = labNameElement.value || '';
            const labSubtitle = labSubtitleElement ? labSubtitleElement.value || '' : '';

            console.log('📝 القيم المقروءة:', { labName, labSubtitle });

            if (!labName.trim()) {
                alert('يرجى إدخال اسم المعمل');
                return;
            }

            // إنشاء كائن إعدادات بسيط
            const settings = {
                labName: labName.trim(),
                labSubtitle: labSubtitle.trim(),
                lastUpdated: new Date().toISOString()
            };

            console.log('💾 حفظ الإعدادات:', settings);

            // حفظ في localStorage فقط
            localStorage.setItem('labSettings', JSON.stringify(settings));
            console.log('✅ تم الحفظ في localStorage');

            // تحديث بسيط للعرض
            const labNameElements = document.querySelectorAll('#labName, .lab-name');
            labNameElements.forEach(element => {
                if (element) {
                    element.textContent = settings.labName;
                }
            });

            alert('تم حفظ الإعدادات بنجاح');

            // إغلاق النافذة
            const panel = document.querySelector('.quick-settings-panel');
            if (panel) {
                panel.remove();
                this.isOpen = false;
            }

        } catch (error) {
            console.error('❌ خطأ في الحفظ المبسط:', error);
            alert('فشل في حفظ الإعدادات: ' + error.message);
        }
    }

    // وظيفة حفظ أساسية محسنة
    static saveSettingsBasic() {
        try {
            console.log('💾 بدء الحفظ الأساسي المحسن...');

            // قراءة القيم
            const labName = document.getElementById('settingsLabName')?.value || 'معمل الأسنان';
            const labSubtitle = document.getElementById('settingsLabSubtitle')?.value || '';

            console.log('📝 البيانات:', { labName, labSubtitle });

            // تحميل الإعدادات الحالية أولاً
            let currentSettings = {};
            try {
                const saved = localStorage.getItem('labSettings');
                if (saved) {
                    currentSettings = JSON.parse(saved);
                }
            } catch (e) {
                console.warn('تعذر تحميل الإعدادات الحالية');
            }

            // دمج الإعدادات الجديدة مع الحالية
            const newSettings = {
                ...currentSettings,
                labName: labName.trim(),
                labSubtitle: labSubtitle.trim(),
                lastUpdated: new Date().toISOString()
            };

            console.log('💾 حفظ الإعدادات:', newSettings);

            // حفظ في localStorage
            localStorage.setItem('labSettings', JSON.stringify(newSettings));

            // تحديث العرض فوراً
            this.applySettingsToInterface(newSettings);

            console.log('✅ تم الحفظ والتطبيق');
            alert('تم حفظ الإعدادات بنجاح');

            // إغلاق النافذة
            const panel = document.querySelector('.quick-settings-panel');
            if (panel) panel.remove();
            this.isOpen = false;

        } catch (e) {
            console.error('❌ خطأ في الحفظ:', e);
            alert('خطأ في الحفظ: ' + e.message);
        }
    }

    // تطبيق الإعدادات على الواجهة
    static applySettingsToInterface(settings) {
        try {
            console.log('🎯 تطبيق الإعدادات على الواجهة:', settings);

            // تحديث اسم المعمل
            if (settings.labName) {
                const nameElements = document.querySelectorAll('#labName, .lab-name');
                nameElements.forEach(el => {
                    if (el) {
                        el.textContent = settings.labName;
                        console.log('✅ تم تحديث اسم المعمل');
                    }
                });
            }

            // تحديث العنوان الفرعي
            if (settings.labSubtitle) {
                const subtitleElements = document.querySelectorAll('#labSubtitle, .lab-subtitle');
                subtitleElements.forEach(el => {
                    if (el) {
                        el.textContent = settings.labSubtitle;
                        console.log('✅ تم تحديث العنوان الفرعي');
                    }
                });
            }

            // تحديث شاشة تسجيل الدخول
            const systemTitle = document.getElementById('loginSystemTitle');
            if (systemTitle && settings.labName) {
                systemTitle.textContent = `نظام إدارة ${settings.labName}`;
                console.log('✅ تم تحديث شاشة تسجيل الدخول');
            }

        } catch (error) {
            console.error('❌ خطأ في تطبيق الإعدادات:', error);
        }
    }

    // فرض تحديث الواجهة الرئيسية فوراً
    static forceUpdateMainInterface(settings) {
        try {
            console.log('🎯 فرض تحديث الواجهة الرئيسية فوراً:', settings);

            // تحديث اسم المعمل في الشريط العلوي
            if (settings.labName) {
                const labNameElements = document.querySelectorAll('#labName, .lab-name');
                labNameElements.forEach(element => {
                    if (element) {
                        element.textContent = settings.labName;
                        console.log('✅ تم تحديث اسم المعمل في الشريط العلوي فوراً');
                    }
                });
            }

            // تحديث العنوان الفرعي في الشريط العلوي
            if (settings.labSubtitle) {
                const labSubtitleElements = document.querySelectorAll('#labSubtitle, .lab-subtitle');
                labSubtitleElements.forEach(element => {
                    if (element) {
                        element.textContent = settings.labSubtitle;
                        console.log('✅ تم تحديث العنوان الفرعي في الشريط العلوي فوراً');
                    }
                });
            }

            // تحديث الشعار في الشريط العلوي
            if (settings.labLogo) {
                const labLogoElements = document.querySelectorAll('#labLogo, .lab-logo');
                labLogoElements.forEach(element => {
                    if (element) {
                        element.innerHTML = `<img src="${settings.labLogo}" alt="شعار المعمل" style="width: 100%; height: 100%; object-fit: cover; border-radius: inherit;">`;
                        console.log('✅ تم تحديث الشعار في الشريط العلوي فوراً');
                    }
                });
            }

            console.log('🎯 تم فرض تحديث الواجهة الرئيسية بنجاح');

        } catch (error) {
            console.error('❌ خطأ في فرض تحديث الواجهة الرئيسية:', error);
        }
    }

    // إرسال حدث تحديث بيانات المعمل
    static dispatchLabInfoUpdateEvent(settings) {
        const event = new CustomEvent('labInfoUpdated', {
            detail: {
                labName: settings.labName,
                labSubtitle: settings.labSubtitle,
                labLogo: settings.labLogo,
                labPhone: settings.labPhone,
                labEmail: settings.labEmail,
                labAddress: settings.labAddress,
                settings: settings
            }
        });
        document.dispatchEvent(event);
        console.log('تم إرسال حدث تحديث بيانات المعمل');
    }

    // تطبيق الإعدادات
    static applySettings(settings) {
        // تطبيق حجم الخط
        if (settings.fontSize) {
            this.applyFontSize(settings.fontSize);
        }

        // تحديث معلومات المعمل في جميع الواجهات
        this.updateAllLabInfo(settings);

        // تحديث شاشة تسجيل الدخول
        this.updateLoginScreen(settings);

        // تحديث معلومات المعمل عبر ThemeManager
        if (typeof ThemeManager !== 'undefined') {
            ThemeManager.updateLabInfo();
        }

        // تطبيق إعدادات الرسوم المتحركة
        if (settings.animations === false) {
            document.body.classList.add('no-animations');
        } else {
            document.body.classList.remove('no-animations');
        }
    }

    // تحديث معلومات المعمل في جميع الواجهات
    static updateAllLabInfo(settings) {
        console.log('بدء تحديث معلومات المعمل:', settings);

        // تحديث اسم المعمل في الشريط العلوي
        const labNameElements = document.querySelectorAll('#labName, .lab-name');
        console.log('عناصر اسم المعمل الموجودة:', labNameElements.length);
        labNameElements.forEach((element, index) => {
            if (element && settings.labName) {
                console.log(`تحديث عنصر اسم المعمل ${index + 1}: ${settings.labName}`);
                element.textContent = settings.labName;
            }
        });

        // تحديث العنوان الفرعي في الشريط العلوي
        const labSubtitleElements = document.querySelectorAll('#labSubtitle, .lab-subtitle');
        console.log('عناصر العنوان الفرعي الموجودة:', labSubtitleElements.length);
        labSubtitleElements.forEach((element, index) => {
            if (element && settings.labSubtitle) {
                console.log(`تحديث عنصر العنوان الفرعي ${index + 1}: ${settings.labSubtitle}`);
                element.textContent = settings.labSubtitle;
            }
        });

        // ملاحظة: تم إزالة تحديث اسم المعمل من الشريط الجانبي
        // لأن الشريط الجانبي الآن يعرض "القائمة الرئيسية" فقط

        // تحديث الشعار في جميع الأماكن
        if (settings.labLogo) {
            console.log('تحديث الشعار');
            this.updateMainLogo(settings.labLogo);
        }

        // تحديث معلومات الاتصال في أي مكان تظهر فيه
        this.updateContactInfo(settings);

        console.log('انتهى تحديث معلومات المعمل في جميع الواجهات');
    }

    // تحديث معلومات الاتصال
    static updateContactInfo(settings) {
        // تحديث رقم الهاتف إذا كان معروضاً
        const phoneElements = document.querySelectorAll('.lab-phone, #labPhoneDisplay');
        phoneElements.forEach(element => {
            if (element && settings.labPhone) {
                element.textContent = settings.labPhone;
            }
        });

        // تحديث البريد الإلكتروني إذا كان معروضاً
        const emailElements = document.querySelectorAll('.lab-email, #labEmailDisplay');
        emailElements.forEach(element => {
            if (element && settings.labEmail) {
                element.textContent = settings.labEmail;
            }
        });

        // تحديث العنوان إذا كان معروضاً
        const addressElements = document.querySelectorAll('.lab-address, #labAddressDisplay');
        addressElements.forEach(element => {
            if (element && settings.labAddress) {
                element.textContent = settings.labAddress;
            }
        });
    }

    // تحديث شاشة تسجيل الدخول
    static updateLoginScreen(settings) {
        try {
            console.log('تحديث شاشة تسجيل الدخول بالإعدادات:', settings);

            // حفظ الإعدادات في localStorage لتحديث شاشة تسجيل الدخول
            localStorage.setItem('labSettings', JSON.stringify(settings));

            // التحقق من وجود شاشة تسجيل الدخول
            const loginScreen = document.getElementById('loginScreen');
            if (!loginScreen || loginScreen.style.display === 'none') {
                console.log('شاشة تسجيل الدخول غير مرئية، تم حفظ الإعدادات فقط');
                return;
            }

            // تحديث عنوان النظام في شاشة تسجيل الدخول
            const systemTitle = document.getElementById('loginSystemTitle');
            if (systemTitle && settings.labName) {
                systemTitle.textContent = `نظام إدارة ${settings.labName}`;
                console.log('تم تحديث عنوان النظام في شاشة تسجيل الدخول');
            }

            // تحديث الشعار في شاشة تسجيل الدخول
            const loginLogo = document.getElementById('loginLogo');
            if (loginLogo) {
                if (settings.labLogo) {
                    loginLogo.innerHTML = `<img src="${settings.labLogo}" alt="شعار المعمل" style="width: 100%; height: 100%; object-fit: cover; border-radius: inherit;">`;
                    console.log('تم تحديث الشعار في شاشة تسجيل الدخول');
                } else {
                    loginLogo.innerHTML = '<i class="fas fa-tooth"></i>';
                    console.log('تم إعادة تعيين الشعار الافتراضي في شاشة تسجيل الدخول');
                }
            }

            // إرسال حدث تحديث لشاشة تسجيل الدخول
            const event = new CustomEvent('loginScreenUpdate', {
                detail: { settings }
            });
            document.dispatchEvent(event);

        } catch (error) {
            console.error('خطأ في تحديث شاشة تسجيل الدخول:', error);
        }
    }

    // فرض تحديث شاشة تسجيل الدخول
    static forceUpdateLoginScreen(settings) {
        try {
            console.log('🔄 فرض تحديث شاشة تسجيل الدخول...');

            // التأكد من حفظ الإعدادات في localStorage
            localStorage.setItem('labSettings', JSON.stringify(settings));
            console.log('💾 تم حفظ الإعدادات في localStorage:', settings);

            // إرسال حدث مخصص لتحديث شاشة تسجيل الدخول
            const updateEvent = new CustomEvent('forceLoginUpdate', {
                detail: { settings },
                bubbles: true
            });
            document.dispatchEvent(updateEvent);

            // محاولة تحديث مباشر إذا كانت الوظيفة متاحة
            if (typeof window.updateLoginScreen === 'function') {
                window.updateLoginScreen();
                console.log('✅ تم استدعاء updateLoginScreen مباشرة');
            }

            // إرسال حدث storage مصطنع لتحفيز التحديث
            const storageEvent = new StorageEvent('storage', {
                key: 'labSettings',
                newValue: JSON.stringify(settings),
                oldValue: null,
                storageArea: localStorage
            });
            window.dispatchEvent(storageEvent);

            console.log('🎉 تم فرض تحديث شاشة تسجيل الدخول');

        } catch (error) {
            console.error('❌ خطأ في فرض تحديث شاشة تسجيل الدخول:', error);
        }
    }

    // إعادة تعيين الإعدادات
    static resetSettings() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
            const defaultSettings = {
                labName: 'معمل الأسنان المتخصص',
                labSubtitle: 'نظام الإدارة المتطور',
                currency: 'جنيه',
                notifications: true,
                sounds: false,
                autoBackup: false,
                backupInterval: 7,
                language: 'ar',
                dateFormat: 'DD/MM/YYYY',
                showHijri: true,
                fontSize: 16,
                animations: true
            };

            // حفظ الإعدادات الافتراضية
            if (typeof Database !== 'undefined') {
                Database.saveSettings(defaultSettings);
            }

            // تحديث العرض
            this.updateSettingsValues();
            this.applySettings(defaultSettings);

            if (typeof Utils !== 'undefined') {
                Utils.showNotification('تم إعادة تعيين الإعدادات', 'info');
            }
        }
    }

    // فتح الإعدادات الكاملة
    static openFullSettings() {
        this.closeQuickSettings();
        if (typeof Navigation !== 'undefined') {
            Navigation.navigateTo('settings');
        }
    }

    // تعديل موضع النافذة للتأكد من عدم خروجها من الشاشة
    static adjustPanelPosition(panel) {
        if (!panel) return;

        const rect = panel.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const settingsBtn = document.getElementById('quickSettingsBtn');

        if (!settingsBtn) return;

        const btnRect = settingsBtn.getBoundingClientRect();
        const rightOffset = this.panelRightOffset; // نفس الإزاحة المستخدمة في الموضع الأولي

        // التحقق من الخروج من الجانب الأيمن
        if (rect.right > viewportWidth - 20) {
            // تقليل الإزاحة تدريجياً حتى تدخل النافذة في الشاشة
            const maxOffset = viewportWidth - rect.width - 20;
            const adjustedOffset = Math.max(0, maxOffset - (window.innerWidth - btnRect.right));
            panel.style.right = adjustedOffset + 'px';
            panel.style.left = 'auto';
        }

        // التحقق من الخروج من الجانب الأيسر
        if (rect.left < 20) {
            panel.style.left = '20px';
            panel.style.right = 'auto';
        }

        // التحقق من الخروج من الأسفل
        if (rect.bottom > viewportHeight - 20) {
            // عرض النافذة أعلى الزر بدلاً من أسفله
            panel.style.top = (btnRect.top - panel.offsetHeight - 10) + 'px';
        }

        // التحقق من الخروج من الأعلى
        if (rect.top < 20) {
            panel.style.top = '20px';
        }

        // للشاشات الصغيرة جداً، استخدم موضع ملء الشاشة
        if (viewportWidth <= 480) {
            panel.style.top = '80px';
            panel.style.left = '10px';
            panel.style.right = '10px';
            panel.style.width = 'auto';
            panel.style.transform = 'translateY(0) scale(1)';
        }
    }

    // تخصيص مقدار الإزاحة نحو اليمين
    static setPanelRightOffset(offset) {
        this.panelRightOffset = Math.max(0, Math.min(200, offset)); // بين 0 و 200 بكسل

        // إعادة تحديد موضع النافذة إذا كانت مفتوحة
        if (this.isOpen) {
            const panel = document.querySelector('.quick-settings-panel');
            if (panel) {
                this.positionPanelRelativeToButton(panel);
                this.adjustPanelPosition(panel);
            }
        }
    }

    // الحصول على مقدار الإزاحة الحالي
    static getPanelRightOffset() {
        return this.panelRightOffset;
    }

    // تنظيف الموارد
    static cleanup() {
        this.closeQuickSettings();
        this.isInitialized = false;
    }
}

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    QuickSettings.init();
});

// تنظيف عند إغلاق الصفحة
window.addEventListener('beforeunload', () => {
    QuickSettings.cleanup();
});
