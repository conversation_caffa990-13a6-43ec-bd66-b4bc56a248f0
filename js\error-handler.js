// معالج الأخطاء الشامل
console.log('🔄 بدء تحميل ملف error-handler.js...');

class ErrorHandler {
    static errors = [];
    static maxErrors = 100;

    // تسجيل خطأ
    static logError(error, context = '') {
        const errorInfo = {
            timestamp: new Date().toISOString(),
            message: error.message || error,
            stack: error.stack || '',
            context: context,
            url: window.location.href,
            userAgent: navigator.userAgent
        };

        this.errors.push(errorInfo);
        
        // الاحتفاظ بآخر 100 خطأ فقط
        if (this.errors.length > this.maxErrors) {
            this.errors = this.errors.slice(-this.maxErrors);
        }

        // طباعة الخطأ في وحدة التحكم (تجنب الأخطاء المتكررة)
        if (error && error.message !== 'Script error.' && !this.isRepeatedError(error)) {
            console.error(`❌ خطأ في ${context}:`, error);
        }
        
        // حفظ الأخطاء في localStorage
        try {
            localStorage.setItem('errorLog', JSON.stringify(this.errors));
        } catch (e) {
            console.warn('لا يمكن حفظ سجل الأخطاء في localStorage');
        }
    }

    // التحقق من الأخطاء المتكررة
    static isRepeatedError(error) {
        const errorKey = `${error.message}_${error.stack}`;
        const now = Date.now();

        // إذا كان نفس الخطأ حدث في آخر 5 ثوانٍ، اعتبره متكرر
        if (this.lastErrors && this.lastErrors[errorKey] && (now - this.lastErrors[errorKey]) < 5000) {
            return true;
        }

        // تسجيل وقت الخطأ
        if (!this.lastErrors) this.lastErrors = {};
        this.lastErrors[errorKey] = now;

        // تنظيف الأخطاء القديمة
        Object.keys(this.lastErrors).forEach(key => {
            if (now - this.lastErrors[key] > 60000) { // أكثر من دقيقة
                delete this.lastErrors[key];
            }
        });

        return false;
    }

    // معالج الأخطاء العام
    static setupGlobalErrorHandlers() {
        // معالج أخطاء JavaScript
        window.addEventListener('error', (event) => {
            // تجنب الأخطاء العامة والمتكررة
            if (event.error && event.error.message !== 'Script error.' && event.filename) {
                this.logError(event.error || event.message, 'JavaScript Error');
            }
        });

        // معالج أخطاء الوعود
        window.addEventListener('unhandledrejection', (event) => {
            this.logError(event.reason, 'Unhandled Promise Rejection');
            // منع عرض الخطأ في وحدة التحكم
            event.preventDefault();
        });

        // معالج أخطاء الموارد
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.logError(`Failed to load resource: ${event.target.src || event.target.href}`, 'Resource Error');
            }
        }, true);
    }

    // تحميل سجل الأخطاء المحفوظ
    static loadErrorLog() {
        try {
            const savedErrors = localStorage.getItem('errorLog');
            if (savedErrors) {
                this.errors = JSON.parse(savedErrors);
                console.log(`📋 تم تحميل ${this.errors.length} خطأ من السجل المحفوظ`);
            }
        } catch (e) {
            console.warn('لا يمكن تحميل سجل الأخطاء المحفوظ');
            this.errors = [];
        }
    }

    // عرض تقرير الأخطاء
    static showErrorReport() {
        if (this.errors.length === 0) {
            Utils.showNotification('لا توجد أخطاء مسجلة', 'info');
            return;
        }

        const recentErrors = this.errors.slice(-10); // آخر 10 أخطاء
        
        const errorList = recentErrors.map(error => `
            <div class="error-item">
                <div class="error-time">${Utils.formatDate(error.timestamp, 'DD/MM/YYYY HH:mm:ss')}</div>
                <div class="error-context">${error.context}</div>
                <div class="error-message">${error.message}</div>
            </div>
        `).join('');

        Utils.createModal({
            title: 'تقرير الأخطاء',
            content: `
                <div class="error-report">
                    <div class="error-summary">
                        <p>إجمالي الأخطاء: <strong>${this.errors.length}</strong></p>
                        <p>آخر 10 أخطاء:</p>
                    </div>
                    <div class="error-list">
                        ${errorList}
                    </div>
                    <div class="modal-actions">
                        <button class="btn btn-primary" onclick="ErrorHandler.exportErrorLog()">
                            <i class="fas fa-download"></i>
                            تصدير سجل الأخطاء
                        </button>
                        <button class="btn btn-warning" onclick="ErrorHandler.clearErrorLog()">
                            <i class="fas fa-trash"></i>
                            مسح السجل
                        </button>
                        <button class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            `,
            size: 'large'
        });
    }

    // تصدير سجل الأخطاء
    static exportErrorLog() {
        try {
            const logData = {
                exportDate: new Date().toISOString(),
                totalErrors: this.errors.length,
                errors: this.errors
            };

            const jsonData = JSON.stringify(logData, null, 2);
            const blob = new Blob([jsonData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `error-log-${Utils.formatDate(new Date(), 'YYYY-MM-DD-HH-mm-ss')}.json`;
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            URL.revokeObjectURL(url);
            
            Utils.showNotification('تم تصدير سجل الأخطاء بنجاح', 'success');
            
        } catch (error) {
            console.error('خطأ في تصدير سجل الأخطاء:', error);
            Utils.showNotification('حدث خطأ في تصدير سجل الأخطاء', 'error');
        }
    }

    // مسح سجل الأخطاء
    static clearErrorLog() {
        if (confirm('هل أنت متأكد من مسح سجل الأخطاء؟')) {
            this.errors = [];
            localStorage.removeItem('errorLog');
            Utils.showNotification('تم مسح سجل الأخطاء', 'success');
            Utils.closeModal();
        }
    }

    // فحص صحة النظام
    static performSystemCheck() {
        const issues = [];
        
        // فحص تحميل الكلاسات المطلوبة
        const requiredClasses = [
            'Utils', 'Database', 'Auth', 'Navigation', 'Dashboard',
            'Prosthetics', 'Doctors', 'Employees', 'ExternalLabs',
            'Financial', 'Reports', 'Settings', 'Inventory', 'Backup'
        ];

        requiredClasses.forEach(className => {
            if (typeof window[className] === 'undefined') {
                issues.push(`الكلاس ${className} غير محمل`);
            }
        });

        // فحص العناصر المطلوبة في DOM
        const requiredElements = [
            'pageContent', 'sidebar', 'topBar', 'bottomBar'
        ];

        requiredElements.forEach(elementId => {
            if (!document.getElementById(elementId)) {
                issues.push(`العنصر ${elementId} غير موجود في DOM`);
            }
        });

        // فحص localStorage
        try {
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
        } catch (e) {
            issues.push('localStorage غير متاح');
        }

        return {
            isHealthy: issues.length === 0,
            issues: issues,
            timestamp: new Date().toISOString()
        };
    }

    // عرض تقرير صحة النظام
    static showSystemHealth() {
        const healthCheck = this.performSystemCheck();
        
        const statusIcon = healthCheck.isHealthy ? 
            '<i class="fas fa-check-circle" style="color: green;"></i>' :
            '<i class="fas fa-exclamation-triangle" style="color: red;"></i>';

        const issuesList = healthCheck.issues.length > 0 ? 
            healthCheck.issues.map(issue => `<li>${issue}</li>`).join('') :
            '<li style="color: green;">لا توجد مشاكل</li>';

        Utils.createModal({
            title: 'تقرير صحة النظام',
            content: `
                <div class="system-health">
                    <div class="health-status">
                        ${statusIcon}
                        <h3>${healthCheck.isHealthy ? 'النظام يعمل بشكل طبيعي' : 'توجد مشاكل في النظام'}</h3>
                    </div>
                    <div class="health-details">
                        <h4>التفاصيل:</h4>
                        <ul>${issuesList}</ul>
                    </div>
                    <div class="modal-actions">
                        <button class="btn btn-primary" onclick="location.reload()">
                            <i class="fas fa-refresh"></i>
                            إعادة تحميل الصفحة
                        </button>
                        <button class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            `,
            size: 'medium'
        });
    }

    // تهيئة معالج الأخطاء
    static init() {
        this.loadErrorLog();
        this.setupGlobalErrorHandlers();
        
        console.log('✅ تم تهيئة معالج الأخطاء بنجاح');
        
        // إضافة اختصار لوحة المفاتيح لعرض تقرير الأخطاء (Ctrl+Shift+E)
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'E') {
                e.preventDefault();
                this.showErrorReport();
            }
        });

        // إضافة اختصار لوحة المفاتيح لفحص صحة النظام (Ctrl+Shift+H)
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'H') {
                e.preventDefault();
                this.showSystemHealth();
            }
        });
    }
}

// تهيئة معالج الأخطاء
ErrorHandler.init();

// تسجيل اكتمال تحميل الملف
console.log('✅ تم تحميل ملف error-handler.js بالكامل');

// التأكد من أن الكلاس متاح عالمياً
window.ErrorHandler = ErrorHandler;
