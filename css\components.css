/* مكونات واجهة المستخدم المتقدمة */

/* النوافذ المنبثقة الحديثة */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: all var(--transition-normal);
    border: 1px solid var(--border-color);
}

.modal-overlay.show .modal {
    transform: scale(1) translateY(0);
}

.modal.small { width: 400px; }
.modal.medium { width: 600px; }
.modal.large { width: 800px; }
.modal.extra-large { width: 1000px; }

.modal-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-primary));
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: 50%;
    transition: all var(--transition-fast);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: var(--error-color);
    color: var(--text-white);
    transform: rotate(90deg);
}

.modal-content {
    padding: var(--spacing-xl);
    max-height: 70vh;
    overflow-y: auto;
}

.modal-actions {
    padding: var(--spacing-xl);
    border-top: 1px solid var(--border-color);
    background: var(--bg-primary);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

/* النماذج الحديثة */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.form-control, input, textarea, select {
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    transition: all var(--transition-fast);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-family: inherit;
}

.form-control:focus, input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.form-control:hover, input:hover, textarea:hover, select:hover {
    border-color: var(--primary-light);
}

/* مربعات الاختيار المخصصة */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    cursor: pointer;
    user-select: none;
    position: relative;
}

.checkbox-label input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    position: relative;
    transition: all var(--transition-fast);
    background: var(--bg-secondary);
}

.checkbox-label:hover .checkmark {
    border-color: var(--primary-color);
    transform: scale(1.1);
}

.checkbox-label input:checked ~ .checkmark {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-color: var(--primary-color);
}

.checkbox-label input:checked ~ .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--text-white);
    font-weight: bold;
    font-size: var(--font-size-sm);
}

/* علامات التبويب */
.tab-buttons {
    display: flex;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow-x: auto;
}

.tab-btn {
    flex: 1;
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    font-weight: 500;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
    position: relative;
}

.tab-btn:hover {
    color: var(--primary-color);
    background: var(--bg-primary);
}

.tab-btn.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-white);
    box-shadow: var(--shadow-md);
}

.tab-content {
    display: none;
    animation: fadeIn var(--transition-normal) ease-out;
}

.tab-content.active {
    display: block;
}

/* الفلاتر والبحث */
.filters-container {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-xl);
    border: 1px solid var(--border-color);
}

.search-box {
    position: relative;
    max-width: 400px;
    margin-bottom: var(--spacing-lg);
}

.search-box input {
    width: 100%;
    padding-left: 50px;
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
}

.search-box i {
    position: absolute;
    left: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
}

.filters-row {
    display: flex;
    gap: var(--spacing-lg);
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    min-width: 150px;
}

.filter-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

/* التصفح */
.table-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
}

.pagination-info {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.pagination-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.pagination-controls .btn {
    min-width: 40px;
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* الإشعارات */
.notification {
    position: fixed;
    top: 20px;
    left: 20px;
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    border-left: 4px solid var(--primary-color);
    z-index: 10001;
    min-width: 300px;
    transform: translateX(-100%);
    transition: all var(--transition-normal);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left-color: var(--success-color);
}

.notification.warning {
    border-left-color: var(--warning-color);
}

.notification.error {
    border-left-color: var(--error-color);
}

.notification.info {
    border-left-color: var(--info-color);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-size: var(--font-size-lg);
}

.notification.success .notification-icon {
    background: var(--success-color);
}

.notification.warning .notification-icon {
    background: var(--warning-color);
    color: var(--text-primary);
}

.notification.error .notification-icon {
    background: var(--error-color);
}

.notification.info .notification-icon {
    background: var(--info-color);
}

.notification-text {
    flex: 1;
    color: var(--text-primary);
    font-weight: 500;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.notification-close:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

/* مخطط الأسنان */
.dental-chart-container {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.dental-chart-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.dental-chart-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.dental-chart-info {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.dental-chart {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.jaw-section {
    text-align: center;
}

.jaw-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.teeth-row {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.tooth {
    width: 50px;
    height: 60px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.tooth:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.tooth.selected {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-white);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-lg);
}

.tooth.selecting {
    animation: pulse 0.3s ease-in-out;
}

.tooth-number {
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.tooth-type {
    font-size: var(--font-size-xs);
    opacity: 0.8;
}

.prosthetic-indicator {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    font-size: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-weight: bold;
}

.prosthetic-indicator.porcelain {
    background: #e3f2fd;
    color: #1976d2;
}

.prosthetic-indicator.zircon {
    background: #f3e5f5;
    color: #7b1fa2;
}

.prosthetic-indicator.metal {
    background: #fafafa;
    color: #424242;
}

.prosthetic-indicator.removable {
    background: #e8f5e8;
    color: #2e7d32;
}

.prosthetic-indicator.orthodontic {
    background: #fff3e0;
    color: #f57c00;
}

/* أدوات التحكم في مخطط الأسنان */
.dental-chart-controls {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
}

.chart-control-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.chart-control-btn:hover {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

.chart-control-btn.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-white);
    border-color: var(--primary-color);
}

/* معلومات الأسنان المحددة */
.selected-teeth-info {
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    margin-top: var(--spacing-lg);
}

.selected-teeth-header {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.selected-teeth-list {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
    margin-bottom: var(--spacing-md);
}

.selected-tooth-tag {
    background: var(--primary-color);
    color: var(--text-white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.remove-tooth {
    cursor: pointer;
    opacity: 0.7;
    transition: opacity var(--transition-fast);
}

.remove-tooth:hover {
    opacity: 1;
}

.teeth-count {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

/* أسطورة مخطط الأسنان */
.dental-chart-legend {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-color);
}

.legend-color.selected {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.legend-color.porcelain {
    background: #e3f2fd;
}

.legend-color.zircon {
    background: #f3e5f5;
}

.legend-color.metal {
    background: #fafafa;
}

.legend-color.removable {
    background: #e8f5e8;
}

.legend-color.orthodontic {
    background: #fff3e0;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .modal {
        width: 95vw;
        margin: var(--spacing-md);
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .filters-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .tab-buttons {
        overflow-x: auto;
    }
    
    .teeth-row {
        gap: var(--spacing-xs);
    }
    
    .tooth {
        width: 40px;
        height: 50px;
    }
    
    .dental-chart-controls {
        flex-direction: column;
    }
    
    .dental-chart-legend {
        flex-direction: column;
        align-items: center;
    }
}

/* تأثيرات خاصة */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* أزرار عصرية */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5em;
    padding: 0.75em 2em;
    font-size: 1rem;
    font-weight: 600;
    border: none;
    border-radius: 2em;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(.4,0,.2,1);
    box-shadow: 0 2px 8px rgba(80, 80, 120, 0.08);
    outline: none;
    position: relative;
    overflow: hidden;
    user-select: none;
}

.btn-primary {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
    color: var(--text-white);
    box-shadow: 0 4px 16px rgba(80, 120, 220, 0.10);
}

.btn-primary:hover, .btn-primary:focus {
    background: linear-gradient(90deg, var(--primary-dark), var(--primary-color));
    box-shadow: 0 6px 24px rgba(80, 120, 220, 0.18);
    transform: translateY(-2px) scale(1.03);
}

.btn-secondary {
    background: linear-gradient(90deg, var(--bg-secondary), var(--primary-light));
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover, .btn-secondary:focus {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-dark);
    transform: translateY(-2px) scale(1.03);
}

.btn:active {
    filter: brightness(0.95);
    transform: scale(0.98);
}

.btn i {
    font-size: 1.1em;
    margin-left: 0.5em;
}

/* زر صغير */
.btn-small {
    padding: 0.4em 1.2em;
    font-size: 0.95em;
    border-radius: 1.5em;
}

.gradient-border {
    position: relative;
    background: var(--bg-secondary);
}

.gradient-border::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
}

.shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}
