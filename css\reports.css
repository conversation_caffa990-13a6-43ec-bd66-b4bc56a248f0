/* تنسيقات إدارة التقارير */

/* ===================================
   تنسيقات عامة للتقارير
=================================== */

.reports-container {
    padding: var(--spacing-lg);
    max-width: 1400px;
    margin: 0 auto;
}

/* فئات التقارير */
.reports-categories {
    margin: var(--spacing-xl) 0;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.report-category {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    position: relative;
    overflow: hidden;
}

.report-category::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
    transform: scaleY(0);
    transition: transform var(--transition-normal);
}

.report-category:hover {
    background: var(--bg-hover);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.report-category:hover::before {
    transform: scaleY(1);
}

.category-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-on-primary);
    font-size: var(--font-size-2xl);
    flex-shrink: 0;
    box-shadow: var(--shadow-sm);
}

.category-info {
    flex: 1;
}

.category-info h3 {
    font-family: var(--font-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.category-info p {
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-sm) 0;
    line-height: var(--line-height-relaxed);
}

.category-stats {
    font-family: var(--font-primary);
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    font-weight: var(--font-weight-medium);
}

.category-arrow {
    color: var(--text-muted);
    font-size: var(--font-size-lg);
    transition: all var(--transition-fast);
}

.report-category:hover .category-arrow {
    color: var(--primary-color);
    transform: translateX(-4px);
}

/* منطقة عرض التقارير */
.reports-display-area {
    margin-top: var(--spacing-xl);
    min-height: 400px;
}

.welcome-message {
    text-align: center;
    padding: var(--spacing-4xl) var(--spacing-xl);
    background: var(--bg-secondary);
    border-radius: var(--border-radius-xl);
    border: 2px dashed var(--border-color);
}

.welcome-icon {
    font-size: var(--font-size-6xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.welcome-message h3 {
    font-family: var(--font-primary);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-md) 0;
}

.welcome-message p {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-xl) 0;
}

.quick-actions {
    margin-top: var(--spacing-lg);
}

/* ===================================
   تنسيقات عرض التقارير
=================================== */

.report-display {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.report-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--text-on-primary);
    padding: var(--spacing-xl);
    text-align: center;
}

.report-header h2 {
    font-family: var(--font-primary);
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    margin: 0 0 var(--spacing-md) 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.report-meta {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

.report-meta span {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* ملخص التقرير */
.report-summary {
    padding: var(--spacing-xl);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.report-summary h3 {
    font-family: var(--font-primary);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-lg) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.summary-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--primary-color);
}

.summary-card.income::before {
    background: var(--success-color);
}

.summary-card.expense::before {
    background: var(--warning-color);
}

.summary-card.profit::before {
    background: var(--success-color);
}

.summary-card.loss::before {
    background: var(--danger-color);
}

.summary-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-on-primary);
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.summary-card.income .card-icon {
    background: linear-gradient(135deg, var(--success-color), #27ae60);
}

.summary-card.expense .card-icon {
    background: linear-gradient(135deg, var(--warning-color), #f39c12);
}

.summary-card.profit .card-icon {
    background: linear-gradient(135deg, var(--success-color), #27ae60);
}

.summary-card.loss .card-icon {
    background: linear-gradient(135deg, var(--danger-color), #e74c3c);
}

.card-content h4 {
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-xs) 0;
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-wide);
}

.card-value {
    font-family: var(--font-modern);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    line-height: 1;
}

/* أقسام التقرير */
.report-section {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
}

.report-section:last-of-type {
    border-bottom: none;
}

.report-section h3 {
    font-family: var(--font-primary);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-lg) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* إحصائيات التقرير */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
}

.stat-label {
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

.stat-value {
    font-family: var(--font-modern);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
}

.stat-percentage {
    font-family: var(--font-primary);
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-right: var(--spacing-xs);
}

/* جداول التقارير */
.report-table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--font-primary);
    background: var(--bg-primary);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.report-table th {
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: var(--font-weight-semibold);
    padding: var(--spacing-md);
    text-align: right;
    border-bottom: 2px solid var(--border-color);
    font-size: var(--font-size-sm);
}

.report-table td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.report-table tr:hover {
    background: var(--bg-hover);
}

/* أزرار التقرير */
.report-actions {
    padding: var(--spacing-xl);
    background: var(--bg-secondary);
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

/* تنسيقات متجاوبة */
@media (max-width: 768px) {
    .categories-grid {
        grid-template-columns: 1fr;
    }
    
    .summary-cards {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .report-meta {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .report-actions {
        flex-direction: column;
    }
}

/* ===================================
   تنسيقات سجل التقارير
=================================== */

.report-history {
    max-height: 600px;
    overflow-y: auto;
}

.history-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.history-header h3 {
    font-family: var(--font-primary);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.history-header p {
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0;
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.history-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
}

.history-item:hover {
    background: var(--bg-hover);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.item-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-on-primary);
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.item-content {
    flex: 1;
}

.item-content h4 {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.item-content p {
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-xs) 0;
}

.item-content small {
    font-family: var(--font-primary);
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.item-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.btn-small {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius-sm);
    border: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
}

.btn-small.btn-info {
    background: var(--info-color);
    color: var(--text-on-primary);
}

.btn-small.btn-success {
    background: var(--success-color);
    color: var(--text-on-primary);
}

.btn-small.btn-danger {
    background: var(--danger-color);
    color: var(--text-on-primary);
}

.btn-small:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-sm);
}

.no-reports {
    text-align: center;
    padding: var(--spacing-4xl);
    color: var(--text-muted);
}

.no-reports i {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.no-reports p {
    font-family: var(--font-primary);
    font-size: var(--font-size-lg);
    margin: 0 0 var(--spacing-sm) 0;
}

.no-reports small {
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    opacity: 0.7;
}

/* ===================================
   تنسيقات التقرير المخصص
=================================== */

.custom-report-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-section {
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h4 {
    font-family: var(--font-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--spacing-lg) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.filter-group label {
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.filter-group input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

/* تنسيقات متجاوبة للتقارير */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .filters-grid {
        grid-template-columns: 1fr;
    }

    .history-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .item-actions {
        align-self: stretch;
        justify-content: center;
    }
}
