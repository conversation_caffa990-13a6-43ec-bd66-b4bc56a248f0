/* تحسينات مباشرة للشريط الجانبي والتخطيط */

/* ===================================
   الشريط العلوي
=================================== */
.top-bar {
    height: 70px !important;
    position: fixed !important;
    top: 0 !important;
    z-index: 200 !important;
}

/* ===================================
   الشريط الجانبي
=================================== */
.sidebar {
    top: 70px !important;
    height: calc(100vh - 70px) !important;
    margin-top: 0 !important;
    padding-top: 0 !important;
    border-top: none !important;
}

.sidebar-header {
    padding: 10px 15px !important;
    min-height: 50px !important;
    background: #6c5ce7 !important;
    border-bottom: none !important;
    margin-top: 0 !important;
    border-top: none !important;
    display: flex !important;
    align-items: center !important;
}

/* تنسيق المسافة بين العنوان والزر */
.sidebar-title {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    gap: 6px !important;
}

/* إزالة أي مسافات إضافية */
.sidebar-nav {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

.sidebar-title h2 {
    font-size: 1.1rem !important;
    color: white !important;
    font-weight: 600 !important;
    margin: 0 !important;
    line-height: 1.4 !important;
    letter-spacing: 0.2px !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
}

.sidebar-toggle-btn {
    width: 26px !important;
    height: 26px !important;
    background: rgba(255,255,255,0.2) !important;
    border: 1px solid rgba(255,255,255,0.3) !important;
    color: white !important;
    border-radius: 4px !important;
}

.sidebar-toggle-btn i {
    font-size: 0.85rem !important;
    color: white !important;
    transform: none !important;
    transition: all 0.2s ease !important;
}

.sidebar-toggle-btn:hover {
    background: rgba(255,255,255,0.3) !important;
    transform: scale(1.05) !important;
}

.sidebar.collapsed .sidebar-toggle-btn:hover {
    background: #5a4fcf !important;
    transform: scale(1.1) !important;
    animation: none !important;
}

.sidebar-nav ul {
    padding: 0 !important;
    margin: 0 !important;
    margin-top: 2px !important;
}

.sidebar-nav li {
    margin-bottom: 0 !important;
}

.sidebar-nav {
    margin-top: 0 !important;
    padding-top: 2px !important;
}

/* ===================================
   روابط التنقل
=================================== */
.nav-link {
    padding: 14px 15px !important;
    border-bottom: 1px solid #f0f0f0 !important;
    border-right: none !important;
    font-size: 1.1rem !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
}

.nav-link:hover {
    background: #f8f9fa !important;
}

.nav-link.active {
    background: #6c5ce7 !important;
    color: white !important;
    border-right: none !important;
    font-weight: 600 !important;
    box-shadow: inset 0 0 0 1px rgba(255,255,255,0.1) !important;
}

.nav-link.active i {
    color: white !important;
}

.nav-link i {
    font-size: 1.2rem !important;
    width: 24px !important;
    text-align: center !important;
    margin-left: 12px !important;
    flex-shrink: 0 !important;
}

.nav-link span {
    line-height: 1.3 !important;
}

/* ===================================
   زر الخروج
=================================== */
.logout-link {
    border-top: 2px solid #f0f0f0 !important;
    margin-top: 10px !important;
    color: #dc3545 !important;
}

.logout-link:hover {
    background: #dc3545 !important;
    color: white !important;
}

.logout-link.active {
    background: #dc3545 !important;
    color: white !important;
}

.logout-link i {
    color: #dc3545 !important;
}

.logout-link:hover i,
.logout-link.active i {
    color: white !important;
}

/* ===================================
   الشريط الجانبي المطوي
=================================== */
.sidebar.collapsed .sidebar-toggle-btn {
    position: fixed !important;
    right: 10px !important;
    top: 80px !important;
    z-index: 9999 !important;
    background: #6c5ce7 !important;
    color: white !important;
    border: 2px solid #6c5ce7 !important;
    border-radius: 8px !important;
    width: 45px !important;
    height: 45px !important;
    box-shadow: 0 4px 15px rgba(108,92,231,0.4) !important;
    animation: pulse 2s infinite;
}

.sidebar.collapsed .sidebar-toggle-btn i {
    font-size: 1rem !important;
    transform: none !important;
}

@keyframes pulse {
    0% {
        box-shadow: 0 4px 15px rgba(108,92,231,0.4), 0 0 0 0 rgba(108,92,231,0.7);
    }
    70% {
        box-shadow: 0 4px 15px rgba(108,92,231,0.4), 0 0 0 10px rgba(108,92,231,0);
    }
    100% {
        box-shadow: 0 4px 15px rgba(108,92,231,0.4), 0 0 0 0 rgba(108,92,231,0);
    }
}

/* ===================================
   معلومات المعمل
=================================== */
.lab-info {
    display: flex !important;
    align-items: center !important;
}

.lab-details h2 {
    margin: 0 !important;
    font-size: 1.1rem !important;
    font-weight: 600 !important;
}

.lab-details small {
    font-size: 0.8rem !important;
    opacity: 0.8 !important;
}
