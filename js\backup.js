// إدارة النسخ الاحتياطي
console.log('🔄 بدء تحميل ملف backup.js...');

class Backup {
    // عرض صفحة النسخ الاحتياطي
    static render() {
        try {
            const pageContent = document.getElementById('pageContent');
            if (!pageContent) {
                console.error('pageContent element not found');
                return;
            }

            pageContent.innerHTML = `
                <div class="backup-container">
                    <!-- عنوان الإدارة المطور -->
                    <div class="department-header backup">
                        <div class="department-header-content">
                            <div class="department-icon">
                                <i class="fas fa-download"></i>
                            </div>
                            <div class="department-info">
                                <h1 class="department-name" data-text="النسخ الاحتياطي">النسخ الاحتياطي</h1>
                                <p class="department-description">إدارة وحفظ النسخ الاحتياطية لجميع بيانات النظام</p>
                                <div class="department-stats">
                                    <div class="department-stat">
                                        <span class="department-stat-value" id="total-backups-count">0</span>
                                        <span class="department-stat-label">إجمالي النسخ</span>
                                    </div>
                                    <div class="department-stat">
                                        <span class="department-stat-value" id="last-backup-date">--</span>
                                        <span class="department-stat-label">آخر نسخة</span>
                                    </div>
                                    <div class="department-stat">
                                        <span class="department-stat-value" id="backup-size">--</span>
                                        <span class="department-stat-label">حجم البيانات</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار النسخ الاحتياطي -->
                    <div class="page-actions-container">
                        <div class="page-actions">
                            <button class="btn btn-primary" onclick="Backup.createBackup()">
                                <i class="fas fa-download"></i>
                                إنشاء نسخة احتياطية
                            </button>
                            <button class="btn btn-success" onclick="Backup.showRestoreModal()">
                                <i class="fas fa-upload"></i>
                                استعادة نسخة احتياطية
                            </button>
                            <button class="btn btn-info" onclick="Backup.exportData()">
                                <i class="fas fa-file-export"></i>
                                تصدير البيانات
                            </button>
                            <button class="btn btn-warning" onclick="Backup.showScheduleModal()">
                                <i class="fas fa-clock"></i>
                                جدولة النسخ
                            </button>
                        </div>
                    </div>

                    <!-- معلومات النظام -->
                    <div class="system-info-section">
                        <h3><i class="fas fa-info-circle"></i> معلومات النظام</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="info-label">إجمالي التركيبات:</span>
                                <span class="info-value" id="total-prosthetics">0</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">إجمالي الأطباء:</span>
                                <span class="info-value" id="total-doctors">0</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">إجمالي الموظفين:</span>
                                <span class="info-value" id="total-employees">0</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">إجمالي المعامل الخارجية:</span>
                                <span class="info-value" id="total-external-labs">0</span>
                            </div>
                        </div>
                    </div>

                    <!-- رسالة ترحيبية -->
                    <div class="welcome-section">
                        <div class="welcome-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3>حماية بياناتك مهمة</h3>
                        <p>قم بإنشاء نسخ احتياطية دورية لضمان سلامة بيانات المعمل</p>
                        <div class="backup-tips">
                            <div class="tip-item">
                                <i class="fas fa-check-circle"></i>
                                <span>انشئ نسخة احتياطية يومياً</span>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle"></i>
                                <span>احفظ النسخ في مكان آمن</span>
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-check-circle"></i>
                                <span>اختبر استعادة النسخ دورياً</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            this.loadStats();

        } catch (error) {
            console.error('Error in Backup.render:', error);
            Utils.showNotification('حدث خطأ في تحميل صفحة النسخ الاحتياطي', 'error');
        }
    }

    // تحميل الإحصائيات
    static loadStats() {
        try {
            const prosthetics = Database.getProsthetics() || [];
            const doctors = Database.getDoctors() || [];
            const employees = Database.getEmployees() || [];
            const externalLabs = Database.getItem('externalLabs') || [];

            // تحديث الإحصائيات
            const totalProsthetics = document.getElementById('total-prosthetics');
            const totalDoctors = document.getElementById('total-doctors');
            const totalEmployees = document.getElementById('total-employees');
            const totalExternalLabs = document.getElementById('total-external-labs');

            if (totalProsthetics) totalProsthetics.textContent = prosthetics.length;
            if (totalDoctors) totalDoctors.textContent = doctors.length;
            if (totalEmployees) totalEmployees.textContent = employees.length;
            if (totalExternalLabs) totalExternalLabs.textContent = externalLabs.length;

            // حساب حجم البيانات التقريبي
            const dataSize = this.calculateDataSize();
            const backupSize = document.getElementById('backup-size');
            if (backupSize) backupSize.textContent = this.formatFileSize(dataSize);

        } catch (error) {
            console.error('خطأ في تحميل إحصائيات النسخ الاحتياطي:', error);
        }
    }

    // حساب حجم البيانات
    static calculateDataSize() {
        try {
            let totalSize = 0;
            
            // حساب حجم جميع البيانات في localStorage
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    totalSize += localStorage[key].length;
                }
            }
            
            return totalSize;
        } catch (error) {
            console.error('خطأ في حساب حجم البيانات:', error);
            return 0;
        }
    }

    // تنسيق حجم الملف
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // إنشاء نسخة احتياطية
    static createBackup() {
        try {
            Utils.showNotification('جاري إنشاء النسخة الاحتياطية...', 'info');

            // جمع جميع البيانات
            const backupData = {
                version: '1.0',
                timestamp: new Date().toISOString(),
                data: {
                    prosthetics: Database.getProsthetics() || [],
                    doctors: Database.getDoctors() || [],
                    employees: Database.getEmployees() || [],
                    externalLabs: Database.getItem('externalLabs') || [],
                    financial: Database.getItem('financial') || [],
                    settings: Database.getSettings() || {},
                    inventory: Database.getItem('inventory') || [],
                    reports: Database.getItem('reports') || []
                }
            };

            // تحويل البيانات إلى JSON
            const jsonData = JSON.stringify(backupData, null, 2);
            
            // إنشاء ملف للتحميل
            const blob = new Blob([jsonData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            // إنشاء رابط التحميل
            const link = document.createElement('a');
            link.href = url;
            link.download = `dental-lab-backup-${Utils.formatDate(new Date(), 'YYYY-MM-DD-HH-mm-ss')}.json`;
            
            // تحميل الملف
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // تنظيف الذاكرة
            URL.revokeObjectURL(url);
            
            Utils.showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
            
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            Utils.showNotification('حدث خطأ في إنشاء النسخة الاحتياطية', 'error');
        }
    }

    // عرض نافذة استعادة النسخة الاحتياطية
    static showRestoreModal() {
        Utils.createModal({
            title: 'استعادة نسخة احتياطية',
            content: `
                <div class="restore-backup-form">
                    <div class="warning-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p><strong>تحذير:</strong> استعادة النسخة الاحتياطية ستحل محل جميع البيانات الحالية.</p>
                    </div>
                    
                    <div class="file-upload-area">
                        <input type="file" id="backupFile" accept=".json" style="display: none;">
                        <div class="upload-zone" onclick="document.getElementById('backupFile').click()">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>انقر لاختيار ملف النسخة الاحتياطية</p>
                            <small>يجب أن يكون الملف بصيغة JSON</small>
                        </div>
                    </div>
                    
                    <div class="modal-actions">
                        <button class="btn btn-primary" onclick="Backup.restoreBackup()">
                            <i class="fas fa-upload"></i>
                            استعادة النسخة
                        </button>
                        <button class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </div>
            `,
            size: 'medium'
        });
    }

    // استعادة النسخة الاحتياطية
    static restoreBackup() {
        const fileInput = document.getElementById('backupFile');
        const file = fileInput.files[0];
        
        if (!file) {
            Utils.showNotification('يرجى اختيار ملف النسخة الاحتياطية', 'warning');
            return;
        }
        
        if (!confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم حذف جميع البيانات الحالية.')) {
            return;
        }
        
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const backupData = JSON.parse(e.target.result);
                
                // التحقق من صحة البيانات
                if (!backupData.data) {
                    throw new Error('ملف النسخة الاحتياطية غير صالح');
                }
                
                // استعادة البيانات
                if (backupData.data.prosthetics) Database.setItem('prosthetics', backupData.data.prosthetics);
                if (backupData.data.doctors) Database.setItem('doctors', backupData.data.doctors);
                if (backupData.data.employees) Database.setItem('employees', backupData.data.employees);
                if (backupData.data.externalLabs) Database.setItem('externalLabs', backupData.data.externalLabs);
                if (backupData.data.financial) Database.setItem('financial', backupData.data.financial);
                if (backupData.data.settings) Database.setItem('settings', backupData.data.settings);
                if (backupData.data.inventory) Database.setItem('inventory', backupData.data.inventory);
                if (backupData.data.reports) Database.setItem('reports', backupData.data.reports);
                
                Utils.closeModal();
                Utils.showNotification('تم استعادة النسخة الاحتياطية بنجاح', 'success');
                
                // إعادة تحميل الصفحة
                setTimeout(() => {
                    location.reload();
                }, 2000);
                
            } catch (error) {
                console.error('خطأ في استعادة النسخة الاحتياطية:', error);
                Utils.showNotification('خطأ في قراءة ملف النسخة الاحتياطية', 'error');
            }
        };
        
        reader.readAsText(file);
    }

    // تصدير البيانات
    static exportData() {
        Utils.showNotification('جاري تصدير البيانات...', 'info');
        
        // محاكاة تصدير البيانات
        setTimeout(() => {
            Utils.showNotification('تم تصدير البيانات بنجاح', 'success');
        }, 2000);
    }

    // عرض نافذة جدولة النسخ
    static showScheduleModal() {
        Utils.createModal({
            title: 'جدولة النسخ الاحتياطية',
            content: `
                <div class="schedule-form">
                    <p>هذه الميزة ستكون متاحة في الإصدارات القادمة</p>
                    <div class="modal-actions">
                        <button class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            `,
            size: 'small'
        });
    }
}

// تسجيل اكتمال تحميل الملف
console.log('✅ تم تحميل ملف backup.js بالكامل');

// التأكد من أن الكلاس متاح عالمياً
window.Backup = Backup;
