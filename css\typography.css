/* ===================================
   نظام الخطوط العصري والمتطور
   Modern Typography System
=================================== */

/* استيراد الخطوط العربية الحديثة */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@100;200;300;400;500;600;700&display=swap');

/* متغيرات الخطوط */
:root {
    /* عائلات الخطوط */
    --font-primary: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-secondary: 'Tajawal', 'Arial', sans-serif;
    --font-elegant: 'Almarai', 'Times New Roman', serif;
    --font-modern: 'IBM Plex Sans Arabic', 'Helvetica', sans-serif;
    
    /* أحجام الخطوط المحدثة */
    --font-size-xs: 0.75rem;      /* 12px */
    --font-size-sm: 0.875rem;     /* 14px */
    --font-size-base: 1rem;       /* 16px */
    --font-size-md: 1.125rem;     /* 18px */
    --font-size-lg: 1.25rem;      /* 20px */
    --font-size-xl: 1.5rem;       /* 24px */
    --font-size-2xl: 1.875rem;    /* 30px */
    --font-size-3xl: 2.25rem;     /* 36px */
    --font-size-4xl: 3rem;        /* 48px */
    --font-size-5xl: 3.75rem;     /* 60px */
    --font-size-6xl: 4.5rem;      /* 72px */
    
    /* أوزان الخطوط */
    --font-weight-thin: 200;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;
    
    /* ارتفاع الأسطر */
    --line-height-tight: 1.25;
    --line-height-snug: 1.375;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;
    
    /* تباعد الأحرف */
    --letter-spacing-tighter: -0.05em;
    --letter-spacing-tight: -0.025em;
    --letter-spacing-normal: 0;
    --letter-spacing-wide: 0.025em;
    --letter-spacing-wider: 0.05em;
    --letter-spacing-widest: 0.1em;
}

/* الخط الأساسي للجسم */
body {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    letter-spacing: var(--letter-spacing-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* العناوين الرئيسية */
h1, .h1 {
    font-family: var(--font-primary);
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
    margin-bottom: 1rem;
    color: var(--text-primary);
}

h2, .h2 {
    font-family: var(--font-primary);
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
    margin-bottom: 0.875rem;
    color: var(--text-primary);
}

h3, .h3 {
    font-family: var(--font-primary);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-snug);
    letter-spacing: var(--letter-spacing-normal);
    margin-bottom: 0.75rem;
    color: var(--text-primary);
}

h4, .h4 {
    font-family: var(--font-primary);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-snug);
    letter-spacing: var(--letter-spacing-normal);
    margin-bottom: 0.625rem;
    color: var(--text-primary);
}

h5, .h5 {
    font-family: var(--font-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    letter-spacing: var(--letter-spacing-normal);
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

h6, .h6 {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-normal);
    letter-spacing: var(--letter-spacing-wide);
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    text-transform: uppercase;
}

/* النصوص العادية */
p, .text-base {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-relaxed);
    letter-spacing: var(--letter-spacing-normal);
    margin-bottom: 1rem;
    color: var(--text-primary);
}

/* أحجام النصوص المساعدة */
.text-xs {
    font-size: var(--font-size-xs);
    line-height: var(--line-height-normal);
}

.text-sm {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
}

.text-md {
    font-size: var(--font-size-md);
    line-height: var(--line-height-normal);
}

.text-lg {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-snug);
}

.text-xl {
    font-size: var(--font-size-xl);
    line-height: var(--line-height-snug);
}

.text-2xl {
    font-size: var(--font-size-2xl);
    line-height: var(--line-height-tight);
}

.text-3xl {
    font-size: var(--font-size-3xl);
    line-height: var(--line-height-tight);
}

.text-4xl {
    font-size: var(--font-size-4xl);
    line-height: var(--line-height-tight);
}

/* أوزان الخطوط */
.font-thin { font-weight: var(--font-weight-thin); }
.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }
.font-extrabold { font-weight: var(--font-weight-extrabold); }
.font-black { font-weight: var(--font-weight-black); }

/* عائلات الخطوط */
.font-primary { font-family: var(--font-primary); }
.font-secondary { font-family: var(--font-secondary); }
.font-elegant { font-family: var(--font-elegant); }
.font-modern { font-family: var(--font-modern); }

/* ارتفاع الأسطر */
.leading-tight { line-height: var(--line-height-tight); }
.leading-snug { line-height: var(--line-height-snug); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }
.leading-loose { line-height: var(--line-height-loose); }

/* تباعد الأحرف */
.tracking-tighter { letter-spacing: var(--letter-spacing-tighter); }
.tracking-tight { letter-spacing: var(--letter-spacing-tight); }
.tracking-normal { letter-spacing: var(--letter-spacing-normal); }
.tracking-wide { letter-spacing: var(--letter-spacing-wide); }
.tracking-wider { letter-spacing: var(--letter-spacing-wider); }
.tracking-widest { letter-spacing: var(--letter-spacing-widest); }

/* تأثيرات النصوص */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: var(--font-weight-bold);
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* تنسيقات خاصة للعناصر */
.page-title {
    font-family: var(--font-primary);
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title {
    font-family: var(--font-primary);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-snug);
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.card-title {
    font-family: var(--font-primary);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-snug);
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.label-text {
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    color: var(--text-secondary);
    letter-spacing: var(--letter-spacing-wide);
    text-transform: uppercase;
}

.body-text {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-relaxed);
    color: var(--text-primary);
}

.caption-text {
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--text-secondary);
    font-style: italic;
}

/* تنسيقات الأزرار */
.btn {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    letter-spacing: var(--letter-spacing-wide);
}

.btn-lg {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.btn-sm {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

/* تنسيقات النماذج */
input, textarea, select {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
}

label {
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    letter-spacing: var(--letter-spacing-wide);
}

/* تنسيقات الجداول */
th {
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-normal);
    letter-spacing: var(--letter-spacing-wide);
    text-transform: uppercase;
}

td {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
}

/* تنسيقات خاصة للقوائم */
.nav-link {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    letter-spacing: var(--letter-spacing-normal);
}

.nav-link.active {
    font-weight: var(--font-weight-semibold);
}

/* تنسيقات الإحصائيات */
.stat-value {
    font-family: var(--font-primary);
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
}

.stat-label {
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    letter-spacing: var(--letter-spacing-wide);
    text-transform: uppercase;
}

/* تنسيقات الشارات والعلامات */
.badge {
    font-family: var(--font-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-wider);
    text-transform: uppercase;
}

/* تنسيقات الرسائل والتنبيهات */
.alert {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-relaxed);
}

.alert-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: 0.5rem;
}

/* تنسيقات خاصة للشريط الجانبي */
.sidebar-title {
    font-family: var(--font-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-normal);
}

/* تنسيقات الشريط العلوي */
.lab-name {
    font-family: var(--font-primary);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.lab-subtitle {
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--text-secondary);
}

/* تنسيقات الشريط السفلي */
.bottom-bar {
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
}

.current-time {
    font-family: var(--font-modern);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    letter-spacing: var(--letter-spacing-wider);
}

/* تأثيرات متقدمة للنصوص */
.text-glow {
    text-shadow: 0 0 10px rgba(108, 92, 231, 0.5);
}

.text-outline {
    -webkit-text-stroke: 1px var(--primary-color);
    -webkit-text-fill-color: transparent;
}

.text-3d {
    text-shadow:
        1px 1px 0 var(--primary-color),
        2px 2px 0 var(--primary-dark),
        3px 3px 5px rgba(0, 0, 0, 0.3);
}

/* تنسيقات متجاوبة للخطوط */
@media (max-width: 1200px) {
    :root {
        --font-size-4xl: 2.5rem;
        --font-size-3xl: 2rem;
        --font-size-2xl: 1.75rem;
        --font-size-xl: 1.375rem;
    }
}

@media (max-width: 768px) {
    :root {
        --font-size-4xl: 2rem;
        --font-size-3xl: 1.75rem;
        --font-size-2xl: 1.5rem;
        --font-size-xl: 1.25rem;
        --font-size-lg: 1.125rem;
    }

    body {
        font-size: var(--font-size-sm);
    }

    .page-title {
        font-size: var(--font-size-3xl);
    }

    .section-title {
        font-size: var(--font-size-xl);
    }
}

@media (max-width: 480px) {
    :root {
        --font-size-4xl: 1.75rem;
        --font-size-3xl: 1.5rem;
        --font-size-2xl: 1.25rem;
        --font-size-xl: 1.125rem;
    }

    .stat-value {
        font-size: var(--font-size-2xl);
    }

    .nav-link {
        font-size: var(--font-size-sm);
    }
}

/* تحسينات الأداء للخطوط */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* تحسين عرض الخطوط العربية */
[lang="ar"], .arabic-text {
    font-feature-settings: "liga" 1, "calt" 1, "kern" 1;
    text-align: right;
    direction: rtl;
}

/* تنسيقات خاصة للأرقام */
.number-display {
    font-family: var(--font-modern);
    font-variant-numeric: tabular-nums;
    letter-spacing: var(--letter-spacing-wide);
}

/* تنسيقات للكود والنصوص التقنية */
.code-text {
    font-family: 'Courier New', monospace;
    font-size: var(--font-size-sm);
    background: var(--bg-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid var(--border-color);
}

/* ===================================
   تصميم عناوين الإدارات المتطور
   Advanced Department Headers
=================================== */

/* حاوي عنوان الإدارة الرئيسي */
.department-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--secondary-color) 100%);
    color: var(--text-on-primary);
    padding: var(--spacing-xl) var(--spacing-2xl);
    border-radius: var(--border-radius-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow:
        0 10px 30px rgba(108, 92, 231, 0.3),
        0 4px 15px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* تأثير الخلفية المتحركة */
.department-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(
        circle at center,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 30%,
        transparent 70%
    );
    animation: headerGlow 6s ease-in-out infinite;
    pointer-events: none;
}

@keyframes headerGlow {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1) rotate(0deg);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.1) rotate(180deg);
    }
}

/* محتوى عنوان الإدارة */
.department-header-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    position: relative;
    z-index: 2;
}

/* أيقونة الإدارة */
.department-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: var(--border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-4xl);
    color: var(--text-on-primary);
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.department-icon:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow:
        0 12px 30px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* معلومات الإدارة */
.department-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

/* اسم الإدارة */
.department-name {
    font-family: var(--font-primary);
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
    color: var(--text-on-primary);
    margin: 0;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.3),
        0 1px 2px rgba(0, 0, 0, 0.2);
    position: relative;
}

/* تأثير توهج النص */
.department-name::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    color: rgba(255, 255, 255, 0.8);
    filter: blur(2px);
    z-index: -1;
    animation: textGlow 3s ease-in-out infinite alternate;
}

@keyframes textGlow {
    0% { opacity: 0.5; }
    100% { opacity: 0.8; }
}

/* وصف الإدارة */
.department-description {
    font-family: var(--font-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-relaxed);
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    opacity: 0.95;
}

/* إحصائيات الإدارة */
.department-stats {
    display: flex;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-md);
}

.department-stat {
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
    text-align: center;
    min-width: 80px;
}

.department-stat-value {
    font-family: var(--font-modern);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-on-primary);
    display: block;
    line-height: 1;
}

.department-stat-label {
    font-family: var(--font-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-wider);
    margin-top: var(--spacing-xs);
}

/* تنسيقات خاصة لكل إدارة */
.department-header.dashboard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.department-header.prosthetics {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 50%, #43e97b 100%);
}

.department-header.doctors {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 50%, #fa709a 100%);
}

.department-header.employees {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 50%, #d299c2 100%);
}

.department-header.financial {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 50%, #ff8a80 100%);
}

.department-header.inventory {
    background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 50%, #a8edea 100%);
}

.department-header.settings {
    background: linear-gradient(135deg, #c2e9fb 0%, #a1c4fd 50%, #667eea 100%);
}

/* تصميم متجاوب للعناوين */
@media (max-width: 1024px) {
    .department-header {
        padding: var(--spacing-lg) var(--spacing-xl);
    }

    .department-icon {
        width: 70px;
        height: 70px;
        font-size: var(--font-size-3xl);
    }

    .department-name {
        font-size: var(--font-size-3xl);
    }

    .department-description {
        font-size: var(--font-size-base);
    }
}

@media (max-width: 768px) {
    .department-header {
        padding: var(--spacing-md) var(--spacing-lg);
    }

    .department-header-content {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .department-icon {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-2xl);
    }

    .department-name {
        font-size: var(--font-size-2xl);
    }

    .department-description {
        font-size: var(--font-size-sm);
    }

    .department-stats {
        justify-content: center;
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .department-header {
        padding: var(--spacing-sm) var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }

    .department-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-xl);
    }

    .department-name {
        font-size: var(--font-size-xl);
    }

    .department-stats {
        gap: var(--spacing-xs);
    }

    .department-stat {
        min-width: 60px;
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .department-stat-value {
        font-size: var(--font-size-base);
    }
}

/* حاوي الأزرار تحت العنوان */
.page-actions-container {
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.page-actions-container .page-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-start;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .page-actions-container {
        padding: var(--spacing-md);
    }

    .page-actions-container .page-actions {
        justify-content: center;
    }
}
