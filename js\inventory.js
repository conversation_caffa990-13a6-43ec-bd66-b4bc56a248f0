/**
 * إدارة المخزن - Inventory Management
 * نظام إدارة شامل للمخزون والمواد
 */

console.log('تم تحميل ملف inventory.js');

class InventoryManager {
    constructor() {
        this.items = this.loadItems();
        this.categories = [
            'مواد خام',
            'أدوات',
            'معدات',
            'مواد استهلاكية',
            'قوالب',
            'مواد تشطيب',
            'أخرى'
        ];
        this.units = [
            'قطعة',
            'كيلو',
            'جرام',
            'لتر',
            'مللي',
            'متر',
            'سنتيمتر',
            'علبة',
            'كيس'
        ];
    }

    // تحميل العناصر من التخزين المحلي
    loadItems() {
        const saved = localStorage.getItem('inventoryItems');
        return saved ? JSON.parse(saved) : [];
    }

    // حفظ العناصر في التخزين المحلي
    saveItems() {
        localStorage.setItem('inventoryItems', JSON.stringify(this.items));
    }

    // إضافة عنصر جديد
    addItem(itemData) {
        const newItem = {
            id: Date.now().toString(),
            name: itemData.name,
            category: itemData.category,
            quantity: parseFloat(itemData.quantity) || 0,
            unit: itemData.unit,
            minQuantity: parseFloat(itemData.minQuantity) || 0,
            price: parseFloat(itemData.price) || 0,
            supplier: itemData.supplier || '',
            description: itemData.description || '',
            dateAdded: new Date().toISOString(),
            lastUpdated: new Date().toISOString()
        };

        this.items.push(newItem);
        this.saveItems();
        return newItem;
    }

    // تحديث عنصر
    updateItem(id, updates) {
        const index = this.items.findIndex(item => item.id === id);
        if (index !== -1) {
            this.items[index] = {
                ...this.items[index],
                ...updates,
                lastUpdated: new Date().toISOString()
            };
            this.saveItems();
            return this.items[index];
        }
        return null;
    }

    // حذف عنصر
    deleteItem(id) {
        const index = this.items.findIndex(item => item.id === id);
        if (index !== -1) {
            const deleted = this.items.splice(index, 1)[0];
            this.saveItems();
            return deleted;
        }
        return null;
    }

    // البحث في العناصر
    searchItems(query) {
        const searchTerm = query.toLowerCase();
        return this.items.filter(item => 
            item.name.toLowerCase().includes(searchTerm) ||
            item.category.toLowerCase().includes(searchTerm) ||
            item.supplier.toLowerCase().includes(searchTerm) ||
            item.description.toLowerCase().includes(searchTerm)
        );
    }

    // فلترة العناصر حسب الفئة
    filterByCategory(category) {
        if (!category || category === 'الكل') {
            return this.items;
        }
        return this.items.filter(item => item.category === category);
    }

    // الحصول على العناصر منخفضة المخزون
    getLowStockItems() {
        return this.items.filter(item => item.quantity <= item.minQuantity);
    }

    // إحصائيات المخزون
    getStatistics() {
        const totalItems = this.items.length;
        const totalValue = this.items.reduce((sum, item) => sum + (item.quantity * item.price), 0);
        const lowStockCount = this.getLowStockItems().length;
        const categoriesCount = [...new Set(this.items.map(item => item.category))].length;

        return {
            totalItems,
            totalValue,
            lowStockCount,
            categoriesCount
        };
    }

    // تصدير البيانات
    exportData() {
        return {
            items: this.items,
            exportDate: new Date().toISOString(),
            totalItems: this.items.length
        };
    }

    // استيراد البيانات
    importData(data) {
        if (data && Array.isArray(data.items)) {
            this.items = data.items;
            this.saveItems();
            return true;
        }
        return false;
    }
}

// إنشاء مثيل من مدير المخزون
const inventoryManager = new InventoryManager();

// تحميل صفحة إدارة المخزن
function loadInventoryPage() {
    console.log('تم استدعاء loadInventoryPage');
    const content = `
        <!-- عنوان الإدارة المطور -->
        <div class="department-header inventory">
            <div class="department-header-content">
                <div class="department-icon">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="department-info">
                    <h1 class="department-name" data-text="إدارة المخزن">إدارة المخزن</h1>
                    <p class="department-description">إدارة شاملة للمخزون والمواد والأدوات ومتابعة المستويات</p>
                    <div class="department-stats">
                        <div class="department-stat">
                            <span class="department-stat-value" id="header-total-items">0</span>
                            <span class="department-stat-label">إجمالي العناصر</span>
                        </div>
                        <div class="department-stat">
                            <span class="department-stat-value" id="header-total-value">0</span>
                            <span class="department-stat-label">القيمة الإجمالية</span>
                        </div>
                        <div class="department-stat">
                            <span class="department-stat-value" id="header-low-stock">0</span>
                            <span class="department-stat-label">مخزون منخفض</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="page-actions-container">
            <div class="page-actions">
                <button class="btn btn-primary" onclick="showAddItemModal()">
                    <i class="fas fa-plus"></i>
                    إضافة عنصر جديد
                </button>
                <button class="btn btn-secondary" onclick="exportInventory()">
                    <i class="fas fa-download"></i>
                    تصدير البيانات
                </button>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="stat-content">
                    <h3 id="totalItemsCount">0</h3>
                    <p>إجمالي العناصر</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-content">
                    <h3 id="totalValue">0</h3>
                    <p>القيمة الإجمالية</p>
                </div>
            </div>
            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-content">
                    <h3 id="lowStockCount">0</h3>
                    <p>عناصر منخفضة المخزون</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="stat-content">
                    <h3 id="categoriesCount">0</h3>
                    <p>عدد الفئات</p>
                </div>
            </div>
        </div>

        <!-- أدوات البحث والفلترة -->
        <div class="inventory-controls">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="inventorySearch" placeholder="البحث في المخزون...">
            </div>
            <div class="filter-controls">
                <select id="categoryFilter">
                    <option value="">جميع الفئات</option>
                </select>
                <button class="btn btn-outline" onclick="showLowStockItems()">
                    <i class="fas fa-exclamation-triangle"></i>
                    عرض المخزون المنخفض
                </button>
            </div>
        </div>

        <!-- جدول المخزون -->
        <div class="inventory-table-container">
            <table class="inventory-table">
                <thead>
                    <tr>
                        <th>اسم العنصر</th>
                        <th>الفئة</th>
                        <th>الكمية</th>
                        <th>الوحدة</th>
                        <th>الحد الأدنى</th>
                        <th>السعر</th>
                        <th>المورد</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="inventoryTableBody">
                    <!-- سيتم ملء البيانات هنا -->
                </tbody>
            </table>
        </div>
    `;

    document.getElementById('pageContent').innerHTML = content;
    
    // تحميل البيانات
    loadInventoryData();
    setupInventoryEventListeners();
}

// تحميل بيانات المخزون
function loadInventoryData() {
    updateInventoryStats();
    loadCategoryFilter();
    displayInventoryItems(inventoryManager.items);
}

// تحديث الإحصائيات
function updateInventoryStats() {
    const stats = inventoryManager.getStatistics();

    // تحديث البطاقات
    document.getElementById('totalItemsCount').textContent = stats.totalItems;
    document.getElementById('totalValue').textContent = stats.totalValue.toFixed(2) + ' ر.س';
    document.getElementById('lowStockCount').textContent = stats.lowStockCount;
    document.getElementById('categoriesCount').textContent = stats.categoriesCount;

    // تحديث إحصائيات العنوان
    const headerTotalItems = document.getElementById('header-total-items');
    const headerTotalValue = document.getElementById('header-total-value');
    const headerLowStock = document.getElementById('header-low-stock');

    if (headerTotalItems) headerTotalItems.textContent = stats.totalItems;
    if (headerTotalValue) headerTotalValue.textContent = stats.totalValue.toFixed(0) + ' ر.س';
    if (headerLowStock) headerLowStock.textContent = stats.lowStockCount;
}

// تحميل فلتر الفئات
function loadCategoryFilter() {
    const categoryFilter = document.getElementById('categoryFilter');
    categoryFilter.innerHTML = '<option value="">جميع الفئات</option>';
    
    inventoryManager.categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        categoryFilter.appendChild(option);
    });
}

// عرض عناصر المخزون
function displayInventoryItems(items) {
    const tbody = document.getElementById('inventoryTableBody');
    
    if (items.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-boxes"></i>
                        <p>لا توجد عناصر في المخزون</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = items.map(item => {
        const isLowStock = item.quantity <= item.minQuantity;
        const statusClass = isLowStock ? 'status-warning' : 'status-normal';
        const statusText = isLowStock ? 'مخزون منخفض' : 'طبيعي';

        return `
            <tr>
                <td>${item.name}</td>
                <td>${item.category}</td>
                <td>${item.quantity}</td>
                <td>${item.unit}</td>
                <td>${item.minQuantity}</td>
                <td>${item.price.toFixed(2)} ر.س</td>
                <td>${item.supplier}</td>
                <td><span class="status ${statusClass}">${statusText}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon" onclick="editItem('${item.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon" onclick="deleteItem('${item.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// إعداد مستمعي الأحداث
function setupInventoryEventListeners() {
    // البحث
    const searchInput = document.getElementById('inventorySearch');
    searchInput.addEventListener('input', (e) => {
        const results = inventoryManager.searchItems(e.target.value);
        displayInventoryItems(results);
    });

    // فلتر الفئات
    const categoryFilter = document.getElementById('categoryFilter');
    categoryFilter.addEventListener('change', (e) => {
        const filtered = inventoryManager.filterByCategory(e.target.value);
        displayInventoryItems(filtered);
    });
}

// عرض نافذة إضافة عنصر
function showAddItemModal() {
    const modalContent = `
        <div class="modal-header">
            <h3>إضافة عنصر جديد</h3>
            <button class="modal-close" onclick="closeModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="addItemForm">
                <div class="form-row">
                    <div class="form-group">
                        <label>اسم العنصر *</label>
                        <input type="text" name="name" required>
                    </div>
                    <div class="form-group">
                        <label>الفئة *</label>
                        <select name="category" required>
                            ${inventoryManager.categories.map(cat =>
                                `<option value="${cat}">${cat}</option>`
                            ).join('')}
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>الكمية *</label>
                        <input type="number" name="quantity" min="0" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label>الوحدة *</label>
                        <select name="unit" required>
                            ${inventoryManager.units.map(unit =>
                                `<option value="${unit}">${unit}</option>`
                            ).join('')}
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>الحد الأدنى</label>
                        <input type="number" name="minQuantity" min="0" step="0.01">
                    </div>
                    <div class="form-group">
                        <label>السعر</label>
                        <input type="number" name="price" min="0" step="0.01">
                    </div>
                </div>
                <div class="form-group">
                    <label>المورد</label>
                    <input type="text" name="supplier">
                </div>
                <div class="form-group">
                    <label>الوصف</label>
                    <textarea name="description" rows="3"></textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
            <button class="btn btn-primary" onclick="saveNewItem()">حفظ</button>
        </div>
    `;

    showModal(modalContent);
}

// حفظ عنصر جديد
function saveNewItem() {
    const form = document.getElementById('addItemForm');
    const formData = new FormData(form);
    const itemData = Object.fromEntries(formData.entries());

    if (!itemData.name || !itemData.category || !itemData.quantity || !itemData.unit) {
        Utils.showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    const newItem = inventoryManager.addItem(itemData);
    if (newItem) {
        Utils.showNotification('تم إضافة العنصر بنجاح', 'success');
        closeModal();
        loadInventoryData();
    }
}

// تعديل عنصر
function editItem(id) {
    const item = inventoryManager.items.find(item => item.id === id);
    if (!item) return;

    const modalContent = `
        <div class="modal-header">
            <h3>تعديل العنصر</h3>
            <button class="modal-close" onclick="closeModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="editItemForm">
                <input type="hidden" name="id" value="${item.id}">
                <div class="form-row">
                    <div class="form-group">
                        <label>اسم العنصر *</label>
                        <input type="text" name="name" value="${item.name}" required>
                    </div>
                    <div class="form-group">
                        <label>الفئة *</label>
                        <select name="category" required>
                            ${inventoryManager.categories.map(cat =>
                                `<option value="${cat}" ${cat === item.category ? 'selected' : ''}>${cat}</option>`
                            ).join('')}
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>الكمية *</label>
                        <input type="number" name="quantity" value="${item.quantity}" min="0" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label>الوحدة *</label>
                        <select name="unit" required>
                            ${inventoryManager.units.map(unit =>
                                `<option value="${unit}" ${unit === item.unit ? 'selected' : ''}>${unit}</option>`
                            ).join('')}
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>الحد الأدنى</label>
                        <input type="number" name="minQuantity" value="${item.minQuantity}" min="0" step="0.01">
                    </div>
                    <div class="form-group">
                        <label>السعر</label>
                        <input type="number" name="price" value="${item.price}" min="0" step="0.01">
                    </div>
                </div>
                <div class="form-group">
                    <label>المورد</label>
                    <input type="text" name="supplier" value="${item.supplier}">
                </div>
                <div class="form-group">
                    <label>الوصف</label>
                    <textarea name="description" rows="3">${item.description}</textarea>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
            <button class="btn btn-primary" onclick="saveEditedItem()">حفظ التغييرات</button>
        </div>
    `;

    showModal(modalContent);
}

// حفظ التعديلات
function saveEditedItem() {
    const form = document.getElementById('editItemForm');
    const formData = new FormData(form);
    const itemData = Object.fromEntries(formData.entries());
    const id = itemData.id;
    delete itemData.id;

    const updated = inventoryManager.updateItem(id, itemData);
    if (updated) {
        Utils.showNotification('تم تحديث العنصر بنجاح', 'success');
        closeModal();
        loadInventoryData();
    }
}

// حذف عنصر
function deleteItem(id) {
    const item = inventoryManager.items.find(item => item.id === id);
    if (!item) return;

    if (confirm(`هل أنت متأكد من حذف "${item.name}"؟`)) {
        const deleted = inventoryManager.deleteItem(id);
        if (deleted) {
            Utils.showNotification('تم حذف العنصر بنجاح', 'success');
            loadInventoryData();
        }
    }
}

// عرض العناصر منخفضة المخزون
function showLowStockItems() {
    const lowStockItems = inventoryManager.getLowStockItems();
    displayInventoryItems(lowStockItems);

    if (lowStockItems.length === 0) {
        Utils.showNotification('لا توجد عناصر منخفضة المخزون', 'info');
    } else {
        Utils.showNotification(`تم العثور على ${lowStockItems.length} عنصر منخفض المخزون`, 'warning');
    }
}

// تصدير البيانات
function exportInventory() {
    const data = inventoryManager.exportData();
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `inventory_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    Utils.showNotification('تم تصدير بيانات المخزون بنجاح', 'success');
}

// وظائف مساعدة للنوافذ المنبثقة
function showModal(content) {
    const overlay = document.getElementById('modalOverlay');
    const modalContent = document.getElementById('modalContent');

    if (overlay && modalContent) {
        modalContent.innerHTML = content;
        overlay.style.display = 'flex';
    }
}

function closeModal() {
    const overlay = document.getElementById('modalOverlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}
