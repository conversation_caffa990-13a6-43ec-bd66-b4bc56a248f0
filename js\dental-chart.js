// مخطط الأسنان التفاعلي
class DentalChart {
    static selectedTeeth = [];
    static currentMode = 'select';
    static onSelectionChange = null;

    // إنشاء مخطط الأسنان
    static create(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const {
            selectable = true,
            multiSelect = true,
            showControls = true,
            showInfo = true,
            onSelectionChange = null
        } = options;

        this.onSelectionChange = onSelectionChange;

        container.innerHTML = `
            <div class="dental-chart-container">
                <div class="dental-chart-header">
                    <h3 class="dental-chart-title">مخطط الأسنان</h3>
                    <div class="dental-chart-info">
                        انقر على الأسنان لتحديدها
                    </div>
                </div>

                <div class="dental-chart">
                    <!-- الفك العلوي -->
                    <div class="jaw-section">
                        <div class="jaw-label">الفك العلوي</div>
                        <div class="teeth-row upper-jaw">
                            ${this.generateTeethHTML('upper')}
                        </div>
                    </div>

                    <!-- الفك السفلي -->
                    <div class="jaw-section">
                        <div class="jaw-label">الفك السفلي</div>
                        <div class="teeth-row lower-jaw">
                            ${this.generateTeethHTML('lower')}
                        </div>
                    </div>
                </div>

                ${showControls ? this.generateControlsHTML() : ''}
                ${showInfo ? this.generateInfoHTML() : ''}
                ${this.generateLegendHTML()}
            </div>
        `;

        this.setupEventListeners(container, selectable, multiSelect);
    }

    // توليد HTML للأسنان
    static generateTeethHTML(jaw) {
        const teethData = this.getTeethData(jaw);
        
        return teethData.map(tooth => `
            <div class="tooth ${tooth.type}" 
                 data-number="${tooth.number}" 
                 data-jaw="${jaw}"
                 data-type="${tooth.type}"
                 title="سن رقم ${tooth.number} - ${tooth.name}">
                <div class="tooth-number">${tooth.number}</div>
                <div class="tooth-type">${tooth.shortName}</div>
            </div>
        `).join('');
    }

    // الحصول على بيانات الأسنان
    static getTeethData(jaw) {
        const upperTeeth = [
            { number: 18, name: 'ضرس العقل الأيمن العلوي', shortName: 'ض.ع', type: 'molar' },
            { number: 17, name: 'الضرس الثاني الأيمن العلوي', shortName: 'ض2', type: 'molar' },
            { number: 16, name: 'الضرس الأول الأيمن العلوي', shortName: 'ض1', type: 'molar' },
            { number: 15, name: 'الضاحك الثاني الأيمن العلوي', shortName: 'ضا2', type: 'premolar' },
            { number: 14, name: 'الضاحك الأول الأيمن العلوي', shortName: 'ضا1', type: 'premolar' },
            { number: 13, name: 'الناب الأيمن العلوي', shortName: 'ناب', type: 'canine' },
            { number: 12, name: 'القاطع الجانبي الأيمن العلوي', shortName: 'ق.ج', type: 'incisor' },
            { number: 11, name: 'القاطع المركزي الأيمن العلوي', shortName: 'ق.م', type: 'incisor' },
            { number: 21, name: 'القاطع المركزي الأيسر العلوي', shortName: 'ق.م', type: 'incisor' },
            { number: 22, name: 'القاطع الجانبي الأيسر العلوي', shortName: 'ق.ج', type: 'incisor' },
            { number: 23, name: 'الناب الأيسر العلوي', shortName: 'ناب', type: 'canine' },
            { number: 24, name: 'الضاحك الأول الأيسر العلوي', shortName: 'ضا1', type: 'premolar' },
            { number: 25, name: 'الضاحك الثاني الأيسر العلوي', shortName: 'ضا2', type: 'premolar' },
            { number: 26, name: 'الضرس الأول الأيسر العلوي', shortName: 'ض1', type: 'molar' },
            { number: 27, name: 'الضرس الثاني الأيسر العلوي', shortName: 'ض2', type: 'molar' },
            { number: 28, name: 'ضرس العقل الأيسر العلوي', shortName: 'ض.ع', type: 'molar' }
        ];

        const lowerTeeth = [
            { number: 48, name: 'ضرس العقل الأيمن السفلي', shortName: 'ض.ع', type: 'molar' },
            { number: 47, name: 'الضرس الثاني الأيمن السفلي', shortName: 'ض2', type: 'molar' },
            { number: 46, name: 'الضرس الأول الأيمن السفلي', shortName: 'ض1', type: 'molar' },
            { number: 45, name: 'الضاحك الثاني الأيمن السفلي', shortName: 'ضا2', type: 'premolar' },
            { number: 44, name: 'الضاحك الأول الأيمن السفلي', shortName: 'ضا1', type: 'premolar' },
            { number: 43, name: 'الناب الأيمن السفلي', shortName: 'ناب', type: 'canine' },
            { number: 42, name: 'القاطع الجانبي الأيمن السفلي', shortName: 'ق.ج', type: 'incisor' },
            { number: 41, name: 'القاطع المركزي الأيمن السفلي', shortName: 'ق.م', type: 'incisor' },
            { number: 31, name: 'القاطع المركزي الأيسر السفلي', shortName: 'ق.م', type: 'incisor' },
            { number: 32, name: 'القاطع الجانبي الأيسر السفلي', shortName: 'ق.ج', type: 'incisor' },
            { number: 33, name: 'الناب الأيسر السفلي', shortName: 'ناب', type: 'canine' },
            { number: 34, name: 'الضاحك الأول الأيسر السفلي', shortName: 'ضا1', type: 'premolar' },
            { number: 35, name: 'الضاحك الثاني الأيسر السفلي', shortName: 'ضا2', type: 'premolar' },
            { number: 36, name: 'الضرس الأول الأيسر السفلي', shortName: 'ض1', type: 'molar' },
            { number: 37, name: 'الضرس الثاني الأيسر السفلي', shortName: 'ض2', type: 'molar' },
            { number: 38, name: 'ضرس العقل الأيسر السفلي', shortName: 'ض.ع', type: 'molar' }
        ];

        return jaw === 'upper' ? upperTeeth : lowerTeeth;
    }

    // توليد HTML لأدوات التحكم
    static generateControlsHTML() {
        return `
            <div class="dental-chart-controls">
                <button class="chart-control-btn active" data-mode="select">
                    <i class="fas fa-mouse-pointer"></i>
                    تحديد
                </button>
                <button class="chart-control-btn" onclick="DentalChart.selectAll()">
                    <i class="fas fa-check-double"></i>
                    تحديد الكل
                </button>
                <button class="chart-control-btn" onclick="DentalChart.clearSelection()">
                    <i class="fas fa-times"></i>
                    إلغاء التحديد
                </button>
                <button class="chart-control-btn" onclick="DentalChart.selectUpperJaw()">
                    <i class="fas fa-arrow-up"></i>
                    الفك العلوي
                </button>
                <button class="chart-control-btn" onclick="DentalChart.selectLowerJaw()">
                    <i class="fas fa-arrow-down"></i>
                    الفك السفلي
                </button>
            </div>
        `;
    }

    // توليد HTML لمعلومات الأسنان المحددة
    static generateInfoHTML() {
        return `
            <div class="selected-teeth-info" style="display: none;">
                <div class="selected-teeth-header">
                    <i class="fas fa-info-circle"></i>
                    الأسنان المحددة
                </div>
                <div class="selected-teeth-list"></div>
                <div class="teeth-count"></div>
            </div>
        `;
    }

    // توليد HTML للأسطورة
    static generateLegendHTML() {
        return `
            <div class="dental-chart-legend">
                <div class="legend-item">
                    <div class="legend-color selected"></div>
                    <span>محدد</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color porcelain"></div>
                    <span>بورسلين</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color zircon"></div>
                    <span>زيركون</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color metal"></div>
                    <span>معدن</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color removable"></div>
                    <span>متحرك</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color orthodontic"></div>
                    <span>تقويم</span>
                </div>
            </div>
        `;
    }

    // إعداد مستمعي الأحداث
    static setupEventListeners(container, selectable, multiSelect) {
        if (!selectable) return;

        // النقر على الأسنان
        container.addEventListener('click', (e) => {
            const tooth = e.target.closest('.tooth');
            if (!tooth) return;

            const toothNumber = parseInt(tooth.dataset.number);
            
            if (multiSelect && e.ctrlKey) {
                this.toggleTooth(toothNumber);
            } else if (multiSelect && e.shiftKey && this.selectedTeeth.length > 0) {
                this.selectRange(this.selectedTeeth[this.selectedTeeth.length - 1], toothNumber);
            } else {
                if (!multiSelect) {
                    this.clearSelection();
                }
                this.toggleTooth(toothNumber);
            }

            this.updateDisplay(container);
        });

        // أدوات التحكم
        const controlBtns = container.querySelectorAll('.chart-control-btn[data-mode]');
        controlBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                controlBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.currentMode = btn.dataset.mode;
            });
        });
    }

    // تبديل تحديد السن
    static toggleTooth(toothNumber) {
        const index = this.selectedTeeth.indexOf(toothNumber);
        if (index > -1) {
            this.selectedTeeth.splice(index, 1);
        } else {
            this.selectedTeeth.push(toothNumber);
        }

        if (this.onSelectionChange) {
            this.onSelectionChange(this.selectedTeeth);
        }
    }

    // تحديد نطاق من الأسنان
    static selectRange(start, end) {
        const min = Math.min(start, end);
        const max = Math.max(start, end);
        
        for (let i = min; i <= max; i++) {
            if (!this.selectedTeeth.includes(i)) {
                this.selectedTeeth.push(i);
            }
        }

        if (this.onSelectionChange) {
            this.onSelectionChange(this.selectedTeeth);
        }
    }

    // تحديد جميع الأسنان
    static selectAll() {
        this.selectedTeeth = [];
        for (let i = 11; i <= 18; i++) this.selectedTeeth.push(i);
        for (let i = 21; i <= 28; i++) this.selectedTeeth.push(i);
        for (let i = 31; i <= 38; i++) this.selectedTeeth.push(i);
        for (let i = 41; i <= 48; i++) this.selectedTeeth.push(i);

        this.updateAllDisplays();
        
        if (this.onSelectionChange) {
            this.onSelectionChange(this.selectedTeeth);
        }
    }

    // تحديد الفك العلوي
    static selectUpperJaw() {
        this.selectedTeeth = this.selectedTeeth.filter(t => t < 30);
        for (let i = 11; i <= 18; i++) {
            if (!this.selectedTeeth.includes(i)) this.selectedTeeth.push(i);
        }
        for (let i = 21; i <= 28; i++) {
            if (!this.selectedTeeth.includes(i)) this.selectedTeeth.push(i);
        }

        this.updateAllDisplays();
        
        if (this.onSelectionChange) {
            this.onSelectionChange(this.selectedTeeth);
        }
    }

    // تحديد الفك السفلي
    static selectLowerJaw() {
        this.selectedTeeth = this.selectedTeeth.filter(t => t >= 30);
        for (let i = 31; i <= 38; i++) {
            if (!this.selectedTeeth.includes(i)) this.selectedTeeth.push(i);
        }
        for (let i = 41; i <= 48; i++) {
            if (!this.selectedTeeth.includes(i)) this.selectedTeeth.push(i);
        }

        this.updateAllDisplays();
        
        if (this.onSelectionChange) {
            this.onSelectionChange(this.selectedTeeth);
        }
    }

    // إلغاء التحديد
    static clearSelection() {
        this.selectedTeeth = [];
        this.updateAllDisplays();
        
        if (this.onSelectionChange) {
            this.onSelectionChange(this.selectedTeeth);
        }
    }

    // تحديث العرض
    static updateDisplay(container) {
        // تحديث مظهر الأسنان
        const teeth = container.querySelectorAll('.tooth');
        teeth.forEach(tooth => {
            const toothNumber = parseInt(tooth.dataset.number);
            if (this.selectedTeeth.includes(toothNumber)) {
                tooth.classList.add('selected');
                tooth.classList.add('selecting');
                setTimeout(() => tooth.classList.remove('selecting'), 300);
            } else {
                tooth.classList.remove('selected');
            }
        });

        // تحديث معلومات الأسنان المحددة
        this.updateSelectedTeethInfo(container);
    }

    // تحديث جميع العروض
    static updateAllDisplays() {
        const containers = document.querySelectorAll('.dental-chart-container');
        containers.forEach(container => {
            this.updateDisplay(container);
        });
    }

    // تحديث معلومات الأسنان المحددة
    static updateSelectedTeethInfo(container) {
        const infoContainer = container.querySelector('.selected-teeth-info');
        if (!infoContainer) return;

        if (this.selectedTeeth.length === 0) {
            infoContainer.style.display = 'none';
            return;
        }

        infoContainer.style.display = 'block';

        const listContainer = infoContainer.querySelector('.selected-teeth-list');
        const countContainer = infoContainer.querySelector('.teeth-count');

        // عرض قائمة الأسنان المحددة
        const teethTags = this.selectedTeeth.sort((a, b) => a - b).map(toothNumber => `
            <div class="selected-tooth-tag">
                ${toothNumber}
                <span class="remove-tooth" onclick="DentalChart.removeTooth(${toothNumber})">
                    <i class="fas fa-times"></i>
                </span>
            </div>
        `).join('');

        listContainer.innerHTML = teethTags;
        countContainer.textContent = `إجمالي الأسنان المحددة: ${this.selectedTeeth.length}`;
    }

    // إزالة سن من التحديد
    static removeTooth(toothNumber) {
        const index = this.selectedTeeth.indexOf(toothNumber);
        if (index > -1) {
            this.selectedTeeth.splice(index, 1);
            this.updateAllDisplays();
            
            if (this.onSelectionChange) {
                this.onSelectionChange(this.selectedTeeth);
            }
        }
    }

    // تطبيق نوع التركيبة على الأسنان المحددة
    static applyProstheticType(type) {
        const containers = document.querySelectorAll('.dental-chart-container');
        containers.forEach(container => {
            const teeth = container.querySelectorAll('.tooth');
            teeth.forEach(tooth => {
                const toothNumber = parseInt(tooth.dataset.number);
                if (this.selectedTeeth.includes(toothNumber)) {
                    // إزالة الأنواع السابقة
                    tooth.classList.remove('porcelain', 'zircon', 'metal', 'removable', 'orthodontic');
                    // إضافة النوع الجديد
                    if (type) {
                        tooth.classList.add(type);
                        
                        // إضافة مؤشر نوع التركيبة
                        let indicator = tooth.querySelector('.prosthetic-indicator');
                        if (!indicator) {
                            indicator = document.createElement('div');
                            indicator.className = 'prosthetic-indicator';
                            tooth.appendChild(indicator);
                        }
                        indicator.className = `prosthetic-indicator ${type}`;
                        indicator.textContent = this.getProstheticTypeShort(type);
                    } else {
                        // إزالة المؤشر
                        const indicator = tooth.querySelector('.prosthetic-indicator');
                        if (indicator) {
                            indicator.remove();
                        }
                    }
                }
            });
        });
    }

    // الحصول على الاختصار لنوع التركيبة
    static getProstheticTypeShort(type) {
        const shortcuts = {
            porcelain: 'ب',
            zircon: 'ز',
            metal: 'م',
            removable: 'ت',
            orthodontic: 'ق'
        };
        return shortcuts[type] || '';
    }

    // الحصول على الأسنان المحددة
    static getSelectedTeeth() {
        return [...this.selectedTeeth];
    }

    // تعيين الأسنان المحددة
    static setSelectedTeeth(teeth) {
        this.selectedTeeth = [...teeth];
        this.updateAllDisplays();
        
        if (this.onSelectionChange) {
            this.onSelectionChange(this.selectedTeeth);
        }
    }

    // إعادة تعيين المخطط
    static reset() {
        this.selectedTeeth = [];
        this.currentMode = 'select';
        this.updateAllDisplays();
    }
}
