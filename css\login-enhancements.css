/* تحسينات شاشة تسجيل الدخول */

/* شعار تسجيل الدخول */
.login-logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    animation: logoFloat 3s ease-in-out infinite;
}

.login-logo i {
    font-size: var(--font-size-3xl);
    color: var(--text-white);
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

/* مجموعات الإدخال */
.input-group {
    position: relative;
    margin-bottom: var(--spacing-lg);
}

.input-group i {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    transition: color var(--transition-fast);
    z-index: 2;
}

.input-group input {
    width: 100%;
    padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-lg) 50px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-base);
    background: var(--bg-secondary);
    color: var(--text-primary);
    transition: all var(--transition-normal);
    position: relative;
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.input-group input:focus + i {
    color: var(--primary-color);
}

.input-group input::placeholder {
    color: var(--text-muted);
    transition: opacity var(--transition-fast);
}

.input-group input:focus::placeholder {
    opacity: 0.7;
}

/* خيارات النموذج */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
}

/* زر تسجيل الدخول */
.btn-login {
    width: 100%;
    padding: var(--spacing-lg);
    font-size: var(--font-size-lg);
    font-weight: 600;
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-lg);
    position: relative;
    overflow: hidden;
}

.btn-login::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left var(--transition-slow);
}

.btn-login:hover::before {
    left: 100%;
}

.btn-login:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.btn-login:active {
    transform: translateY(-1px);
}

/* تذييل تسجيل الدخول */
.login-footer {
    text-align: center;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.login-footer .btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-md);
}

.login-footer p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: var(--spacing-sm) 0 0;
}

/* تأثيرات الحركة */
.login-container {
    animation: slideInUp 0.8s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات الاستجابة لشاشة تسجيل الدخول */
@media (max-width: 768px) {
    .login-container {
        margin: var(--spacing-lg);
        padding: var(--spacing-xl);
    }
    
    .login-logo {
        width: 60px;
        height: 60px;
    }
    
    .login-logo i {
        font-size: var(--font-size-2xl);
    }
    
    .login-header h1 {
        font-size: var(--font-size-2xl);
    }
    
    .input-group input {
        padding: var(--spacing-md) var(--spacing-lg) var(--spacing-md) 45px;
    }
    
    .input-group i {
        font-size: var(--font-size-base);
    }
}

/* تأثيرات إضافية للخلفية */
#loginScreen::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: gradientShift 10s ease-in-out infinite;
    pointer-events: none;
}

@keyframes gradientShift {
    0%, 100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1) rotate(2deg);
    }
}

/* تحسينات إضافية للنص */
.login-header h1 {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
}

.login-header p {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
    font-weight: 400;
    margin-bottom: var(--spacing-xl);
}

/* تأثيرات التركيز المحسنة */
.input-group input:focus {
    background: linear-gradient(135deg, var(--bg-secondary), #f8fafc);
}

/* تحسينات مربع الاختيار */
.checkbox-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.checkbox-label:hover {
    color: var(--text-primary);
}

/* تأثيرات الزر المحسنة */
.btn-login {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border: none;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
}

.btn-login:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
    box-shadow: var(--shadow-xl);
}

/* تحسينات زر إعادة التعيين */
.btn-secondary.btn-sm {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: var(--text-white);
    border: none;
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
}

.btn-secondary.btn-sm:hover {
    background: linear-gradient(135deg, #5a6268, #495057);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* تأثيرات الضوء */
.login-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
    animation: lightPulse 4s ease-in-out infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes lightPulse {
    0%, 100% {
        opacity: 0.5;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1);
    }
}

/* تحسينات الطباعة */
@media print {
    #loginScreen {
        display: none !important;
    }
}

/* تحسينات إمكانية الوصول */
.input-group input:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.btn-login:focus {
    outline: 2px solid var(--primary-light);
    outline-offset: 2px;
}

/* تأثيرات الحركة المتقدمة */
.login-form .form-group {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

.login-form .form-group:nth-child(1) { animation-delay: 0.1s; }
.login-form .form-group:nth-child(2) { animation-delay: 0.2s; }
.login-form .form-options { animation-delay: 0.3s; }
.login-form .btn-login { animation-delay: 0.4s; }
.login-form .login-footer { animation-delay: 0.5s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تنسيق بيانات تسجيل الدخول */
.login-credentials {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border: 1px solid #cbd5e1;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.login-credentials h4 {
    color: #1e293b;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.login-credentials h4::before {
    content: "🔑";
    font-size: 18px;
}

.login-credentials::after {
    content: "💡 انقر على البيانات لنسخها أو استخدم زر الملء التلقائي";
    display: block;
    font-size: 12px;
    color: #64748b;
    text-align: center;
    margin-top: 10px;
    font-style: italic;
}

.credentials-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.credential-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.credential-item:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.credential-label {
    font-weight: 500;
    color: #475569;
    font-size: 14px;
}

.credential-value {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #1e293b;
    background: #f1f5f9;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.credential-value:hover {
    background: #e2e8f0;
    transform: scale(1.05);
}

.credential-value:active {
    transform: scale(0.95);
}

/* تحسين زر إعادة تعيين قاعدة البيانات */
.reset-db-btn {
    width: 100%;
    margin-top: 15px;
    padding: 12px;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.reset-db-btn:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.reset-db-btn:active {
    transform: translateY(0);
}

.reset-db-btn i {
    font-size: 16px;
}

/* زر الملء التلقائي */
.auto-fill-btn {
    width: 100%;
    margin-top: 15px;
    padding: 12px;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.auto-fill-btn:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.auto-fill-btn:active {
    transform: translateY(0);
}

.auto-fill-btn i {
    font-size: 16px;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .login-credentials {
        padding: 15px;
        margin-bottom: 15px;
    }

    .credential-item {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }

    .credential-value {
        font-size: 16px;
        padding: 8px 12px;
    }
}
