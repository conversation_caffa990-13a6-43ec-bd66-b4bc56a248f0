// التطبيق الرئيسي
class App {
    static version = '1.0.0';
    static initialized = false;

    // تهيئة التطبيق
    static init() {
        if (this.initialized) return;

        console.log(`🦷 نظام إدارة معمل الأسنان v${this.version}`);
        console.log('جاري تهيئة النظام...');

        try {
            // تهيئة قاعدة البيانات
            Database.init();
            console.log('✅ تم تهيئة قاعدة البيانات');

            // تهيئة نظام المصادقة
            Auth.init();
            console.log('✅ تم تهيئة نظام المصادقة');

            // تهيئة نظام التنقل
            Navigation.init();
            console.log('✅ تم تهيئة نظام التنقل');

            // تهيئة مدير الشريط الجانبي
            if (typeof SidebarManager !== 'undefined') {
                SidebarManager.init();
                console.log('✅ تم تهيئة مدير الشريط الجانبي');
            }

            // تهيئة مدير المظهر
            if (typeof ThemeManager !== 'undefined') {
                ThemeManager.init();
                console.log('✅ تم تهيئة مدير المظهر');

                // تحديث معلومات المعمل بعد تحميل قاعدة البيانات
                setTimeout(() => {
                    ThemeManager.updateLabInfo();
                }, 500);
            }

            // تهيئة الإعدادات السريعة
            if (typeof QuickSettings !== 'undefined') {
                QuickSettings.init();
                console.log('✅ تم تهيئة الإعدادات السريعة');
            }

            // تهيئة الشريط السفلي
            if (typeof BottomBar !== 'undefined') {
                BottomBar.init();
                console.log('✅ تم تهيئة الشريط السفلي');
            }



            // تحميل الإعدادات المحفوظة
            this.loadSavedSettings();

            // تنظيف الأخطاء السابقة
            this.clearPreviousErrors();

            // إعداد معالجات الأخطاء العامة
            this.setupErrorHandlers();
            console.log('✅ تم إعداد معالجات الأخطاء');

            // إعداد الأحداث العامة
            this.setupGlobalEvents();
            console.log('✅ تم إعداد الأحداث العامة');

            // إعداد تحديث شاشة تسجيل الدخول
            this.setupLoginScreenUpdates();
            console.log('✅ تم إعداد تحديث شاشة تسجيل الدخول');

            // تحديث العنوان
            this.updateTitle();

            // إضافة التأثيرات الحديثة
            this.addModernEffects();

            this.initialized = true;
            console.log('🎉 تم تهيئة النظام بنجاح');

            // إظهار رسالة ترحيب
            setTimeout(() => {
                if (Auth.isLoggedIn()) {
                    Utils.showNotification('مرحباً بك في نظام إدارة معمل الأسنان', 'success');
                }
            }, 1000);

        } catch (error) {
            console.error('❌ خطأ في تهيئة النظام:', error);
            this.handleCriticalError(error);
        }
    }

    // تنظيف الأخطاء السابقة
    static clearPreviousErrors() {
        try {
            // مسح سجل الأخطاء القديم
            localStorage.removeItem('dentalLab_errors');
            localStorage.removeItem('errorLog');

            // إعادة تعيين متغيرات الأخطاء
            if (typeof ErrorHandler !== 'undefined') {
                ErrorHandler.errors = [];
                ErrorHandler.lastErrors = {};
            }

            console.log('🧹 تم تنظيف الأخطاء السابقة');
        } catch (error) {
            console.warn('تعذر تنظيف الأخطاء السابقة:', error);
        }
    }

    // إعداد معالجات الأخطاء
    static setupErrorHandlers() {
        // معالجة الأخطاء غير المتوقعة
        window.addEventListener('error', (event) => {
            // تجنب الأخطاء المتكررة
            if (event.error && event.error.message !== 'Script error.') {
                console.error('خطأ JavaScript:', event.error);
                this.logError('JavaScript Error', event.error);
            }
        });

        // معالجة الأخطاء في الوعود
        window.addEventListener('unhandledrejection', (event) => {
            console.error('خطأ في Promise:', event.reason);
            this.logError('Promise Rejection', event.reason);
            // منع عرض الخطأ في وحدة التحكم
            event.preventDefault();
        });

        // معالجة أخطاء localStorage
        this.setupStorageErrorHandling();
    }

    // إعداد معالجة أخطاء التخزين
    static setupStorageErrorHandling() {
        const originalSetItem = localStorage.setItem;
        localStorage.setItem = function(key, value) {
            try {
                originalSetItem.call(this, key, value);
            } catch (error) {
                if (error.name === 'QuotaExceededError') {
                    Utils.showNotification('مساحة التخزين ممتلئة. يرجى حذف بعض البيانات القديمة.', 'error');
                    App.handleStorageQuotaExceeded();
                } else {
                    console.error('خطأ في حفظ البيانات:', error);
                    Utils.showNotification('خطأ في حفظ البيانات', 'error');
                }
            }
        };
    }

    // إعداد الأحداث العامة
    static setupGlobalEvents() {
        // معالجة اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (event) => {
            this.handleKeyboardShortcuts(event);
        });

        // معالجة تغيير حالة الاتصال
        window.addEventListener('online', () => {
            Utils.showNotification('تم استعادة الاتصال بالإنترنت', 'success');
        });

        window.addEventListener('offline', () => {
            Utils.showNotification('تم فقدان الاتصال بالإنترنت', 'warning');
        });

        // معالجة إغلاق النافذة
        window.addEventListener('beforeunload', (event) => {
            this.handleBeforeUnload(event);
        });

        // معالجة تغيير حجم النافذة
        window.addEventListener('resize', () => {
            this.handleWindowResize();
        });

        // معالجة النقر خارج النوافذ المنبثقة
        document.addEventListener('click', (event) => {
            this.handleGlobalClick(event);
        });
    }

    // إعداد تحديث شاشة تسجيل الدخول
    static setupLoginScreenUpdates() {
        // مراقبة تحديث بيانات المعمل
        document.addEventListener('labInfoUpdated', (event) => {
            console.log('🔄 App: تم استقبال حدث تحديث بيانات المعمل');
            this.updateLoginScreenFromSettings(event.detail.settings);
        });

        // مراقبة تغييرات localStorage
        window.addEventListener('storage', (event) => {
            if (event.key === 'labSettings') {
                console.log('🔄 App: تم تحديث labSettings في localStorage');
                try {
                    const settings = JSON.parse(event.newValue || '{}');
                    this.updateLoginScreenFromSettings(settings);
                } catch (error) {
                    console.error('خطأ في تحليل إعدادات localStorage:', error);
                }
            }
        });

        // تحديث فوري عند التهيئة
        setTimeout(() => {
            this.updateLoginScreenFromSettings();
        }, 1000);
    }

    // تحديث شاشة تسجيل الدخول من الإعدادات
    static updateLoginScreenFromSettings(settings = null) {
        try {
            console.log('🔄 App: بدء تحديث شاشة تسجيل الدخول...');

            // الحصول على الإعدادات إذا لم تُمرر
            if (!settings) {
                const labSettingsStr = localStorage.getItem('labSettings');
                settings = labSettingsStr ? JSON.parse(labSettingsStr) : {};

                // إذا لم توجد في localStorage، جرب قاعدة البيانات
                if (Object.keys(settings).length === 0 && typeof Database !== 'undefined') {
                    settings = Database.getSettings() || {};
                }
            }

            console.log('⚙️ App: الإعدادات المستخدمة:', settings);

            // التحقق من وجود شاشة تسجيل الدخول
            const loginScreen = document.getElementById('loginScreen');
            if (!loginScreen) {
                console.log('❌ App: شاشة تسجيل الدخول غير موجودة');
                return;
            }

            const isLoginVisible = loginScreen.style.display !== 'none' &&
                                 !loginScreen.classList.contains('hidden');
            console.log('👁️ App: شاشة تسجيل الدخول مرئية:', isLoginVisible);

            // تحديث عنوان النظام
            const systemTitle = document.getElementById('loginSystemTitle');
            if (systemTitle && settings.labName) {
                const newTitle = `نظام إدارة ${settings.labName}`;
                systemTitle.textContent = newTitle;
                console.log('✅ App: تم تحديث عنوان النظام:', newTitle);
            }

            // تحديث الشعار
            const loginLogo = document.getElementById('loginLogo');
            if (loginLogo) {
                if (settings.labLogo) {
                    loginLogo.innerHTML = `<img src="${settings.labLogo}" alt="شعار المعمل" style="width: 100%; height: 100%; object-fit: cover; border-radius: inherit;">`;
                    console.log('✅ App: تم تحديث الشعار بصورة مخصصة');
                } else {
                    loginLogo.innerHTML = '<i class="fas fa-tooth"></i>';
                    console.log('✅ App: تم تعيين الشعار الافتراضي');
                }
            }

            // استدعاء وظيفة التحديث العامة إذا كانت متاحة
            if (typeof window.updateLoginScreen === 'function') {
                window.updateLoginScreen();
                console.log('✅ App: تم استدعاء updateLoginScreen العامة');
            }

            console.log('🎉 App: تم تحديث شاشة تسجيل الدخول بنجاح');

        } catch (error) {
            console.error('❌ App: خطأ في تحديث شاشة تسجيل الدخول:', error);
        }
    }

    // معالجة اختصارات لوحة المفاتيح
    static handleKeyboardShortcuts(event) {
        // Ctrl/Cmd + S: حفظ
        if ((event.ctrlKey || event.metaKey) && event.key === 's') {
            event.preventDefault();
            this.handleSaveShortcut();
        }

        // Ctrl/Cmd + N: جديد
        if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
            event.preventDefault();
            this.handleNewShortcut();
        }

        // Ctrl/Cmd + F: بحث
        if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
            event.preventDefault();
            this.handleSearchShortcut();
        }

        // ESC: إغلاق النوافذ المنبثقة
        if (event.key === 'Escape') {
            this.handleEscapeKey();
        }

        // F5: تحديث
        if (event.key === 'F5') {
            event.preventDefault();
            this.handleRefreshShortcut();
        }
    }

    // معالجة اختصار الحفظ
    static handleSaveShortcut() {
        const currentPage = Navigation.getCurrentPage();
        Utils.showNotification('تم الحفظ', 'success', 2000);
    }

    // معالجة اختصار جديد
    static handleNewShortcut() {
        const currentPage = Navigation.getCurrentPage();
        switch (currentPage) {
            case 'prosthetics':
                // إضافة تركيبة جديدة
                break;
            case 'doctors':
                // إضافة طبيب جديد
                break;
            case 'employees':
                // إضافة موظف جديد
                break;
        }
    }

    // معالجة اختصار البحث
    static handleSearchShortcut() {
        const searchInput = document.querySelector('input[type="search"], .search-input');
        if (searchInput) {
            searchInput.focus();
        }
    }

    // معالجة مفتاح ESC
    static handleEscapeKey() {
        // إغلاق النوافذ المنبثقة
        const modal = document.getElementById('modalOverlay');
        if (modal && modal.style.display !== 'none') {
            Utils.closeModal();
        }

        // إغلاق القوائم المنسدلة
        const dropdowns = document.querySelectorAll('.dropdown.open');
        dropdowns.forEach(dropdown => {
            dropdown.classList.remove('open');
        });
    }

    // معالجة اختصار التحديث
    static handleRefreshShortcut() {
        Navigation.reloadCurrentPage();
        Utils.showNotification('تم تحديث الصفحة', 'info', 2000);
    }

    // معالجة إغلاق النافذة
    static handleBeforeUnload(event) {
        // التحقق من وجود تغييرات غير محفوظة
        const hasUnsavedChanges = this.checkUnsavedChanges();
        
        if (hasUnsavedChanges) {
            event.preventDefault();
            event.returnValue = 'لديك تغييرات غير محفوظة. هل تريد المغادرة؟';
            return event.returnValue;
        }
    }

    // التحقق من التغييرات غير المحفوظة
    static checkUnsavedChanges() {
        // سيتم تنفيذ هذا في كل صفحة حسب الحاجة
        return false;
    }

    // معالجة تغيير حجم النافذة
    static handleWindowResize() {
        // إعادة رسم المخططات إذا لزم الأمر
        const currentPage = Navigation.getCurrentPage();
        if (currentPage === 'dashboard') {
            // إعادة رسم مخططات لوحة التحكم
            setTimeout(() => {
                if (typeof Dashboard !== 'undefined' && typeof Dashboard.updateCharts === 'function') {
                    Dashboard.updateCharts();
                } else if (typeof Dashboard !== 'undefined' && typeof Dashboard.refreshStats === 'function') {
                    Dashboard.refreshStats();
                }
            }, 100);
        }
    }

    // معالجة النقر العام
    static handleGlobalClick(event) {
        // إغلاق القوائم المنسدلة عند النقر خارجها
        const dropdowns = document.querySelectorAll('.dropdown.open');
        dropdowns.forEach(dropdown => {
            if (!dropdown.contains(event.target)) {
                dropdown.classList.remove('open');
            }
        });
    }

    // معالجة امتلاء مساحة التخزين
    static handleStorageQuotaExceeded() {
        Utils.createModal({
            title: 'مساحة التخزين ممتلئة',
            content: `
                <div class="storage-quota-modal">
                    <p>مساحة التخزين المحلي ممتلئة. يرجى اختيار أحد الخيارات التالية:</p>
                    <div class="quota-options">
                        <button class="btn btn-primary" onclick="App.cleanOldData()">
                            <i class="fas fa-broom"></i>
                            تنظيف البيانات القديمة
                        </button>
                        <button class="btn btn-warning" onclick="App.exportAndClear()">
                            <i class="fas fa-download"></i>
                            تصدير البيانات وتنظيف التخزين
                        </button>
                        <button class="btn btn-secondary" onclick="Utils.closeModal()">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </div>
            `,
            size: 'medium'
        });
    }

    // تنظيف البيانات القديمة
    static cleanOldData() {
        try {
            // حذف النشاطات القديمة (الاحتفاظ بآخر 50)
            const activities = Database.getActivities();
            if (activities.length > 50) {
                const recentActivities = activities.slice(0, 50);
                Database.setItem('activities', recentActivities);
            }

            Utils.closeModal();
            Utils.showNotification('تم تنظيف البيانات القديمة بنجاح', 'success');
        } catch (error) {
            console.error('خطأ في تنظيف البيانات:', error);
            Utils.showNotification('فشل في تنظيف البيانات', 'error');
        }
    }

    // تصدير البيانات وتنظيف التخزين
    static exportAndClear() {
        try {
            // تصدير البيانات
            const data = Database.exportAll();
            const filename = `dental_lab_backup_${Utils.formatDate(new Date(), 'YYYY-MM-DD')}.json`;
            Utils.exportToJSON(data, filename);

            // تنظيف البيانات القديمة
            this.cleanOldData();

            Utils.closeModal();
            Utils.showNotification('تم تصدير البيانات وتنظيف التخزين', 'success');
        } catch (error) {
            console.error('خطأ في تصدير البيانات:', error);
            Utils.showNotification('فشل في تصدير البيانات', 'error');
        }
    }

    // تسجيل الأخطاء
    static logError(type, error) {
        try {
            const errorLog = {
                type: type || 'Unknown Error',
                message: error?.message || error?.toString() || 'Unknown error occurred',
                stack: error?.stack || 'No stack trace available',
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                url: window.location.href,
                user: (typeof Auth !== 'undefined' && Auth.getCurrentUser) ? Auth.getCurrentUser()?.id || 'anonymous' : 'anonymous'
            };

            // حفظ في localStorage (مؤقتاً)
            try {
                const errors = JSON.parse(localStorage.getItem('dentalLab_errors') || '[]');
                errors.push(errorLog);

                // الاحتفاظ بآخر 100 خطأ فقط
                if (errors.length > 100) {
                    errors.splice(0, errors.length - 100);
                }

                localStorage.setItem('dentalLab_errors', JSON.stringify(errors));
            } catch (e) {
                console.error('فشل في حفظ سجل الأخطاء:', e);
            }

            console.log('📝 تم تسجيل الخطأ:', errorLog);

        } catch (error) {
            console.error('❌ خطأ في تسجيل الخطأ:', error);
        }
    }

    // معالجة الأخطاء الحرجة
    static handleCriticalError(error) {
        console.error('خطأ حرج في النظام:', error);
        
        document.body.innerHTML = `
            <div class="critical-error">
                <div class="error-container">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h1>خطأ في النظام</h1>
                    <p>حدث خطأ حرج في النظام. يرجى إعادة تحميل الصفحة.</p>
                    <div class="error-actions">
                        <button onclick="window.location.reload()" class="btn btn-primary">
                            <i class="fas fa-sync-alt"></i>
                            إعادة تحميل
                        </button>
                        <button onclick="App.resetSystem()" class="btn btn-danger">
                            <i class="fas fa-redo"></i>
                            إعادة تعيين النظام
                        </button>
                    </div>
                    <details class="error-details">
                        <summary>تفاصيل الخطأ</summary>
                        <pre>${error.stack || error.message}</pre>
                    </details>
                </div>
            </div>
        `;
    }

    // إعادة تعيين النظام
    static resetSystem() {
        if (confirm('هل أنت متأكد من إعادة تعيين النظام؟ سيتم حذف جميع البيانات!')) {
            localStorage.clear();
            window.location.reload();
        }
    }

    // تحديث العنوان
    static updateTitle() {
        const settings = Database.getSettings();
        const labName = settings.labName || 'معمل الأسنان المتخصص';
        document.title = `${labName} - نظام الإدارة`;
    }

    // إضافة التأثيرات الحديثة
    static addModernEffects() {
        // إضافة تأثيرات الحركة للعناصر
        this.addAnimationEffects();

        // إضافة تأثيرات التمرير
        this.addScrollEffects();

        // إضافة تأثيرات الهوفر المتقدمة
        this.addHoverEffects();

        // إضافة تأثيرات التحميل
        this.addLoadingEffects();
    }

    // إضافة تأثيرات الحركة
    static addAnimationEffects() {
        // تأثير الظهور التدريجي للعناصر
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // مراقبة العناصر القابلة للحركة
        document.addEventListener('DOMContentLoaded', () => {
            const animatedElements = document.querySelectorAll('.card, .stat-card, .table-container, .dashboard-card');
            animatedElements.forEach(el => {
                observer.observe(el);
            });
        });
    }

    // إضافة تأثيرات التمرير
    static addScrollEffects() {
        let ticking = false;

        function updateScrollEffects() {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;

            // تأثير المنظور للخلفية
            const loginScreen = document.getElementById('loginScreen');
            if (loginScreen && loginScreen.style.display !== 'none') {
                loginScreen.style.transform = `translate3d(0, ${rate}px, 0)`;
            }

            ticking = false;
        }

        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateScrollEffects);
                ticking = true;
            }
        }

        window.addEventListener('scroll', requestTick);
    }

    // إضافة تأثيرات الهوفر المتقدمة
    static addHoverEffects() {
        // تأثير الإضاءة للبطاقات
        document.addEventListener('mousemove', (e) => {
            const cards = document.querySelectorAll('.card, .stat-card, .dashboard-card');

            cards.forEach(card => {
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                if (x >= 0 && x <= rect.width && y >= 0 && y <= rect.height) {
                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;
                    const deltaX = (x - centerX) / centerX;
                    const deltaY = (y - centerY) / centerY;

                    card.style.transform = `perspective(1000px) rotateX(${deltaY * 5}deg) rotateY(${deltaX * 5}deg) translateZ(10px)`;
                    card.style.boxShadow = `${deltaX * 10}px ${deltaY * 10}px 30px rgba(0,0,0,0.2)`;
                }
            });
        });

        // إعادة تعيين التأثيرات عند مغادرة الماوس
        document.addEventListener('mouseleave', () => {
            const cards = document.querySelectorAll('.card, .stat-card, .dashboard-card');
            cards.forEach(card => {
                card.style.transform = '';
                card.style.boxShadow = '';
            });
        });
    }

    // إضافة تأثيرات التحميل
    static addLoadingEffects() {
        // تأثير التحميل للأزرار
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn') && !e.target.classList.contains('btn-icon')) {
                const button = e.target;
                const originalText = button.innerHTML;

                // إضافة تأثير التحميل
                button.classList.add('loading');
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
                button.disabled = true;

                // إزالة التأثير بعد فترة قصيرة (للتجربة)
                setTimeout(() => {
                    button.classList.remove('loading');
                    button.innerHTML = originalText;
                    button.disabled = false;
                }, 1000);
            }
        });

        // تأثير النبض للإشعارات
        const style = document.createElement('style');
        style.textContent = `
            .notification.show {
                animation: notificationSlide 0.5s ease-out, notificationPulse 2s ease-in-out infinite 0.5s;
            }

            @keyframes notificationSlide {
                from { transform: translateX(-100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes notificationPulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.02); }
            }

            .btn.loading {
                pointer-events: none;
                opacity: 0.7;
            }
        `;
        document.head.appendChild(style);
    }

    // تحميل الإعدادات المحفوظة
    static loadSavedSettings() {
        try {
            console.log('📦 تحميل الإعدادات المحفوظة...');

            const savedData = localStorage.getItem('labSettings');
            if (savedData) {
                const settings = JSON.parse(savedData);
                console.log('✅ تم العثور على إعدادات محفوظة:', settings);

                // تطبيق الإعدادات على شاشة تسجيل الدخول
                this.applySettingsToLoginScreen(settings);

            } else {
                console.log('⚠️ لا توجد إعدادات محفوظة');
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل الإعدادات:', error);
        }
    }

    // تطبيق الإعدادات على شاشة تسجيل الدخول
    static applySettingsToLoginScreen(settings) {
        try {
            // تحديث عنوان النظام في شاشة تسجيل الدخول
            if (settings.labName) {
                const systemTitle = document.getElementById('loginSystemTitle');
                if (systemTitle) {
                    systemTitle.textContent = `نظام إدارة ${settings.labName}`;
                    console.log('✅ تم تطبيق اسم المعمل في شاشة تسجيل الدخول');
                }
            }

            // تحديث الشعار في شاشة تسجيل الدخول
            if (settings.labLogo) {
                const loginLogo = document.getElementById('loginLogo');
                if (loginLogo) {
                    loginLogo.innerHTML = `<img src="${settings.labLogo}" alt="شعار المعمل" style="width: 100%; height: 100%; object-fit: cover; border-radius: inherit;">`;
                    console.log('✅ تم تطبيق الشعار في شاشة تسجيل الدخول');
                }
            }
        } catch (error) {
            console.error('❌ خطأ في تطبيق الإعدادات على شاشة تسجيل الدخول:', error);
        }
    }

    // الحصول على معلومات النظام
    static getSystemInfo() {
        return {
            version: this.version,
            initialized: this.initialized,
            user: Auth.getCurrentUser(),
            page: Navigation.getCurrentPage(),
            timestamp: new Date().toISOString()
        };
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // تأخير صغير للتأكد من تحميل جميع العناصر
    setTimeout(() => {
        App.init();
    }, 100);
});

// تصدير للاستخدام العام
window.App = App;
