// لوحة التحكم الرئيسية
class Dashboard {
    static currentStats = {};
    static refreshInterval = null;

    // عرض لوحة التحكم
    static render() {
        const pageContent = document.getElementById('pageContent');

        if (!pageContent) {
            console.error('❌ عنصر pageContent غير موجود');
            return;
        }

        pageContent.innerHTML = `
            <div class="dashboard-container">
                <!-- عنوان الإدارة المطور -->
                <div class="department-header dashboard">
                    <div class="department-header-content">
                        <div class="department-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <div class="department-info">
                            <h1 class="department-name" data-text="لوحة التحكم">لوحة التحكم</h1>
                            <p class="department-description">مركز التحكم الرئيسي لإدارة ومراقبة جميع عمليات المعمل</p>

                            <!-- 📊 الإحصائيات تحت العنوان مباشرة -->
                            <div class="header-stats">
                                <div class="header-stat-item">
                                    <div class="header-stat-icon"><i class="fas fa-tooth"></i></div>
                                    <div class="header-stat-content">
                                        <span class="header-stat-number" id="dashboard-total-prosthetics">0</span>
                                        <span class="header-stat-label">إجمالي التركيبات</span>
                                    </div>
                                </div>
                                <div class="header-stat-item">
                                    <div class="header-stat-icon"><i class="fas fa-user-md"></i></div>
                                    <div class="header-stat-content">
                                        <span class="header-stat-number" id="dashboard-total-doctors">0</span>
                                        <span class="header-stat-label">الأطباء</span>
                                    </div>
                                </div>
                                <div class="header-stat-item">
                                    <div class="header-stat-icon"><i class="fas fa-users"></i></div>
                                    <div class="header-stat-content">
                                        <span class="header-stat-number" id="dashboard-total-employees">0</span>
                                        <span class="header-stat-label">الموظفين</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات سريعة - شريط أفقي عصري -->
                <div class="quick-info-bar">
                    <div class="quick-info-container">
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-hashtag"></i>
                            </div>
                            <div class="info-content">
                                <span class="info-label">رقم الحالة التالي</span>
                                <span class="info-value" id="nextCaseNumber">-</span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-calendar-day"></i>
                            </div>
                            <div class="info-content">
                                <span class="info-label">تاريخ اليوم</span>
                                <span class="info-value" id="currentDate">-</span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="info-content">
                                <span class="info-label">المستخدم الحالي</span>
                                <span class="info-value" id="currentUserInfo">-</span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="info-content">
                                <span class="info-label">آخر تحديث</span>
                                <span class="info-value" id="lastUpdate">-</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإجراءات السريعة -->
                <div class="quick-actions-section">
                    <h2 class="section-title">
                        <i class="fas fa-bolt"></i>
                        الإجراءات السريعة
                    </h2>
                    <div class="quick-actions-grid">
                        <div class="quick-action-card" onclick="Navigation.navigateTo('prosthetics'); setTimeout(() => { if(typeof Prosthetics !== 'undefined') Prosthetics.showAddModal(); }, 100)">
                            <div class="action-icon primary">
                                <i class="fas fa-tooth"></i>
                            </div>
                            <div class="action-content">
                                <h3>إضافة تركيبة جديدة</h3>
                                <p>إنشاء طلب تركيبة سنية جديد بسرعة</p>
                            </div>
                            <div class="action-arrow">
                                <i class="fas fa-chevron-left"></i>
                            </div>
                        </div>

                        <div class="quick-action-card" onclick="Navigation.navigateTo('doctors'); setTimeout(() => { if(typeof Doctors !== 'undefined') Doctors.showAddModal(); }, 100)">
                            <div class="action-icon success">
                                <i class="fas fa-user-md"></i>
                            </div>
                            <div class="action-content">
                                <h3>إضافة طبيب جديد</h3>
                                <p>تسجيل طبيب أسنان جديد في النظام</p>
                            </div>
                            <div class="action-arrow">
                                <i class="fas fa-chevron-left"></i>
                            </div>
                        </div>

                        <div class="quick-action-card" onclick="Dashboard.exportData()">
                            <div class="action-icon warning">
                                <i class="fas fa-download"></i>
                            </div>
                            <div class="action-content">
                                <h3>تصدير البيانات</h3>
                                <p>تصدير جميع بيانات النظام كنسخة احتياطية</p>
                            </div>
                            <div class="action-arrow">
                                <i class="fas fa-chevron-left"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات الرئيسية -->
                <div class="dashboard-stats-grid">
                    <div class="stat-card" id="totalProstheticsCard">
                        <div class="stat-header">
                            <div class="stat-icon primary">
                                <i class="fas fa-tooth"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="totalProsthetics">0</div>
                        <div class="stat-label">إجمالي التركيبات</div>
                    </div>

                    <div class="stat-card" id="totalDoctorsCard">
                        <div class="stat-header">
                            <div class="stat-icon success">
                                <i class="fas fa-user-md"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="totalDoctors">0</div>
                        <div class="stat-label">إجمالي الأطباء</div>
                    </div>

                    <div class="stat-card" id="totalEmployeesCard">
                        <div class="stat-header">
                            <div class="stat-icon secondary">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="stat-value" id="totalEmployees">0</div>
                        <div class="stat-label">إجمالي الموظفين</div>
                    </div>
                </div>



                <!-- النشاطات الأخيرة -->
                <div class="dashboard-card activities-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-history"></i>
                            النشاطات الأخيرة
                        </h3>
                        <div class="card-actions">
                            <button class="btn btn-secondary btn-sm" onclick="Dashboard.viewAllActivities()">
                                <i class="fas fa-list"></i>
                                عرض الكل
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div id="recentActivities" class="activities-list">
                            <!-- سيتم تحميل النشاطات هنا -->
                        </div>
                    </div>
                </div>
            </div>
        `;

        // تحميل البيانات
        this.loadDashboardData();
        this.setupAutoRefresh();
    }

    // تحميل بيانات لوحة التحكم
    static loadDashboardData() {
        try {
            this.updateStats();
            this.updateQuickInfo();
            this.updateRecentActivities();
            console.log('✅ تم تحميل بيانات لوحة التحكم بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تحميل بيانات لوحة التحكم:', error);
        }
    }

    // تحديث الإحصائيات
    static updateStats() {
        try {
            const stats = Database.getQuickStats();
            this.currentStats = stats;

            // تحديث القيم في البطاقات (مع فحص الوجود)
            const totalProstheticsEl = document.getElementById('totalProsthetics');
            const totalDoctorsEl = document.getElementById('totalDoctors');
            const totalEmployeesEl = document.getElementById('totalEmployees');

            if (totalProstheticsEl) totalProstheticsEl.textContent = Utils.formatNumber(stats.totalProsthetics);
            if (totalDoctorsEl) totalDoctorsEl.textContent = Utils.formatNumber(stats.totalDoctors);
            if (totalEmployeesEl) totalEmployeesEl.textContent = Utils.formatNumber(stats.totalEmployees);

            // تحديث إحصائيات العنوان
            const dashboardTotalProsthetics = document.getElementById('dashboard-total-prosthetics');
            const dashboardTotalDoctors = document.getElementById('dashboard-total-doctors');
            const dashboardTotalEmployees = document.getElementById('dashboard-total-employees');

            if (dashboardTotalProsthetics) dashboardTotalProsthetics.textContent = stats.totalProsthetics;
            if (dashboardTotalDoctors) dashboardTotalDoctors.textContent = stats.totalDoctors;
            if (dashboardTotalEmployees) dashboardTotalEmployees.textContent = stats.totalEmployees;

            // إضافة تأثيرات بصرية
            this.animateStatCards();

            console.log('✅ تم تحديث الإحصائيات بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تحديث الإحصائيات:', error);
        }
    }

    // تحديث المعلومات السريعة
    static updateQuickInfo() {
        try {
            const currentUser = Auth.getCurrentUser();
            const nextCaseNumber = Utils.generateCaseNumber();
            const currentDate = Utils.formatDate(new Date(), 'DD/MM/YYYY');
            const lastUpdate = Utils.formatDate(new Date(), 'DD/MM/YYYY HH:mm');

            // فحص وجود العناصر قبل التحديث
            const nextCaseNumberEl = document.getElementById('nextCaseNumber');
            const currentDateEl = document.getElementById('currentDate');
            const currentUserInfoEl = document.getElementById('currentUserInfo');
            const lastUpdateEl = document.getElementById('lastUpdate');

            if (nextCaseNumberEl) nextCaseNumberEl.textContent = nextCaseNumber;
            if (currentDateEl) currentDateEl.textContent = currentDate;
            if (currentUserInfoEl) currentUserInfoEl.textContent = currentUser ? currentUser.name : 'غير محدد';
            if (lastUpdateEl) lastUpdateEl.textContent = lastUpdate;

            console.log('✅ تم تحديث المعلومات السريعة');
        } catch (error) {
            console.warn('⚠️ خطأ في تحديث المعلومات السريعة:', error);
        }
    }

    // تحديث النشاطات الأخيرة
    static updateRecentActivities() {
        try {
            const activities = Database.getActivities().slice(0, 10); // آخر 10 نشاطات
            if (Utils.updateRecentActivities) {
                Utils.updateRecentActivities(activities);
            }
            console.log('✅ تم تحديث النشاطات الأخيرة');
        } catch (error) {
            console.warn('⚠️ خطأ في تحديث النشاطات الأخيرة:', error);
        }
    }



    // تحريك بطاقات الإحصائيات
    static animateStatCards() {
        try {
            const cards = document.querySelectorAll('.stat-card');
            if (cards.length > 0) {
                cards.forEach((card, index) => {
                    setTimeout(() => {
                        if (card && card.style) {
                            card.style.animation = 'statCardPulse 0.6s ease';
                        }
                    }, index * 100);
                });
            }
        } catch (error) {
            console.warn('⚠️ خطأ في تحريك بطاقات الإحصائيات:', error);
        }
    }

    // تحديث الإحصائيات
    static refreshStats() {
        try {
            Utils.showNotification('جاري تحديث البيانات...', 'info', 2000);

            setTimeout(() => {
                try {
                    this.loadDashboardData();
                    Utils.showNotification('تم تحديث البيانات بنجاح', 'success');
                } catch (error) {
                    console.error('❌ خطأ في تحديث البيانات:', error);
                    Utils.showNotification('حدث خطأ في تحديث البيانات', 'error');
                }
            }, 1000);
        } catch (error) {
            console.error('❌ خطأ في refreshStats:', error);
        }
    }

    // تحديث المخططات
    static updateCharts() {
        try {
            console.log('🔄 تحديث مخططات لوحة التحكم...');

            // تحديث الإحصائيات
            this.refreshStats();

            // تحديث المخططات إذا كانت موجودة
            this.updateQuickStats();

            console.log('✅ تم تحديث المخططات بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تحديث المخططات:', error);
        }
    }

    // تحديث الإحصائيات السريعة
    static updateQuickStats() {
        try {
            const prosthetics = Database.getProsthetics() || [];
            const doctors = Database.getDoctors() || [];
            const employees = Database.getEmployees() || [];

            // تحديث العدادات في العنوان
            const totalProstheticsElement = document.getElementById('dashboard-total-prosthetics');
            const totalDoctorsElement = document.getElementById('dashboard-total-doctors');
            const totalEmployeesElement = document.getElementById('dashboard-total-employees');

            if (totalProstheticsElement) totalProstheticsElement.textContent = prosthetics.length;
            if (totalDoctorsElement) totalDoctorsElement.textContent = doctors.length;
            if (totalEmployeesElement) totalEmployeesElement.textContent = employees.length;

            console.log('✅ تم تحديث الإحصائيات السريعة');
        } catch (error) {
            console.error('❌ خطأ في تحديث الإحصائيات السريعة:', error);
        }
    }

    // إعداد التحديث التلقائي
    static setupAutoRefresh() {
        // تحديث كل 5 دقائق
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        this.refreshInterval = setInterval(() => {
            this.loadDashboardData();
        }, 5 * 60 * 1000);
    }

    // إيقاف التحديث التلقائي
    static stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    // الإجراءات السريعة
    static addNewProsthetic() {
        Navigation.loadPage('prosthetics');
        // سيتم تنفيذ إضافة تركيبة جديدة في صفحة التركيبات
    }

    static addNewDoctor() {
        Navigation.loadPage('doctors');
        // سيتم تنفيذ إضافة طبيب جديد في صفحة الأطباء
    }

    static viewReports() {
        Navigation.loadPage('reports');
    }

    static exportData() {
        Navigation.loadPage('backup');
    }

    static viewAllActivities() {
        const activities = Database.getActivities();
        
        Utils.createModal({
            title: 'جميع النشاطات',
            content: `
                <div class="activities-modal">
                    <div class="activities-list">
                        ${activities.map(activity => `
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-${activity.icon}"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-text">${activity.text}</div>
                                    <div class="activity-time">${Utils.formatDate(activity.time, 'DD/MM/YYYY HH:mm')}</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                    <div class="modal-actions">
                        <button class="btn btn-secondary" onclick="Utils.closeModal()">إغلاق</button>
                    </div>
                </div>
            `,
            size: 'large'
        });
    }

    // تنظيف الموارد
    static cleanup() {
        this.stopAutoRefresh();
    }

    // تحديث البيانات
    static refreshData() {
        this.loadDashboardData();
        Utils.showNotification('تم تحديث البيانات بنجاح', 'success');
    }

    // عرض نافذة البحث السريع
    static showQuickSearch() {
        Utils.createModal({
            title: 'البحث السريع',
            content: `
                <div class="quick-search-modal">
                    <div class="search-input-container">
                        <div class="search-box-large">
                            <i class="fas fa-search"></i>
                            <input type="text" id="quickSearchInput" placeholder="ابحث في التركيبات، الأطباء، الموظفين..." autocomplete="off">
                        </div>
                    </div>

                    <div class="search-filters">
                        <div class="filter-chips">
                            <button class="filter-chip active" data-type="all">
                                <i class="fas fa-globe"></i>
                                الكل
                            </button>
                            <button class="filter-chip" data-type="prosthetics">
                                <i class="fas fa-tooth"></i>
                                التركيبات
                            </button>
                            <button class="filter-chip" data-type="doctors">
                                <i class="fas fa-user-md"></i>
                                الأطباء
                            </button>
                            <button class="filter-chip" data-type="employees">
                                <i class="fas fa-users"></i>
                                الموظفين
                            </button>
                            <button class="filter-chip" data-type="external-labs">
                                <i class="fas fa-building"></i>
                                المعامل الخارجية
                            </button>
                        </div>
                    </div>

                    <div class="search-results" id="quickSearchResults">
                        <div class="search-placeholder">
                            <i class="fas fa-search"></i>
                            <p>ابدأ بكتابة كلمة البحث...</p>
                        </div>
                    </div>
                </div>
            `,
            size: 'large'
        });

        this.setupQuickSearch();
    }

    // إعداد البحث السريع
    static setupQuickSearch() {
        const searchInput = document.getElementById('quickSearchInput');
        const resultsContainer = document.getElementById('quickSearchResults');
        const filterChips = document.querySelectorAll('.filter-chip');

        let currentFilter = 'all';
        let searchTimeout;

        // معالجة تغيير الفلاتر
        filterChips.forEach(chip => {
            chip.addEventListener('click', () => {
                filterChips.forEach(c => c.classList.remove('active'));
                chip.classList.add('active');
                currentFilter = chip.getAttribute('data-type');

                if (searchInput.value.trim()) {
                    this.performQuickSearch(searchInput.value.trim(), currentFilter, resultsContainer);
                }
            });
        });

        // معالجة البحث
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            const query = e.target.value.trim();

            if (query.length < 2) {
                resultsContainer.innerHTML = `
                    <div class="search-placeholder">
                        <i class="fas fa-search"></i>
                        <p>ابدأ بكتابة كلمة البحث...</p>
                    </div>
                `;
                return;
            }

            searchTimeout = setTimeout(() => {
                this.performQuickSearch(query, currentFilter, resultsContainer);
            }, 300);
        });

        // التركيز على حقل البحث
        searchInput.focus();
    }

    // تنفيذ البحث السريع
    static performQuickSearch(query, filter, resultsContainer) {
        resultsContainer.innerHTML = `
            <div class="search-loading">
                <i class="fas fa-spinner fa-spin"></i>
                <p>جاري البحث...</p>
            </div>
        `;

        setTimeout(() => {
            const results = this.searchInData(query, filter);
            this.displaySearchResults(results, resultsContainer);
        }, 500);
    }

    // البحث في البيانات
    static searchInData(query, filter) {
        const results = [];
        const searchTerm = query.toLowerCase();

        // البحث في التركيبات
        if (filter === 'all' || filter === 'prosthetics') {
            const prosthetics = Database.getProsthetics();
            prosthetics.forEach(item => {
                if (item.caseNumber.toLowerCase().includes(searchTerm) ||
                    item.patientName.toLowerCase().includes(searchTerm) ||
                    item.doctorName.toLowerCase().includes(searchTerm) ||
                    item.prostheticType.toLowerCase().includes(searchTerm)) {
                    results.push({
                        type: 'prosthetic',
                        icon: 'tooth',
                        title: `${item.caseNumber} - ${item.patientName}`,
                        subtitle: `${item.prostheticType} - ${item.doctorName}`,
                        status: item.status,
                        id: item.id,
                        action: () => {
                            Utils.closeModal();
                            Navigation.navigateTo('prosthetics');
                        }
                    });
                }
            });
        }

        // البحث في الأطباء
        if (filter === 'all' || filter === 'doctors') {
            const doctors = Database.getDoctors();
            doctors.forEach(item => {
                if (item.name.toLowerCase().includes(searchTerm) ||
                    item.phone.includes(searchTerm) ||
                    item.specialization.toLowerCase().includes(searchTerm)) {
                    results.push({
                        type: 'doctor',
                        icon: 'user-md',
                        title: item.name,
                        subtitle: `${item.specialization} - ${item.phone}`,
                        status: 'active',
                        id: item.id,
                        action: () => {
                            Utils.closeModal();
                            Navigation.navigateTo('doctors');
                        }
                    });
                }
            });
        }

        return results;
    }

    // عرض نتائج البحث
    static displaySearchResults(results, container) {
        if (results.length === 0) {
            container.innerHTML = `
                <div class="search-no-results">
                    <i class="fas fa-search-minus"></i>
                    <p>لم يتم العثور على نتائج</p>
                </div>
            `;
            return;
        }

        const resultsHTML = results.map(result => `
            <div class="search-result-item" onclick="(${result.action.toString()})()">
                <div class="result-icon">
                    <i class="fas fa-${result.icon}"></i>
                </div>
                <div class="result-content">
                    <h4>${result.title}</h4>
                    <p>${result.subtitle}</p>
                </div>
                <div class="result-status">
                    <span class="status-badge status-${result.status}">${this.getStatusDisplayName(result.status)}</span>
                </div>
                <div class="result-arrow">
                    <i class="fas fa-chevron-left"></i>
                </div>
            </div>
        `).join('');

        container.innerHTML = `
            <div class="search-results-header">
                <h3>نتائج البحث (${results.length})</h3>
            </div>
            <div class="search-results-list">
                ${resultsHTML}
            </div>
        `;
    }

    // الحصول على اسم عرض الحالة
    static getStatusDisplayName(status) {
        const statusNames = {
            pending: 'معلق',
            progress: 'قيد التنفيذ',
            completed: 'مكتمل',
            delivered: 'تم التسليم',
            active: 'نشط'
        };
        return statusNames[status] || status;
    }

    // تصدير البيانات
    static exportData() {
        try {
            // جمع جميع البيانات
            const allData = {
                prosthetics: Database.getProsthetics(),
                doctors: Database.getDoctors(),
                employees: Database.getEmployees(),
                externalLabs: Database.getItem('externalLabs') || [],
                externalWorks: Database.getItem('externalWorks') || [],
                accounts: Database.getItem('accounts') || [],
                expenses: Database.getItem('expenses') || [],
                payments: Database.getItem('payments') || [],
                activities: Database.getActivities(),
                settings: Database.getSettings(),
                exportDate: new Date().toISOString(),
                version: App.version || '1.0.0'
            };

            // إنشاء اسم الملف
            const now = new Date();
            const dateStr = Utils.formatDate(now, 'YYYY-MM-DD');
            const timeStr = Utils.formatDate(now, 'HH-mm');
            const filename = `dental_lab_backup_${dateStr}_${timeStr}.json`;

            // تصدير البيانات
            Utils.exportToJSON(allData, filename);

            // إضافة نشاط
            Database.addActivity({
                type: 'data_exported',
                text: `تم تصدير جميع بيانات النظام`,
                icon: 'download',
                userId: Auth.getCurrentUser()?.id
            });

            // عرض رسالة نجاح مع تفاصيل
            Utils.createModal({
                title: 'تم تصدير البيانات بنجاح',
                content: `
                    <div class="export-success-modal">
                        <div class="success-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h3>تم تصدير البيانات بنجاح!</h3>
                        <div class="export-details">
                            <div class="detail-item">
                                <span class="label">اسم الملف:</span>
                                <span class="value">${filename}</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">تاريخ التصدير:</span>
                                <span class="value">${Utils.formatDate(now, 'DD/MM/YYYY HH:mm')}</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">عدد التركيبات:</span>
                                <span class="value">${allData.prosthetics.length}</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">عدد الأطباء:</span>
                                <span class="value">${allData.doctors.length}</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">عدد الموظفين:</span>
                                <span class="value">${allData.employees.length}</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">عدد المعامل الخارجية:</span>
                                <span class="value">${allData.externalLabs.length}</span>
                            </div>
                        </div>
                        <div class="export-note">
                            <i class="fas fa-info-circle"></i>
                            <p>تم حفظ الملف في مجلد التحميلات الخاص بك. يمكنك استخدام هذا الملف لاستعادة البيانات لاحقاً.</p>
                        </div>
                        <div class="modal-actions">
                            <button class="btn btn-primary" onclick="Utils.closeModal()">
                                <i class="fas fa-check"></i>
                                تم
                            </button>
                        </div>
                    </div>
                `,
                size: 'medium'
            });

            Utils.showNotification('تم تصدير البيانات بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في تصدير البيانات:', error);
            Utils.showNotification('فشل في تصدير البيانات', 'error');

            Utils.createModal({
                title: 'خطأ في تصدير البيانات',
                content: `
                    <div class="error-modal">
                        <div class="error-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h3>حدث خطأ أثناء تصدير البيانات</h3>
                        <p>يرجى المحاولة مرة أخرى. إذا استمر الخطأ، تأكد من أن المتصفح يدعم تحميل الملفات.</p>
                        <div class="modal-actions">
                            <button class="btn btn-primary" onclick="Dashboard.exportData(); Utils.closeModal()">
                                <i class="fas fa-retry"></i>
                                إعادة المحاولة
                            </button>
                            <button class="btn btn-secondary" onclick="Utils.closeModal()">
                                <i class="fas fa-times"></i>
                                إغلاق
                            </button>
                        </div>
                    </div>
                `,
                size: 'medium'
            });
        }
    }
}
