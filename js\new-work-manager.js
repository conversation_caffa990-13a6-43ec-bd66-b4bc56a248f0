class NewWorkManager {
    static selectedTeeth = [];

    static showModal() {
        console.log('🔄 NewWorkManager.showModal() called');
        
        let modalOverlay = document.getElementById('modalOverlay');
        let modalContent = document.getElementById('modalContent');
        
        console.log('Modal elements:', { modalOverlay, modalContent });
        
        if (!modalOverlay || !modalContent) {
            console.error('❌ Modal elements not found!');
            return;
        }
        
        // إضافة المحتوى أولاً
        modalContent.innerHTML = NewWorkManager.getFormHTML();
        console.log('✅ Modal content added');
        
        // عرض النافذة
        modalOverlay.style.display = 'flex';
        modalOverlay.classList.add('show');
        console.log('✅ Modal displayed');
        
        // إضافة مستمع النقر
        modalOverlay.onclick = function(e) {
            if (e.target === modalOverlay) {
                NewWorkManager.closeModal();
            }
        };
        
        // تهيئة البيانات
        setTimeout(() => {
            console.log('🔄 Initializing form data...');
            try {
                NewWorkManager.fillDoctorsList();
                NewWorkManager.selectedTeeth = [];
                NewWorkManager.renderTeethDiagram();
                NewWorkManager.updateTotals();
                console.log('✅ Form initialized successfully');
            } catch (error) {
                console.error('❌ Error initializing form:', error);
            }
        }, 100);
    }

    static closeModal() {
        console.log('🔄 NewWorkManager.closeModal() called');
        
        let modalOverlay = document.getElementById('modalOverlay');
        let modalContent = document.getElementById('modalContent');
        
        if (modalOverlay) {
            modalOverlay.style.display = 'none';
            modalOverlay.classList.remove('show');
            console.log('✅ Modal hidden');
        }
        
        if (modalContent) {
            modalContent.innerHTML = '';
            console.log('✅ Modal content cleared');
        }
        
        // إعادة تعيين البيانات
        NewWorkManager.selectedTeeth = [];
    }

    static generateCaseNumber() {
        return 'C' + Math.floor(1000 + Math.random() * 9000);
    }

    static handleSubmit(event) {
        event.preventDefault();
        const form = document.getElementById('newWorkForm');
        const data = {
            caseNumber: form.caseNumber.value,
            doctorName: form.doctorName.value,
            patientName: form.patientName.value,
            prostheticType: form.prostheticType.value,
            color: form.color.value,
            unitPrice: parseFloat(form.unitPrice.value) || 0,
            selectedTeeth: NewWorkManager.selectedTeeth || [],
            discount: parseFloat(form.discount.value) || 0,
            totalPrice: parseFloat(document.getElementById('totalPrice').textContent) || 0,
            finalPrice: parseFloat(document.getElementById('finalPrice').textContent) || 0,
            createdAt: new Date().toISOString(),
            status: 'pending'
        };
        if (!data.doctorName || !data.patientName || !data.prostheticType) {
            alert('يرجى تعبئة جميع الحقول المطلوبة');
            return;
        }
        if (!data.selectedTeeth.length) {
            alert('يرجى اختيار سن واحد على الأقل من المخطط');
            return;
        }
        if (typeof Database !== 'undefined' && Database.addProsthetic) {
            Database.addProsthetic({
                id: (typeof Utils !== 'undefined' && Utils.generateId) ? Utils.generateId() : Date.now().toString(),
                ...data
            });
            if (typeof Prosthetics !== 'undefined') {
                Prosthetics.loadProstheticsData();
                Prosthetics.updateStats();
            }
            NewWorkManager.closeModal();
            if (typeof Utils !== 'undefined' && Utils.showNotification) {
                Utils.showNotification('تم حفظ العمل الجديد بنجاح', 'success');
            }
        } else {
            alert('حدث خطأ أثناء الحفظ!');
        }
    }

    static clearForm() {
        const form = document.getElementById('newWorkForm');
        if (!form) return;
        form.reset();
        document.getElementById('caseNumber').value = NewWorkManager.generateCaseNumber();
        NewWorkManager.selectedTeeth = [];
        NewWorkManager.renderTeethDiagram();
        NewWorkManager.updateTotals();
    }

    static renderTeethDiagram() {
        const container = document.getElementById('teethDiagram');
        if (!container) return;
        
        let html = `
            <div class="teeth-diagram-container">
                <!-- الفك العلوي -->
                <div class="teeth-arch upper-arch">
                    <div class="arch-label">
                        <i class="fas fa-arrow-up"></i>
                        الفك العلوي
                    </div>
                    <div class="teeth-row horizontal-layout">
                        <!-- الجهة اليمنى -->
                        <div class="teeth-side right-side">
                            <div class="side-label">يمين</div>
                            <div class="teeth-grid">`;
        
        // الأسنان اليمنى العلوية (1-8)
        for (let i = 1; i <= 8; i++) {
            html += `
                <div class="tooth-container" data-jaw="upper" data-side="right" data-num="${i}" 
                     onclick="NewWorkManager.toggleTooth('upper','right',${i})" 
                     title="الفك العلوي - يمين - ${i}">
                    <div class="tooth-box">
                        <div class="tooth-icon">
                            <i class="fas fa-tooth"></i>
                        </div>
                        <div class="tooth-number">${i}</div>
                    </div>
                </div>`;
        }
        
        html += `
                            </div>
                        </div>
                        
                        <!-- الخط الفاصل الأزرق -->
                        <div class="center-separator">
                            <div class="separator-line-vertical"></div>
                        </div>
                        
                        <!-- الجهة اليسرى -->
                        <div class="teeth-side left-side">
                            <div class="side-label">يسار</div>
                            <div class="teeth-grid">`;
        
        // الأسنان اليسرى العلوية (1-8) - نفس ترتيب اليمين
        for (let i = 1; i <= 8; i++) {
            html += `
                <div class="tooth-container" data-jaw="upper" data-side="left" data-num="${i}" 
                     onclick="NewWorkManager.toggleTooth('upper','left',${i})" 
                     title="الفك العلوي - يسار - ${i}">
                    <div class="tooth-box">
                        <div class="tooth-icon">
                            <i class="fas fa-tooth"></i>
                        </div>
                        <div class="tooth-number">${i}</div>
                    </div>
                </div>`;
        }
        
        html += `       </div>
                        </div>
                    </div>
                </div>
                
                <!-- فاصل أفقي بين الفكين -->
                <div class="jaw-separator">
                    <div class="separator-line-horizontal"></div>
                    <div class="jaw-separator-label">
                        <i class="fas fa-arrows-alt-v"></i>
                    </div>
                </div>
                
                <!-- الفك السفلي -->
                <div class="teeth-arch lower-arch">
                    <div class="arch-label">
                        <i class="fas fa-arrow-down"></i>
                        الفك السفلي
                    </div>
                    <div class="teeth-row horizontal-layout">
                        <!-- الجهة اليمنى -->
                        <div class="teeth-side right-side">
                            <div class="side-label">يمين</div>
                            <div class="teeth-grid">`;
        
        // الأسنان اليمنى السفلية (1-8)
        for (let i = 1; i <= 8; i++) {
            html += `
                <div class="tooth-container" data-jaw="lower" data-side="right" data-num="${i}" 
                     onclick="NewWorkManager.toggleTooth('lower','right',${i})" 
                     title="الفك السفلي - يمين - ${i}">
                    <div class="tooth-box">
                        <div class="tooth-icon">
                            <i class="fas fa-tooth"></i>
                        </div>
                        <div class="tooth-number">${i}</div>
                    </div>
                </div>`;
        }
        
        html += `
                            </div>
                        </div>
                        
                        <!-- الخط الفاصل الأزرق -->
                        <div class="center-separator">
                            <div class="separator-line-vertical"></div>
                        </div>
                        
                        <!-- الجهة اليسرى -->
                        <div class="teeth-side left-side">
                            <div class="side-label">يسار</div>
                            <div class="teeth-grid">`;
        
        // الأسنان اليسرى السفلية (1-8) - نفس ترتيب اليمين
        for (let i = 1; i <= 8; i++) {
            html += `
                <div class="tooth-container" data-jaw="lower" data-side="left" data-num="${i}" 
                     onclick="NewWorkManager.toggleTooth('lower','left',${i})" 
                     title="الفك السفلي - يسار - ${i}">
                    <div class="tooth-box">
                        <div class="tooth-icon">
                            <i class="fas fa-tooth"></i>
                        </div>
                        <div class="tooth-number">${i}</div>
                    </div>
                </div>`;
        }
        
        html += `       </div>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات الأسنان المختارة -->
                <div class="selected-teeth-info">
                    <div class="info-header">
                        <i class="fas fa-check-circle"></i>
                        <span class="info-title">الأسنان المختارة</span>
                    </div>
                    <div id="selectedTeethList" class="selected-list">
                        <span class="no-selection">لم يتم اختيار أي أسنان</span>
                    </div>
                </div>
            </div>
        `;
        
        container.innerHTML = html;
        
        // تحديث الأسنان المختارة
        NewWorkManager.selectedTeeth.forEach(t => {
            const selector = `.tooth-container[data-jaw='${t.jaw}'][data-side='${t.side}'][data-num='${t.num}']`;
            const el = container.querySelector(selector);
            if (el) el.classList.add('selected');
        });
        
        // تحديث قائمة الأسنان المختارة
        NewWorkManager.updateSelectedTeethList();
    }

    static toggleTooth(jaw, side, num) {
        const idx = NewWorkManager.selectedTeeth.findIndex(t => t.jaw === jaw && t.side === side && t.num === num);
        if (idx >= 0) {
            NewWorkManager.selectedTeeth.splice(idx, 1);
        } else {
            NewWorkManager.selectedTeeth.push({ jaw, side, num });
        }
        NewWorkManager.renderTeethDiagram();
        NewWorkManager.updateTotals();
    }

    static updateSelectedTeethList() {
        const listContainer = document.getElementById('selectedTeethList');
        if (!listContainer) return;
        
        if (NewWorkManager.selectedTeeth.length === 0) {
            listContainer.innerHTML = '<span class="no-selection">لم يتم اختيار أي أسنان</span>';
            return;
        }
        
        const teethList = NewWorkManager.selectedTeeth.map(tooth => {
            const jawText = tooth.jaw === 'upper' ? 'علوي' : 'سفلي';
            const sideText = tooth.side === 'right' ? 'يمين' : 'يسار';
            return `<span class="selected-tooth-item">${jawText} ${sideText} ${tooth.num}</span>`;
        }).join('');
        
        listContainer.innerHTML = teethList;
    }

    static updateTotals() {
        const unitPrice = parseFloat(document.getElementById('unitPrice').value) || 0;
        const count = NewWorkManager.selectedTeeth.length;
        const discount = parseFloat(document.getElementById('discount').value) || 0;
        document.getElementById('selectedTeethCount').textContent = count;
        document.getElementById('unitPriceDisplay').textContent = unitPrice;
        const total = unitPrice * count;
        document.getElementById('totalPrice').textContent = total;
        const final = Math.max(0, total - discount);
        document.getElementById('finalPrice').textContent = final;
    }

    static fillDoctorsList() {
        let doctors = [];
        if (typeof Database !== 'undefined' && Database.getDoctors) {
            doctors = Database.getDoctors() || [];
        }
        const select = document.getElementById('doctorName');
        if (!select) return;
        select.innerHTML = '<option value="">اختر الطبيب</option>' +
            doctors.map(d => `<option value="${d.name}">${d.name}</option>`).join('');
    }

    static showProstheticsListModal() {
        if (typeof Prosthetics !== 'undefined' && Prosthetics.showProstheticsListModal) {
            Prosthetics.showProstheticsListModal();
        } else {
            alert('وظيفة قائمة التركيبات غير متاحة حالياً');
        }
    }

    static getFormHTML() {
        const prostheticsOptions = {
            'بورسلين': [
                'بورسلين جى سرام',
                'بورسلين فيتا',
                'بورسلين فيس',
                'بورسلين شركه'
            ],
            'تقويم': [
                'Space maintainer',
                'برشل',
                'post',
                'lingual arch',
                'Retainer فكيم',
                'NIGHT GUARD',
                'night guard',
                'Pand',
                'Nance applaince',
                'crown & loop',
                'Retainer hard',
                'سلك Retainer'
            ],
            'زيركون': [
                'zircon copy',
                'ZIRCON FULLANTOMY',
                'implant zircon',
                'Zircon onlay'
            ],
            'معدن': [
                'صب مقاس',
                'vitallium'
            ],
            'أخرى': [
                'مؤقت',
                'بروفا شمع'
            ]
        };
        const colorOptions = [
            'A1', 'A2', 'A3', 'A3.5', 'A4',
            'B1', 'B2', 'B3', 'B4',
            'C1', 'C2', 'C3', 'C4',
            'D2', 'D3', 'D4',
            'BL1', 'BL2', 'BL3', 'BL4',
            'OM1', 'OM2', 'OM3', 'OM4',
            'أخرى'
        ];
        let prostheticsSelect = '';
        for (const cat in prostheticsOptions) {
            prostheticsSelect += `<optgroup label="${cat}">`;
            prostheticsOptions[cat].forEach(item => {
                prostheticsSelect += `<option value="${item}">${item}</option>`;
            });
            prostheticsSelect += '</optgroup>';
        }
        let colorSelect = colorOptions.map(c => `<option value="${c}">${c}</option>`).join('');
        return `
        <div class="new-work-modal">
            <div class="modal-header">
                <h2><i class="fas fa-plus-circle"></i> تسجيل عمل جديد</h2>
                <button type="button" class="close-modal-btn" onclick="NewWorkManager.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="newWorkForm" class="new-work-form" onsubmit="NewWorkManager.handleSubmit(event)">
                <!-- معلومات أساسية -->
                <div class="form-section">
                    <h3><i class="fas fa-info-circle"></i> المعلومات الأساسية</h3>
                    <div class="form-grid">
                        <div class="form-row">
                            <label><i class="fas fa-hashtag"></i> رقم الحالة</label>
                            <input type="text" id="caseNumber" name="caseNumber" readonly value="${NewWorkManager.generateCaseNumber()}" class="readonly-input">
                        </div>
                        <div class="form-row">
                            <label><i class="fas fa-user-md"></i> اسم الطبيب *</label>
                            <select id="doctorName" name="doctorName" required>
                                <option value="">اختر الطبيب</option>
                            </select>
                        </div>
                        <div class="form-row">
                            <label><i class="fas fa-user"></i> اسم المريض *</label>
                            <input type="text" id="patientName" name="patientName" required placeholder="أدخل اسم المريض">
                        </div>
                    </div>
                </div>

                <!-- تفاصيل التركيبة -->
                <div class="form-section">
                    <h3><i class="fas fa-tooth"></i> تفاصيل التركيبة</h3>
                    <div class="form-grid">
                        <div class="form-row">
                            <label><i class="fas fa-cogs"></i> نوع التركيبة *</label>
                            <select id="prostheticType" name="prostheticType" required>
                                <option value="">اختر التركيبة</option>
                                ${prostheticsSelect}
                            </select>
                        </div>
                        <div class="form-row">
                            <label><i class="fas fa-palette"></i> اللون</label>
                            <select id="color" name="color">
                                <option value="">اختر اللون</option>
                                ${colorSelect}
                            </select>
                        </div>
                        <div class="form-row">
                            <label><i class="fas fa-money-bill-wave"></i> سعر السن (ريال) *</label>
                            <input type="number" id="unitPrice" name="unitPrice" min="0" step="0.01" value="0" required oninput="NewWorkManager.updateTotals()" placeholder="0.00">
                        </div>
                    </div>
                </div>

                <!-- مخطط الأسنان -->
                <div class="form-section">
                    <h3><i class="fas fa-teeth"></i> مخطط الأسنان</h3>
                    <div class="teeth-diagram-section">
                        <div id="teethDiagram" class="teeth-diagram"></div>
                    </div>
                </div>

                <!-- ملخص التكلفة -->
                <div class="form-section">
                    <h3><i class="fas fa-calculator"></i> ملخص التكلفة</h3>
                    <div class="cost-summary">
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="summary-label">عدد الأسنان:</span>
                                <span class="summary-value" id="selectedTeethCount">0</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">سعر الوحدة:</span>
                                <span class="summary-value"><span id="unitPriceDisplay">0</span> ريال</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">السعر الإجمالي:</span>
                                <span class="summary-value total-price"><span id="totalPrice">0</span> ريال</span>
                            </div>
                        </div>
                        
                        <div class="discount-section">
                            <label><i class="fas fa-percentage"></i> مبلغ الخصم (ريال)</label>
                            <input type="number" id="discount" name="discount" min="0" step="0.01" value="0" oninput="NewWorkManager.updateTotals()" placeholder="0.00">
                        </div>
                        
                        <div class="final-total">
                            <span class="final-label">السعر النهائي:</span>
                            <span class="final-value"><span id="finalPrice">0</span> ريال</span>
                        </div>
                    </div>
                </div>

                <!-- أزرار التحكم -->
                <div class="form-actions">
                    <button type="submit" class="btn-action btn-save">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                    <button type="button" class="btn-action btn-clear" onclick="NewWorkManager.clearForm()">
                        <i class="fas fa-eraser"></i> إخلاء الحقول
                    </button>
                    <button type="button" class="btn-action btn-list" onclick="Prosthetics.showProstheticsListModal()">
                        <i class="fas fa-list"></i> قائمة التركيبات
                    </button>
                    <button type="button" class="btn-action btn-close" onclick="NewWorkManager.closeModal()">
                        <i class="fas fa-times"></i> إغلاق
                    </button>
                </div>
            </form>
        </div>
        <style>
        /* النافذة الرئيسية */
        .new-work-modal {
            max-width: 900px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 20px;
            padding: 0;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            margin: auto;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* رأس النافذة */
        .modal-header {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .close-modal-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-modal-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        /* النموذج */
        .new-work-form {
            padding: 30px;
            max-height: 70vh;
            overflow-y: auto;
        }

        /* أقسام النموذج */
        .form-section {
            margin-bottom: 30px;
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .form-section h3 {
            margin: 0 0 20px 0;
            color: #1f2937;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e5e7eb;
        }

        .form-section h3 i {
            color: #3b82f6;
        }

        /* شبكة النموذج */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .form-row {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-row label {
            font-weight: 600;
            color: #374151;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-row label i {
            color: #6b7280;
            width: 16px;
        }

        .form-row input,
        .form-row select {
            padding: 12px 15px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-row input:focus,
        .form-row select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .readonly-input {
            background: #f9fafb !important;
            color: #6b7280;
            cursor: not-allowed;
        }

        /* مخطط الأسنان */
        .teeth-diagram {
            background: linear-gradient(135deg, #f8fafc 0%, #e5e7eb 100%);
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #e5e7eb;
            min-height: 300px;
        }

        .teeth-diagram-container {
            background: linear-gradient(135deg, #f8fafc 0%, #e5e7eb 100%);
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #e5e7eb;
        }

        .teeth-arch {
            margin-bottom: 25px;
        }

        .arch-label {
            text-align: center;
            font-weight: 600;
            font-size: 1.2rem;
            color: #374151;
            margin-bottom: 20px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border-radius: 12px;
            border: 1px solid #93c5fd;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .arch-label i {
            color: #3b82f6;
            font-size: 1rem;
        }

        .teeth-row {
            display: flex;
            justify-content: center;
        }

        .teeth-row.horizontal-layout {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 30px;
            align-items: center;
            justify-items: center;
        }

        .teeth-side {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }

        .side-label {
            font-weight: 600;
            color: #6b7280;
            margin-bottom: 15px;
            font-size: 1rem;
            padding: 8px 16px;
            background: #f3f4f6;
            border-radius: 20px;
            border: 1px solid #e5e7eb;
        }

        .teeth-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 10px;
            justify-items: center;
            width: 100%;
            max-width: 400px;
        }

        .left-side .teeth-grid {
            direction: ltr;
        }

        .tooth-container {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tooth-container:hover {
            transform: translateY(-3px);
        }

        .tooth-box {
            width: 50px;
            height: 60px;
            border: 2px solid #d1d5db;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .tooth-container:hover .tooth-box {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            box-shadow: 0 6px 15px rgba(59, 130, 246, 0.3);
        }

        .tooth-container.selected .tooth-box {
            border-color: #10b981;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }

        .tooth-container.selected .tooth-box::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #10b981, #059669, #047857);
            border-radius: 14px;
            z-index: -1;
            animation: selectedGlow 2s ease-in-out infinite alternate;
        }

        @keyframes selectedGlow {
            0% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .tooth-icon {
            font-size: 1.2rem;
            margin-bottom: 4px;
            color: #6b7280;
        }

        .tooth-container:hover .tooth-icon {
            color: #3b82f6;
        }

        .tooth-container.selected .tooth-icon {
            color: white;
        }

        .tooth-number {
            font-weight: 700;
            font-size: 0.8rem;
            color: #374151;
        }

        .tooth-container:hover .tooth-number {
            color: #1e40af;
        }

        .tooth-container.selected .tooth-number {
            color: white;
        }

        .center-separator {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .separator-line-vertical {
            width: 4px;
            height: 100px;
            background: linear-gradient(to bottom, #3b82f6, #1e40af, #3b82f6);
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .jaw-separator {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        .separator-line-horizontal {
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, #3b82f6, #1e40af, #3b82f6);
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .jaw-separator-label {
            position: absolute;
            background: white;
            padding: 8px 12px;
            border-radius: 50%;
            border: 2px solid #3b82f6;
            color: #3b82f6;
            font-size: 0.9rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .selected-teeth-info {
            margin-top: 25px;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 12px;
            border: 1px solid #bae6fd;
        }

        .info-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
        }

        .info-header i {
            color: #0369a1;
            font-size: 1.1rem;
        }

        .info-title {
            font-weight: 600;
            color: #0c4a6e;
            font-size: 1rem;
        }

        .selected-list {
            min-height: 40px;
            padding: 12px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e0f2fe;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            align-items: center;
        }

        .no-selection {
            color: #64748b;
            font-style: italic;
            font-size: 0.9rem;
        }

        .selected-tooth-item {
            background: linear-gradient(135deg, #0369a1 0%, #0284c7 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(3, 105, 161, 0.3);
        }
            align-items: center;
            gap: 20px;
        }

        .teeth-side {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .side-label {
            font-size: 0.9rem;
            color: #6b7280;
            font-weight: 500;
        }

        .teeth-numbers {
            display: flex;
            gap: 5px;
        }

        .modern-tooth {
            width: 40px;
            height: 40px;
            border: 2px solid #d1d5db;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            font-weight: 600;
            color: #374151;
        }

        .modern-tooth:hover {
            border-color: #3b82f6;
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3);
        }

        .modern-tooth.selected {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
            border-color: #1e40af;
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(59, 130, 246, 0.4);
        }

        .separator-line {
            width: 2px;
            height: 40px;
            background: #3b82f6;
            border-radius: 1px;
        }

        .jaw-separator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        .separator-line-horizontal {
            width: 80%;
            height: 3px;
            background: linear-gradient(90deg, transparent, #3b82f6, transparent);
            border-radius: 2px;
        }

        .selected-teeth-info {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border: 1px solid #e5e7eb;
        }

        .info-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 10px;
        }

        .selected-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .selected-tooth-item {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .no-selection {
            color: #6b7280;
            font-style: italic;
        }

        /* ملخص التكلفة */
        .cost-summary {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid #bae6fd;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            background: white;
            border-radius: 10px;
            border: 1px solid #e0f2fe;
        }

        .summary-label {
            font-weight: 500;
            color: #374151;
        }

        .summary-value {
            font-weight: 600;
            color: #0369a1;
        }

        .total-price {
            color: #dc2626 !important;
        }

        .discount-section {
            margin-bottom: 20px;
        }

        .discount-section label {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
            font-weight: 600;
            color: #374151;
        }

        .discount-section input {
            padding: 12px 15px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .final-total {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 1.2rem;
            font-weight: 700;
            box-shadow: 0 5px 15px rgba(34, 197, 94, 0.3);
        }

        /* أزرار التحكم */
        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .btn-action {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 140px;
            justify-content: center;
        }

        .btn-save {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            box-shadow: 0 5px 15px rgba(34, 197, 94, 0.3);
        }

        .btn-clear {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            box-shadow: 0 5px 15px rgba(245, 158, 11, 0.3);
        }

        .btn-list {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
            box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3);
        }

        .btn-close {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
        }

        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .new-work-modal {
                max-width: 95%;
                margin: 10px auto;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .teeth-row {
                flex-direction: column;
                gap: 15px;
            }
            
            .form-actions {
                flex-direction: column;
            }
            
            .btn-action {
                width: 100%;
            }
        }
        </style>
        `;
    }
}

window.NewWorkManager = NewWorkManager;
