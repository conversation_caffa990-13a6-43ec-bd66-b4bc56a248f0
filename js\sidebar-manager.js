// إدارة الشريط الجانبي
class SidebarManager {
    static isCollapsed = false;
    static isInitialized = false;

    // تهيئة الشريط الجانبي
    static init() {
        if (this.isInitialized) return;

        this.setupEventListeners();
        this.restoreSidebarState();
        this.isInitialized = true;

        console.log('تم تهيئة مدير الشريط الجانبي');
    }

    // إعداد مستمعي الأحداث
    static setupEventListeners() {
        // زر إخفاء/إظهار الشريط الجانبي
        const sidebarToggleBtn = document.getElementById('sidebarToggleBtn');
        if (sidebarToggleBtn) {
            sidebarToggleBtn.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        // زر الشريط الجانبي في الشريط العلوي (للجوال)
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                this.toggleSidebarMobile();
            });
        }

        // إغلاق الشريط الجانبي عند النقر خارجه (للجوال)
        document.addEventListener('click', (e) => {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            
            if (window.innerWidth <= 768 && sidebar && sidebar.classList.contains('open')) {
                if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                    this.closeSidebarMobile();
                }
            }
        });

        // مراقبة تغيير حجم الشاشة
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    // إخفاء/إظهار الشريط الجانبي
    static toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        const mainApp = document.getElementById('mainApp');

        if (!sidebar || !mainContent) return;

        sidebar.classList.toggle('collapsed');
        this.isCollapsed = sidebar.classList.contains('collapsed');

        // تحديث هامش المحتوى الرئيسي فوراً
        if (this.isCollapsed) {
            mainContent.style.marginRight = '60px';
            // إضافة فئة للتطبيق الرئيسي للتحكم الشامل
            if (mainApp) mainApp.classList.add('sidebar-collapsed');
        } else {
            mainContent.style.marginRight = '280px';
            // إزالة فئة التطبيق الرئيسي
            if (mainApp) mainApp.classList.remove('sidebar-collapsed');
        }

        // فرض إعادة حساب التخطيط
        this.forceLayoutUpdate();

        // حفظ حالة الشريط الجانبي
        localStorage.setItem('sidebarCollapsed', this.isCollapsed);

        // إرسال حدث تحديث الشريط الجانبي
        this.dispatchSidebarEvent();

        console.log(`تم ${this.isCollapsed ? 'إخفاء' : 'إظهار'} الشريط الجانبي`);
        console.log(`هامش المحتوى الجديد: ${mainContent.style.marginRight}`);
    }

    // إخفاء/إظهار الشريط الجانبي للجوال
    static toggleSidebarMobile() {
        const sidebar = document.getElementById('sidebar');
        if (!sidebar) return;

        sidebar.classList.toggle('open');
        console.log(`تم ${sidebar.classList.contains('open') ? 'فتح' : 'إغلاق'} الشريط الجانبي للجوال`);
    }

    // إغلاق الشريط الجانبي للجوال
    static closeSidebarMobile() {
        const sidebar = document.getElementById('sidebar');
        if (!sidebar) return;

        sidebar.classList.remove('open');
        console.log('تم إغلاق الشريط الجانبي للجوال');
    }

    // استعادة حالة الشريط الجانبي
    static restoreSidebarState() {
        const savedState = localStorage.getItem('sidebarCollapsed');
        const isCollapsed = savedState === 'true';

        if (isCollapsed) {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const mainApp = document.getElementById('mainApp');

            if (sidebar && mainContent) {
                sidebar.classList.add('collapsed');
                mainContent.style.marginRight = '60px';
                if (mainApp) mainApp.classList.add('sidebar-collapsed');
                this.isCollapsed = true;

                // فرض إعادة حساب التخطيط
                mainContent.offsetHeight;

                console.log('تم استعادة حالة الشريط الجانبي المطوي');
                console.log(`هامش المحتوى المستعاد: ${mainContent.style.marginRight}`);
            }
        }
    }

    // معالجة تغيير حجم الشاشة
    static handleResize() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        const mainApp = document.getElementById('mainApp');

        if (!sidebar || !mainContent) return;

        // للشاشات الكبيرة
        if (window.innerWidth > 768) {
            sidebar.classList.remove('open');

            // تطبيق حالة الطي المحفوظة
            if (this.isCollapsed) {
                sidebar.classList.add('collapsed');
                mainContent.style.marginRight = '60px';
                mainContent.style.width = 'calc(100% - 60px)';
                if (mainApp) mainApp.classList.add('sidebar-collapsed');
            } else {
                sidebar.classList.remove('collapsed');
                mainContent.style.marginRight = '280px';
                mainContent.style.width = 'calc(100% - 280px)';
                if (mainApp) mainApp.classList.remove('sidebar-collapsed');
            }
        } else {
            // للشاشات الصغيرة
            mainContent.style.marginRight = '0';
            mainContent.style.width = '100%';
            if (mainApp) mainApp.classList.remove('sidebar-collapsed');
        }

        // فرض تحديث التخطيط
        this.forceLayoutUpdate();
    }

    // إرسال حدث تحديث الشريط الجانبي
    static dispatchSidebarEvent() {
        const event = new CustomEvent('sidebarToggled', {
            detail: {
                isCollapsed: this.isCollapsed,
                width: this.isCollapsed ? '60px' : '280px'
            }
        });
        document.dispatchEvent(event);
    }

    // الحصول على حالة الشريط الجانبي
    static getState() {
        return {
            isCollapsed: this.isCollapsed,
            width: this.isCollapsed ? 60 : 280,
            isInitialized: this.isInitialized
        };
    }

    // تعيين حالة الشريط الجانبي برمجياً
    static setState(collapsed) {
        if (collapsed !== this.isCollapsed) {
            this.toggleSidebar();
        }
    }

    // فرض تحديث التخطيط
    static forceLayoutUpdate() {
        const mainContent = document.getElementById('mainContent');
        const sidebar = document.getElementById('sidebar');

        if (mainContent && sidebar) {
            // فرض إعادة حساب التخطيط
            mainContent.offsetHeight;
            sidebar.offsetWidth;

            // تحديث إضافي بعد تأخير قصير
            setTimeout(() => {
                if (this.isCollapsed) {
                    mainContent.style.marginRight = '60px';
                    mainContent.style.width = 'calc(100% - 60px)';
                } else {
                    mainContent.style.marginRight = '280px';
                    mainContent.style.width = 'calc(100% - 280px)';
                }

                // إرسال حدث resize للنوافذ التي تحتاج إعادة حساب
                window.dispatchEvent(new Event('resize'));
            }, 50);
        }
    }

    // إعادة تعيين الشريط الجانبي
    static reset() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        const mainApp = document.getElementById('mainApp');

        if (sidebar && mainContent) {
            sidebar.classList.remove('collapsed', 'open');
            mainContent.style.marginRight = '280px';
            mainContent.style.width = 'calc(100% - 280px)';
            if (mainApp) mainApp.classList.remove('sidebar-collapsed');
            this.isCollapsed = false;

            localStorage.removeItem('sidebarCollapsed');
            this.dispatchSidebarEvent();
            this.forceLayoutUpdate();

            console.log('تم إعادة تعيين الشريط الجانبي');
        }
    }
}

// تهيئة تلقائية عند تحميل الصفحة (تم نقلها إلى App.init)
// document.addEventListener('DOMContentLoaded', () => {
//     SidebarManager.init();
// });

// تصدير للاستخدام العام
window.SidebarManager = SidebarManager;
